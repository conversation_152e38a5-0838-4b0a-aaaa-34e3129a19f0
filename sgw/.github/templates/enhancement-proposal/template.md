# EP-PNNNN: Your short, descriptive title

<!--
This is the title of your EP. Keep it short, simple, and descriptive. A good
title can help communicate what the EP is and should be considered as part of
any review.
-->

<!--
A table of contents is helpful for quickly jumping to sections of a EP and for
highlighting any additional information provided beyond the standard EP
template.

To generate TOC and update it easily, install markdown-all-in-one extension
https://marketplace.visualstudio.com/items?itemName=yzhang.markdown-all-in-one.

- As a rule of thumb, set the TOC levels to 2..4 for the extension setting
-->

- [Summary](#summary)
- [Motivation](#motivation)
	- [Goals](#goals)
	- [Non-Goals](#non-goals)
- [Proposal](#proposal)
	- [Acceptance criteria (Optional)](#acceptance-criteria-optional)
		- [AC 1](#ac-1)
		- [AC 2](#ac-2)
	- [Notes/Constraints/Caveats (Optional)](#notesconstraintscaveats-optional)
	- [Risks and Mitigation](#risks-and-mitigation)
- [Design Details](#design-details)
- [Alternatives](#alternatives)
- [Infrastructure Needed (Optional)](#infrastructure-needed-optional)

## Summary

<!--
In this section, you will briefly explain what the proposal is about, without
going into too much technical details. A reader of this section should be able
to go "Oh, I know **what** you are trying to achieve, though i do not know
exactly **how** you are going to do that, or **how** does this impact the rest
of the system. But hey, at least i know where you're going on this so that i
can make sense of the rest of the EP"

Both in this section and below, follow the guidelines of the documentation
style guide
https://github.com/kubernetes/community/blob/master/contributors/guide/style-guide.md
where it make sense for us. In particular, wrap lines to a reasonable length
(80 characters) using a repository co-pilot prompt: `/80-char-markdown-formatter`
-->

## Motivation

<!--
This section is for explicitly listing the motivation. Describe why the change
is important/necessary and the benefits to users. Usually this is for you to
list down more context of what is existing behavior and what's the need for
the change
-->

### Goals

<!--
List the specific goals of the EP. What is it trying to achieve? How will we
know that this has succeeded?
-->

### Non-Goals

<!--
What is out of scope for this EP? Listing non-goals helps to focus discussion
and make progress.
-->

## Proposal

<!--
This is where we get down to the specifics of what the proposal actually is.
This should have enough detail that reviewers can understand exactly what
you're proposing, but should not include things like API designs or
implementation. What is the desired outcome and how do we measure success?.
The "Design Details" section below is for the real nitty-gritty.
-->

### Acceptance criteria (Optional)

<!--
Detail the things that people will be able to do if this EP is implemented.
Include as much detail as possible so that people can understand the "how" of
the system. The goal here is to make this feel real for users without getting
bogged down.
-->

#### AC 1

#### AC 2

### Notes/Constraints/Caveats (Optional)

<!--
What are the caveats to the proposal?
What are some important details that didn't come across above?
Go in to as much detail as necessary here.
-->

### Risks and Mitigation

<!--
What are the risks of this proposal, and how do we mitigate? Think broadly.
For example, consider security, UX and performance risk.
-->

## Design Details

<!--
This section should contain enough information that the specifics of your
change are understandable. This may include API specs (though not always
required) or even code snippets. If there's any ambiguity about HOW your
proposal will be implemented, this is the place to discuss them.
-->

## Alternatives

<!--
What other approaches did you consider, and why did you rule them out? These
do not need to be as detailed as the proposal, but should include enough
information to express the idea and why it was not acceptable.
-->

## Infrastructure Needed (Optional)

<!--
Use this section if you need things from the project. Example you need an S3
bucket, you need SQS.
-->

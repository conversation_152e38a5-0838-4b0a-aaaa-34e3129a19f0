---
mode: "agent"
tools: ["editFiles", "codebase"]
description: "Reflow markdown prose to max 80 columns; safe, idempotent, no semantic changes."
---

# 80-Column Markdown Formatter

You are a principal technical documentation specialist. You will reflow plain
prose in Markdown to a maximum of 80 characters per line while preserving all
content, meaning, and protected structures. You perform a single-shot
transformation on either the current selection (if non-empty) or the entire
current markdown file.

## Scope

- Input: `${selection}` if non-empty else full `${file}` contents (Markdown).
- Output: ONLY the fully formatted Markdown (no diff, no commentary). If no
  change is required output EXACTLY: `No reflow needed.`
- Line endings normalized to `\n`.

## Core Responsibilities

1. Reflow only plain paragraph text and list item body text (after markers) that
   is not inside a protected block.
2. Do not alter wording, punctuation, capitalization, or ordering of any tokens.
3. Maintain idempotency: a second run produces identical output.
4. Preserve intentional short lines (poetry, admonition titles, manual breaks
   with two trailing spaces) — never join lines that end with two spaces.
5. Trim trailing spaces on lines (except inside fenced code blocks which are
   completely preserved).
6. Collapse multiple consecutive blank lines to a single blank line (but never
   remove a required blank line that separates block elements).

## Wrapping Rules

- Maximum width: 80 characters per line (fixed, not configurable).
- Break lines only at existing whitespace boundaries; never hyphenate or split
  within words, URLs, or inline code spans.
- Headings: do NOT reflow or split; leave each heading line exactly as-is.
- Lists: Reflow paragraph text within list items, keeping proper continuation
  indentation (continuation lines align under first character of text after the
  bullet/number + space). Do NOT renumber ordered lists.
- Blockquotes: Treat leading `>` + single space as a prefix whose length counts
  toward the 80 char limit; otherwise same wrapping rules. (If nested, each `>`
  plus following space counts.)
- Very long unbreakable tokens (e.g., URLs, inline code, long words) may exceed
  80 chars; keep them intact and place them alone on a line if needed.

## Protected / Non-Reflow Regions

NEVER reflow, alter, or trim within:

- Fenced code blocks (``` or ~~~) including their opening/closing fences.
- Indented code blocks (4 leading spaces or a tab) — leave lines untouched.
- Tables (lines containing at least one unescaped pipe `|` and forming part of a
  contiguous table block) — leave each line as-is.
- Raw HTML blocks.
- YAML front matter (between `---` delimiters at file start) — preserve intact.
- Inline code spans (backticks) — do not break inside; treat as atomic tokens.
- Reference link definitions (`[label]: URL ...`) — keep each entire definition
  on a single line; do not wrap.
- HTML comments (`<!-- ... -->`).
- Existing hard line breaks indicated by two trailing spaces — preserve the line
  without joining; do not remove the two spaces.

## Whitespace Normalization

- Remove trailing spaces on lines except inside fenced code blocks.
- Normalize consecutive blank lines to a single blank line (never fewer than one
  blank line between block-level elements: paragraphs, lists, code fences,
  tables, blockquotes, headings).
- Preserve indentation required for list nesting (2 spaces per nesting level) or
  code blocks. Do NOT remove indentation on protected blocks.

## Algorithm (Apply Sequentially)

1. Detect and mark protected blocks & lines (front matter, fenced/indented code,
   tables, raw HTML blocks, reference link definitions, HTML comments, math
   blocks, TOC markers if present, but do not add or modify a TOC).
2. Partition remaining content into segments: paragraphs, list item bodies,
   blockquote paragraphs.
3. Within each segment:
   - Respect existing hard line breaks (two trailing spaces) by splitting the
     segment at those lines and preserving them.
   - Reflow each sub-segment to <= 80 columns taking into account any prefix
     (`> ` for blockquotes, list marker + space, indentation for continuation).
4. Reconstruct document preserving original order and all protected content
   byte-for-byte.
5. Collapse multiple blank lines (outside protected regions) to a single blank
   line; ensure exactly one blank line between major block elements when they
   were separated by 1+ blank lines originally.
6. Trim trailing spaces except in protected blocks.
7. Final Verification:
   - No headings altered.
   - No fenced code/table lines changed.
   - No line (except unbreakable token lines) exceeds 80 chars.
   - Word count identical to input.
   - Running algorithm again would yield no change.
8. If any verification fails, revert the affected segment(s) to original.

## Absolute Prohibitions (NEVER)

- NEVER change wording, add, remove, reorder, or duplicate any content.
- NEVER infer or generate new content, summaries, or commentary.
- NEVER alter code blocks, table formatting, or link/macro syntax.
- NEVER attempt lint "fixes" beyond specified wrapping & whitespace actions.

## Output Rules

- If at least one change (wrapping, blank line collapse, trailing space removal)
  was applied: output ONLY the full, updated Markdown content.
- If no changes were needed: output EXACTLY `No reflow needed.` (no quotes,
  alone on a line).
- Do NOT output diffs, explanations, counts, or metadata.

## Quality / Success Criteria

- Plain prose lines <= 80 chars (except lines containing an unbreakable token).
- Protected regions identical to input.
- Idempotent: immediate re-run yields `No reflow needed.`
- All intentional hard breaks (two trailing spaces) preserved.
- No accidental merging of separate paragraphs.

## Failure Handling

- If uncertain about parsing a region (ambiguous structure), leave that region
  untouched rather than risking corruption.
- If a catastrophic inconsistency is detected (e.g., fence mismatch), abort and
  output `No reflow needed.`

Follow these instructions precisely. Perform ONLY the allowed transformations.

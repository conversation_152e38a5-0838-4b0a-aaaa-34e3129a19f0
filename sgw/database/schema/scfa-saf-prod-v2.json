{"schemeName": "Student Care Fee Assistance (SCFA)", "schemeCode": "scfa", "bundleSchemeCode": "saf", "schemeDetailsLink": "/schemes/SCFA/student-care-fee-assistance-scfa", "subtitle": "Supported by Ministry of Social and Family Development", "dashboard": {"applicationGuidance": "**Child must attend a [Student Care Centre](https://www.msf.gov.sg/our-services/directories#studenttab) registered with MSF to qualify for the assistance.**\n&nbsp;\nThe application may take 15 mins to complete.\n&nbsp;\nYou may apply for a **renewal** of your SCFA support up to 6 months from now if you meet the eligibility criteria."}, "contacts": [{"faqLink": "https://ask.gov.sg/msf?topic=ComCare&subtopic=Student%20Care%20Fee%20Assistance", "email": "<EMAIL>", "hotlineNumber": "1800 111 2222", "helpExtra": ["Charges apply for mobile calls to 1800 service lines. Calls are free of charge only if made from regular land lines in Singapore."]}], "nextSteps": "Processing takes 4 to 8 weeks after we receive all required documents. You may be contacted by MSF and/or HOMES (a Government System supporting public schemes to conduct means-tests to determine the level of assistance for citizens), for more details or supporting documents.\n&nbsp;\nCheck your email if additional documents are needed and submit them by the deadline stated to keep your application open.", "schema": [{"id": "main", "actionLabel": "Apply for 1 scheme", "descriptionLabel": "Continue to form", "section": [{"id": "profile", "title": "Profile", "subtitle": "Tell us about yourself", "safId": "sect-profile", "safIdx": "5", "member": [{"type": "DECORATOR", "subType": "HEADER", "safId": "personalDetails", "safIdx": "5", "title": "Personal details"}, {"id": "name", "type": "PRESET_FIELD", "subType": "name", "safId": "name", "safIdx": "10", "prefillSource": "myInfo.name"}, {"id": "nric", "type": "PRESET_FIELD", "subType": "nric", "safId": "nric", "safIdx": "15", "prefillSource": "myInfo.nric"}, {"id": "dob", "type": "PRESET_FIELD", "subType": "dob", "safId": "dob", "safIdx": "20", "prefillSource": "myInfo.dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex", "safId": "sex", "safIdx": "25", "prefillSource": "myInfo.sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "safId": "residentialStatus", "safIdx": "30", "prefillSource": "myInfo.residentialStatus"}, {"id": "race", "type": "PRESET_FIELD", "subType": "race", "safId": "race", "safIdx": "35", "prefillSource": "myInfo.race"}, {"id": "countryOfBirth", "type": "PRESET_FIELD", "subType": "countryOfBirth", "safId": "countryOfBirth", "safIdx": "40", "prefillSource": "myInfo.countryOfBirth"}, {"type": "SECTION_CONDITIONAL", "id": "spokenLanguage", "safId": "spokenLanguage", "safIdx": "45", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "language", "title": "Spoken language", "options": {"EL": "English", "MN": "Mandarin", "MY": "Malay", "TM": "Tamil", "XX": "Others (including dialect)"}}, "result": [{"choice": ["XX"], "member": [{"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "others", "safId": "preferredSpokenLanguage", "safIdx": "5", "title": "Tell us your preferred spoken language"}]}]}, {"id": "address", "type": "PRESET_FIELD", "subType": "address", "safId": "address", "safIdx": "50", "prefillSource": "myInfo.address"}, {"id": "mailingAddress", "type": "PRESET_FIELD", "subType": "mailingAddress", "safId": "mailingAddress", "safIdx": "55"}, {"type": "DECORATOR", "subType": "HEADER", "safId": "contactDetails", "safIdx": "60", "title": "Contact details"}, {"id": "mobileNumber", "type": "PRESET_FIELD", "subType": "mobileNumber", "safId": "mobileNumber", "safIdx": "65", "prefillSource": "myInfo.mobileNumber", "editable": true}, {"id": "homeNumber", "type": "PRESET_FIELD", "subType": "homeNumber", "safId": "homeNumber", "safIdx": "70", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email", "safId": "email", "safIdx": "75", "prefillSource": "myInfo.email", "template": "application", "editable": true}]}, {"id": "household", "title": "Household", "safId": "sect-household", "safIdx": "15", "member": [{"type": "SECTION_CONDITIONAL", "id": "marital", "safId": "marital", "safIdx": "5", "selector": {"type": "PRESET_FIELD", "subType": "maritalStatus", "title": "Marital status", "id": "maritalStatus", "prefillSource": "myInfo.maritalStatus", "editable": true}, "result": [{"choice": ["4"], "member": [{"type": "CUSTOM_FIELD", "subType": "DATE", "safId": "dateOfSeparation", "safIdx": "5", "id": "dateOfSeparation", "title": "Date of separation"}]}, {"choice": ["2"], "member": [{"type": "DECORATOR", "subType": "HEADER", "safId": "spouseDetails", "safIdx": "5", "title": "Spouse details"}, {"type": "PRESET_FIELD", "subType": "name", "safId": "<PERSON><PERSON><PERSON>", "safIdx": "10", "id": "<PERSON><PERSON><PERSON>"}, {"type": "PRESET_FIELD", "subType": "nric", "safId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "safIdx": "15", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consent": true, "consenterInfo": {"name": "<PERSON><PERSON><PERSON>"}}, {"type": "PRESET_FIELD", "subType": "dob", "safId": "spouse<PERSON><PERSON>", "safIdx": "20", "id": "spouse<PERSON><PERSON>"}, {"type": "PRESET_FIELD", "id": "spouseSex", "subType": "sex", "safId": "spouseSex", "safIdx": "25"}, {"type": "PRESET_FIELD", "subType": "residentialStatus", "safId": "spouseResidentialStatus", "safIdx": "30", "id": "spouseResidentialStatus"}, {"type": "PRESET_FIELD", "subType": "email", "safId": "spouseEmail", "safIdx": "35", "id": "spouseEmail", "optional": true}]}]}, {"type": "DECORATOR", "subType": "HEADER", "safId": "householdMembers", "safIdx": "10", "title": "Household members"}, {"type": "SECTION_CONDITIONAL", "id": "cohabitation", "safId": "cohabitation", "safIdx": "15", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "cohabitationStatus", "title": "Do you live with any household member?", "description": ["This refers to family members who are living in the same registered address (e.g. parents, children). You are not required to provide your spouse details (if any) again."], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "familyMember", "safId": "familyMember", "safIdx": "5", "title": "How many household members (apart from spouse) live with you?", "maxGroup": 15, "header": "Household member details", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "familyMemberDetails", "title": "Household member", "member": [{"id": "family", "safId": "family", "safIdx": "5", "type": "PRESET_FIELD", "consent": true, "subType": "family"}, {"type": "PRESET_FIELD", "subType": "residentialStatus", "safId": "residentialStatus", "safIdx": "10", "id": "residentialStatus"}, {"type": "PRESET_FIELD", "subType": "email", "safId": "email", "safIdx": "15", "id": "email", "optional": true}, {"type": "PRESET_FIELD", "subType": "relationshipType", "safId": "relationshipType", "safIdx": "20", "id": "relationshipType", "title": "Relationship to applicant", "excludedOptions": ["REL001"]}, {"id": "memberMinorConsent", "safId": "memberMinorConsent", "safIdx": "25", "type": "GLOBAL_GROUP_CONDITIONAL", "result": [{"choice": [{"type": "value", "sourcePath": "computedAge", "operator": "lessThan", "value": "21"}], "member": [{"id": "consent", "safId": "consent", "safIdx": "5", "type": "CUSTOM_FIELD", "subType": "TNC", "title": "Consent / declaration for minors", "acknowledge": "I acknowledge and consent to the terms, on behalf of this minor", "content": [{"header": "Consent for collection, use and disclosure of personal information for the application or renewal of application for the subsidy and/or the Start Up Grant", "indent": [{"paragraph": "I am the parent/legal guardian of the Child who is under 21 years of age."}, {"indent": [{"paragraph": "to verify my, my Child and Family’s identity and relationship for the Subsidy and/or the Start Up Grant (“the Subsidies”) and other Services or Scheme;"}, {"paragraph": "to determine my, my Child and Family’s eligibility for the Subsidies and other Services or Scheme;"}, {"paragraph": "to provide me, my Child and Family with both or any of the Subsidies and other Services or Scheme; and"}, {"paragraph": "for data analysis, evaluation and policy-making, for the Subsidies and other Services or Scheme."}], "paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:"}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information, to the extent permitted by the Singapore Public Agencies and Participating Organisations, for the purposes stated in Paragraph 2. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that my personal information will not be shared with non-participating agencies and organisations."}, {"paragraph": "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}, {"paragraph": "This consent shall be governed by and construed in accordance with the laws of the Republic of Singapore."}]}, {"header": "Declaration", "indent": [{"paragraph": "I, the undersigned, declare that I have read and understood the content in this Application Form. I confirm that the information that I have provided is true and correct and I furnish the information knowing that I may be liable to criminal prosecution if I have stated any information that I know to be false or not believe to be true."}, {"paragraph": "In the event my application is successful and my Child receives any of the Subsidies which I am applying for, I hereby acknowledge that I may also be liable to make full repayment to the Government of the Subsidies which were provided, should I be found to have provided false or inaccurate information in this form."}]}, {"header": "Other terms", "indent": [{"indent": [{"paragraph": "It shall be my responsibility to stay employed (i.e. to be engaged under a contract or service and receive a salary) to continue to enjoy the Subsidies for my Child. If I am unemployed and intend to seek employment, the onus is on me to actively seek employment."}, {"paragraph": "(only applicable to applications for the Start Up Grant) The Start Up Grant shall only be given once to my Child, and any subsequent applications shall be assessed and granted only in MSF’s sole discretion."}, {"paragraph": "In order to continue enjoying the relevant monthly Subsidy, I must ensure that my Child attends at least 30% (June and December) and 50% (Other Calendar months) of the number of days in which the SCC operates per month. My Child must be present at the centre for at least 3 hours in order to be considered present for the day. If my Child does not meet the minimum attendance rate, the Subsidy paid for the relevant month may be refunded to MSF and I am liable to pay the full SCC monthly fee."}, {"paragraph": " I shall provide the SCC with a one-month notice before withdrawing my Child from the SCC."}], "paragraph": "I understand and agree to the following:"}]}]}]}, {"choice": [{"type": "value", "sourcePath": "computedAge", "operator": "greaterThanInclusive", "value": "21"}], "member": [{"type": "DECORATOR", "safId": "consentInfoblock", "safIdx": "5", "title": ["**Household members’ consent is required for the application.**\nMore details on how your household members can provide consent will be given after the application is submitted."], "subType": "INFO_BLOCK"}]}], "selector": {"id": "computedAge", "type": "CUSTOM_FIELD", "subType": "HIDDEN", "globalAction": {"type": "calculateMultiValueAge", "dependsOn": ["household.cohabitation.familyMember.familyMemberDetails"], "relativeIds": ["family.member<PERSON>ob"]}}}, {"type": "GROUP_CONDITIONAL", "id": "scfaBeneficiary", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "isBeneficiary", "title": "Are you applying SCFA for this household member?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "PRESET_FIELD", "subType": "school", "id": "childSchoolName"}, {"type": "GROUP_CONDITIONAL", "id": "childScc", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "sccType", "title": "Student care centre (SCC) type", "description": ["**School-based SCC**: only accepts students from primary schools they are located in.", "**Community-based SCC**: located in community, outside school compounds."], "options": {"SCC1": "School-based SCC", "SCC2": "Community-based SCC"}}, "result": [{"choice": ["SCC1"], "member": [{"type": "CUSTOM_FIELD", "subType": "DYNAMIC_DROPDOWN", "id": "sccCodeSchool", "title": "School-based SCC", "url": "https://a1684weme9-vpce-0fe06e989c650da0f.execute-api.ap-southeast-1.amazonaws.com/prd/apigw.ssnet.gov.sg/api/v1/sgwappgen/getSccNameAndAddress?typeOfScc=SCC1"}]}, {"choice": ["SCC2"], "member": [{"type": "CUSTOM_FIELD", "subType": "DYNAMIC_DROPDOWN", "id": "sccCodeCommunity", "title": "Community-based SCC", "url": "https://a1684weme9-vpce-0fe06e989c650da0f.execute-api.ap-southeast-1.amazonaws.com/prd/apigw.ssnet.gov.sg/api/v1/sgwappgen/getSccNameAndAddress?typeOfScc=SCC2"}]}]}, {"type": "CUSTOM_FIELD", "subType": "DATE_MONTH", "id": "childSubsidyStartMonth", "title": "Request for subsidy start month", "description": ["You may apply for a renewal of your SCFA support within 4 months of expiry if you meet the eligibility criteria."], "minMonthRange": 5, "maxMonthRange": 4}, {"type": "GROUP_CONDITIONAL", "id": "relationship", "selector": {"type": "PRESET_FIELD", "subType": "relationshipType", "id": "childRelationshipType", "title": "Relationship to beneficiary", "description": ["Beneficiary refers to the child you are applying for."], "allowedOptions": ["REL002", "REL205", "REL206", "REL009"]}, "result": [{"choice": ["REL002", "REL205", "REL206"], "member": [{"type": "CUSTOM_FIELD", "subType": "SINGLE_CHECK", "id": "declaration", "title": "I declare that I am now living with my child(ren) in the same address, and I am caring for the child(ren). If there is another parent with shared care and control of the child(ren), I have sought his/her consent for the SCFA application."}, {"type": "PRESET_FIELD", "subType": "relationshipType", "id": "spouseChildRelationshipType", "title": "Spouse’s relationship to beneficiary (if applicable)", "allowedOptions": ["REL002", "REL205", "REL206"], "description": ["Beneficiary refers to the child you are applying for."], "optional": true}]}, {"choice": ["REL009"], "member": [{"type": "CUSTOM_FIELD", "subType": "FILE_UPLOAD", "id": "SD5", "title": "Guardianship documents", "documents": [{"paragraph": "**Legal guardian**:", "indent": [{"paragraph": "Guardianship paper"}]}, {"paragraph": "**Non-legal guardian**:", "indent": [{"paragraph": "Any of the following documents explaining the need to be the non-legal guardian of child such as:", "indent": ["Parent(s)’ death certificate", "Police report", "Prison letter", "[Annex 6](https://www.msf.gov.sg/docs/default-source/student-care/annex-6-self-declaration-form.pdf): SCFA Self Declaration Form", "Proof that non-legal guardian is also applicant of approved MOE-FAS application for child"]}]}, {"paragraph": "**Foster parent**:", "indent": [{"paragraph": "Letter of recommendation from foster care agencies"}]}]}]}]}]}]}]}}]}]}]}, {"id": "additionalDetails", "safId": "sect-additional-details", "safIdx": "20", "title": "Additional details", "subtitle": "Provide additional details about your application, if relevant, to help us better assess your situation.", "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "comments", "title": "Comments", "optional": true, "maxLength": 300, "description": ["Let us know if you had difficulties providing any document or feedback to help improve the form experience."]}, {"type": "CUSTOM_FIELD", "subType": "FILE_UPLOAD", "id": "SD34", "title": "SCFA - Other supporting documents", "optional": true, "instructions": ["Please upload a copy of:"], "documents": [{"paragraph": "Any **other documents to support this application** (e.g. Enrolment letter provided by the Student Care Centre)"}]}]}, {"id": "terms", "safId": "sect-tnc", "safIdx": "25", "title": "Terms & Conditions", "subtitle": "Please read and acknowledge the terms and conditions", "member": [{"type": "CUSTOM_GROUP", "subType": "TNC", "id": "declarationConsent", "title": "Consent / declaration", "acknowledge": "I acknowledge and consent to the terms above.", "content": [{"header": "Consent for collection, use and disclosure of personal information for the application or renewal of application for the subsidy and/or the Start Up Grant", "indent": [{"paragraph": "I am the parent/legal guardian of the Child who is under 21 years of age."}, {"paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:", "indent": [{"paragraph": "to verify my, my Child and Family’s identity and relationship for the Subsidy and/or the Start Up Grant (“the Subsidies”) and other Services or Scheme;"}, {"paragraph": "to determine my, my Child and Family’s eligibility for the Subsidies and other Services or Scheme;"}, {"paragraph": "to provide me, my Child and Family with both or any of the Subsidies and other Services or Scheme; and"}, {"paragraph": "for data analysis, evaluation and policy-making, for the Subsidies and other Services or Scheme."}]}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information, to the extent permitted by the Singapore Public Agencies and Participating Organisations, for the purposes stated in Paragraph 2. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that my personal information will not be shared with non-participating agencies and organisations."}, {"paragraph": "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}, {"paragraph": "This consent shall be governed by and construed in accordance with the laws of the Republic of Singapore."}]}, {"header": "Declaration", "indent": [{"paragraph": "I, the undersigned, declare that I have read and understood the content in this Application Form. I confirm that the information that I have provided is true and correct and I furnish the information knowing that I may be liable to criminal prosecution if I have stated any information that I know to be false or not believe to be true."}, {"paragraph": "In the event my application is successful and my Child receives any of the Subsidies which I am applying for, I hereby acknowledge that I may also be liable to make full repayment to the Government of the Subsidies which were provided, should I be found to have provided false or inaccurate information in this form."}]}, {"header": "Other terms", "indent": [{"paragraph": "I understand and agree to the following:", "indent": [{"paragraph": "It shall be my responsibility to stay employed (i.e. to be engaged under a contract or service and receive a salary) to continue to enjoy the Subsidies for my Child. If I am unemployed and intend to seek employment, the onus is on me to actively seek employment."}, {"paragraph": "(only applicable to applications for the Start Up Grant) The Start Up Grant shall only be given once to my Child, and any subsequent applications shall be assessed and granted only in MSF’s sole discretion."}, {"paragraph": "In order to continue enjoying the relevant monthly Subsidy, I must ensure that my Child attends at least 30% (June and December) and 50% (Other Calendar months) of the number of days in which the SCC operates per month. My Child must be present at the centre for at least 3 hours in order to be considered present for the day. If my Child does not meet the minimum attendance rate, the Subsidy paid for the relevant month may be refunded to MSF and I am liable to pay the full SCC monthly fee."}, {"paragraph": "I shall provide the SCC with a one-month notice before withdrawing my Child from the SCC."}]}]}]}]}]}], "consentDocumentsSchema": {"section": [{"id": "outstandingItems", "title": "Documents", "subtitle": "Upload relevant documents to help us better assess your situation", "member": [{"type": "DECORATOR", "subType": "INFO_BLOCK", "title": ["**Draft will not be saved**\nPlease submit the form to avoid losing any details."]}, {"type": "SECTION_CONDITIONAL", "id": "SD23", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "haveSingpass", "title": "Family members’ consent is required for the application.", "description": ["**Consent is not required for family members who:**\n(1) Require consent to be provided on behalf by a <PERSON><PERSON>/Deputy.\n(2) Have already provided consent in an earlier application, or\n(3) Are minors as you have consented for on their behalf in this application.", "**For family members with Singpass:**\n**Self-consent**\n(1) Log in via [SupportGoWhere’s website](https://supportgowhere.life.gov.sg/).\n(2) On My Applications page, select ‘To-do’ tab and find the request for consent.", "**Can all of your family members provide consent with <PERSON><PERSON>?**"], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "DECORATOR", "subType": "INFO_BLOCK", "title": ["Based on your response no further details are required. Please share the above instructions for **Self-consent** with your family members."]}]}, {"choice": ["1"], "member": [{"type": "DECORATOR", "subType": "HEADER", "title": "Other consent methods"}, {"type": "MULTI_VALUE", "id": "memberConsentRecords", "title": "Number of family members using other consent methods", "maxGroup": 10, "header": "Consent details", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "memberConsent", "title": "Family member", "member": [{"type": "PRESET_FIELD", "subType": "consenter", "id": "personalDetails"}, {"type": "DECORATOR", "subType": "INFO_BLOCK", "title": ["If their details are inaccurate, please use the PDF form method below and fill in the correct details."]}, {"type": "GROUP_CONDITIONAL", "id": "consent", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "method", "title": "Way to provide consent", "options": ["E-signature (recommended): Family member signs the form on your device", "PDF form: Download PDF form and upload a signed copy"]}, "result": [{"choice": ["0"], "member": [{"type": "PRESET_FIELD", "subType": "msfOmnibusConsent", "id": "esig", "priorityRelativeId": ["../personalDetails.name", "../personalDetails.nric"]}, {"type": "CUSTOM_FIELD", "subType": "SINGLE_CHECK", "id": "witnessDeclaration", "title": "I, main applicant, will act as a witness for my family members’ consent."}]}, {"choice": ["1"], "member": [{"id": "manual", "type": "CUSTOM_FIELD", "title": "For family member: Omnibus consent form", "subType": "FILE_UPLOAD", "instructions": ["Please upload a copy of:"], "documents": [{"paragraph": "[Consent form](https://go.gov.sg/householdconsent): Omnibus consent form", "indent": [{"paragraph": "Please ensure that the signature and other details are shown clearly."}]}]}]}]}]}}]}]}]}]}, "outstandingDocumentsSchema": {"section": [{"id": "outstandingItems", "title": "Documents", "member": [{"type": "DECORATOR", "subType": "INFO_BLOCK", "title": ["**Draft will not be saved**\nPlease submit the form to avoid losing any details."]}, {"id": "SD1", "type": "CUSTOM_FIELD", "title": "Letter of recommendation", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "**Letter of recommendation** from your social worker"}]}, {"id": "SD4", "type": "CUSTOM_FIELD", "title": "Adoption paper", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "**Adoption paper/proof** that you are taking care of the child"}]}, {"id": "SD5", "type": "CUSTOM_FIELD", "title": "Guardianship documents", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "**Guardianship paper**"}]}, {"id": "SD6", "type": "CUSTOM_FIELD", "title": "Non-legal guardianship documents", "subType": "FILE_UPLOAD", "documents": [{"indent": [{"paragraph": "Parent(s)’ death certificate"}, {"paragraph": "Police report"}, {"paragraph": "Prison letter"}, {"paragraph": "[Annex 6](https://www.msf.gov.sg/docs/default-source/student-care/annex-6-self-declaration-form.pdf): SCFA Self Declaration Form"}, {"paragraph": "Proof that non-legal guardian is also applicant of approved MOE-FAS application for child"}], "paragraph": "Any of the following **documents explaining the need to be the non-legal guardian** of child such as:"}]}, {"id": "SD7", "type": "CUSTOM_FIELD", "title": "Letter of recommendation", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "**Letter of recommendation** from foster care agencies"}]}, {"id": "SD8", "type": "CUSTOM_FIELD", "title": "Custody papers", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Custody papers with clause stating who has “care and control” of child from previous marriage"}], "instructions": ["If you are **married with child from previous marriage,** please upload a copy of:"]}, {"id": "SD12", "type": "CUSTOM_FIELD", "title": "Divorce proceedings documents", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Supporting Documents (e.g. Interim Judgement - Divorce)"}], "instructions": ["If you are **in process of divorce** please upload a copy of:"]}, {"id": "SD13", "type": "CUSTOM_FIELD", "title": "Payslips", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Latest 3 months of **payslips** from date of application"}]}, {"id": "SD14", "type": "CUSTOM_FIELD", "title": "Self declaration form", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "[Annex 6](https://www.msf.gov.sg/docs/default-source/student-care/annex-6-self-declaration-form.pdf): SCFA Self Declaration Form"}]}, {"id": "SD16", "type": "CUSTOM_FIELD", "title": "Commission earnings statements", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Last 12 months of **commission earnings statements** before the date of application (This helps us calculate the monthly gross income, which is based on the average earnings per month over 12 months)"}]}, {"id": "SD17", "type": "CUSTOM_FIELD", "title": "Proof of job search", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "**Screenshot** of your **job seeker profile,** and **submitted job applications** (if any), from one of the following platforms:", "indent": [{"paragraph": "Career Centre under Workforce Singapore (WSG) or Employment and Employability Institute (e2i)"}, {"paragraph": "Private employment agency"}, {"paragraph": "Any other job search portal"}]}, {"paragraph": "**Email correspondence** with company on **submitted job applications**"}], "instructions": ["Please upload **any** of the following documents:"]}, {"id": "SD18", "type": "CUSTOM_FIELD", "title": "Medical Certificate / Medical letter", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Latest **Medical Certificate (MC)/Medical letter** from your doctor"}], "additionalDetails": ["Please ensure that it states the following details, if relevant: (1) Duration period of medical condition, (2) You/your spouse is the caregiver of your family member"]}, {"id": "SD19", "type": "CUSTOM_FIELD", "title": "Medical letter", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "**Medical letter** that states family member’s medical condition and that you/your spouse is the caregiver of your family member"}]}, {"id": "SD20", "type": "CUSTOM_FIELD", "title": "Proof of training", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "**Document to show enrolment** and duration of the training/educational program"}]}, {"id": "SD21", "type": "CUSTOM_FIELD", "title": "Letter of incarceration", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Latest **Letter of Incarceration (LOI)** from prison office"}]}, {"type": "CUSTOM_FIELD", "subType": "FILE_UPLOAD", "id": "SD24", "title": "Entry/Re-entry Permit", "documents": [{"paragraph": "**Entry/Re-entry Permit** if your child is a Permanent Resident"}]}, {"id": "SD34", "type": "CUSTOM_FIELD", "title": "Other supporting documents", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Any **other documents to support this application** (e.g. Enrolment letter provided by the Student Care Centre)"}]}, {"type": "SECTION_CONDITIONAL", "id": "SD23", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "haveSingpass", "title": "Family members’ consent is required for the application.", "description": ["**Consent is not required for family members who:**\n(1) Require consent to be provided on behalf by a <PERSON><PERSON>/Deputy.\n(2) Have already provided consent in an earlier application, or\n(3) Are minors as you have consented for on their behalf in this application.", "**For family members with Singpass:**\n**Self-consent**\n(1) Log in via [SupportGoWhere’s website](https://supportgowhere.life.gov.sg/).\n(2) On My Applications page, select ‘To-do’ tab and find the request for consent.", "**Can all of your family members provide consent with <PERSON><PERSON>?**"], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "DECORATOR", "subType": "INFO_BLOCK", "title": ["Based on your response no further details are required. Please share the above instructions for **Self-consent** with your family members."]}]}, {"choice": ["1"], "member": [{"type": "DECORATOR", "subType": "HEADER", "title": "Other consent methods"}, {"type": "MULTI_VALUE", "id": "memberConsentRecords", "title": "Number of family members using other consent methods", "maxGroup": 10, "header": "Consent details", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "memberConsent", "title": "Family member", "member": [{"type": "PRESET_FIELD", "subType": "consenter", "id": "personalDetails"}, {"type": "DECORATOR", "subType": "INFO_BLOCK", "title": ["If their details are inaccurate, please use the PDF form method below and fill in the correct details."]}, {"type": "GROUP_CONDITIONAL", "id": "consent", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "method", "title": "Way to provide consent", "options": ["E-signature (recommended): Family member signs the form on your device", "PDF form: Download PDF form and upload a signed copy"]}, "result": [{"choice": ["0"], "member": [{"type": "PRESET_FIELD", "subType": "msfOmnibusConsent", "id": "esig", "priorityRelativeId": ["../personalDetails.name", "../personalDetails.nric"]}, {"type": "CUSTOM_FIELD", "subType": "SINGLE_CHECK", "id": "witnessDeclaration", "title": "I, main applicant, will act as a witness for my family members’ consent."}]}, {"choice": ["1"], "member": [{"id": "manual", "type": "CUSTOM_FIELD", "title": "For family member: Omnibus consent form", "subType": "FILE_UPLOAD", "instructions": ["Please upload a copy of:"], "documents": [{"paragraph": "[Consent form](https://go.gov.sg/householdconsent): Omnibus consent form", "indent": [{"paragraph": "Please ensure that the signature and other details are shown clearly."}]}]}]}]}]}}]}]}, {"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "comments", "title": "Comments", "optional": true, "maxLength": 300, "description": ["Let us know if you had difficulties providing any document or feedback to help improve the form experience."]}], "subtitle": "Upload relevant documents to help us better assess your situation"}]}, "consentSchema": {"section": [{"id": "consent", "title": "Consent", "member": [{"type": "DECORATOR", "subType": "INFO_BLOCK", "title": ["If you are not aware of the above application, or not the intended family member, please email [<EMAIL>](mailto:<EMAIL>) or call [1800 111 2222](tel:18001112222). Charges apply for mobile calls to 1800 service lines. Calls are free of charge only if made from regular land lines in Singapore."]}, {"type": "PRESET_FIELD", "subType": "name", "id": "name", "prefillSource": "myInfo.name", "editable": false}, {"type": "PRESET_FIELD", "subType": "nric", "id": "nric", "prefillSource": "myInfo.nric", "editable": false}, {"type": "DECORATOR", "subType": "HEADER", "title": "Contact details"}, {"type": "PRESET_FIELD", "subType": "mobileNumber", "id": "mobileNumber", "prefillSource": "myInfo.mobileNumber", "editable": true}, {"type": "PRESET_FIELD", "subType": "homeNumber", "id": "homeNumber", "optional": true, "prefillSource": "myInfo.homeNumber", "editable": true}, {"type": "PRESET_FIELD", "subType": "email", "template": "consent", "id": "email", "optional": true, "prefillSource": "myInfo.email", "editable": true}, {"type": "CUSTOM_GROUP", "subType": "TNC", "id": "termsConsent", "title": "Consent to data sharing", "acknowledge": "I acknowledge and consent to the terms above.", "content": [{"header": "Consent for collection, use and disclosure of personal information", "indent": [{"paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:", "indent": [{"paragraph": "to verify my and my Family’s identity and relationship for the Services or Scheme;"}, {"paragraph": "to determine my and my Family’s eligibility for the Services or Scheme;"}, {"paragraph": "to provide me and my Family with the Services or Scheme; and"}, {"paragraph": "for data analysis, evaluation and policy-making, for the Services or Scheme."}]}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information for the purposes stated in Paragraph 1 and any other purpose permitted by law. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that if there are any discrepancies in the Personal Information collected, such discrepancies may be reflected to the relevant Singapore Public Agencies, so that they may take the necessary steps to rectify any inaccurate records relating to me."}, {"paragraph": "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}]}]}]}]}}
{"schemeName": "MSF Disability Verification", "schemeCode": "pwdr", "agencyControlled": true, "subtitle": "Supported by Ministry of Social and Family Development (MSF)", "dashboard": {"applicationGuidance": "To help you access disability support and services efficiently, we require a one-time disability verification. Once verified, it is used as proof of disability for relevant support and services.\n&nbsp;\nThis is **NOT** an application for disability support or services. You will need to meet additional eligibility criteria for these (e.g. means-testing) and apply for them separately.\n&nbsp;\nTo get started, please check if verification is necessary by clicking the relevant buttons below.\n&nbsp;\nIf verification is necessary, please note the following:\n&nbsp;\n• **Proceed with the verification application process ONLY when you have a completed [Disability Verification Form (DVF)](https://enablingguide.sg/disability-verification) endorsed by a registered healthcare professional.**\n&nbsp;\n• Due to data sensitivity, we are unable to save a draft for this application process.\n&nbsp;\nIt takes 15-20 mins to complete this submission."}, "contacts": [{"email": "<EMAIL>", "hotlineNumber": "64366635"}], "nextSteps": "Upon the submission of all supporting documents, we will take up to 15 working days to process your verification.", "schema": [{"id": "pwd", "actionLabel": "As a Person with disability", "section": [{"id": "profile", "title": "Profile", "subtitle": "Tell us about yourself", "member": [{"type": "DECORATOR", "title": "Personal details", "subType": "HEADER"}, {"id": "fullName", "type": "PRESET_FIELD", "subType": "name", "prefillSource": "myInfo.name", "title": "Name (as in NRIC)"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric", "prefillSource": "myInfo.nric", "isUnmasked": true, "title": "NRIC"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob", "prefillSource": "myInfo.dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex", "prefillSource": "myInfo.sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "prefillSource": "myInfo.residentialStatus", "excludedOptions": ["A"], "description": ["Only Singapore Citizens or Permanent Residents are eligible."]}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race", "prefillSource": "myInfo.race"}, {"id": "address", "type": "PRESET_FIELD", "subType": "address", "prefillSource": "myInfo.address"}, {"type": "DECORATOR", "title": "Contact details", "subType": "HEADER"}, {"id": "mobile", "type": "PRESET_FIELD", "subType": "mobileNumber", "prefillSource": "myInfo.mobileNumber", "editable": true}, {"id": "otherPhone", "type": "PRESET_FIELD", "subType": "homeNumber", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email", "prefillSource": "myInfo.email", "template": "application", "editable": true}, {"type": "DECORATOR", "title": "Other information", "subType": "HEADER"}, {"type": "CUSTOM_FIELD", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "id": "sh_dcf", "title": "Disability Verification Form (DVF)", "instructions": ["Please upload a copy of:"], "documents": [{"paragraph": "Completed DVF"}], "additionalDetails": ["Please ensure the DVF is endorsed by a registered healthcare professional before proceeding. You can [download the DVF](https://enablingguide.sg/disability-verification) here.", "&nbsp;\n**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"]}]}, {"id": "caregiver<PERSON><PERSON><PERSON>", "title": "Guardian / Caregiver", "subtitle": "Please complete the Profile section before proceeding", "member": [{"type": "SECTION_CONDITIONAL", "id": "caregiver<PERSON><PERSON><PERSON>", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "haveLegalGuardianCaregiver", "title": "Do you have guardians / caregivers (including parents)?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "caregiver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "How many guardians / caregivers (including parents)?", "maxGroup": 11, "header": "Guardian / Caregiver details", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "caregiverGuardianDetails", "title": "Guardian / Caregiver", "member": [{"id": "fullName", "type": "PRESET_FIELD", "subType": "name"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus"}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race"}, {"type": "PRESET_FIELD", "subType": "relationshipTypeV5", "id": "relationship", "title": "Relationship to person with disability", "allowedOptions": ["REL001", "REL002", "REL003", "REL004", "REL006", "REL007", "REL009", "REL209", "REL408", "REL609", "REL507", "REL508", "REL509", "REL805", "REL807", "REL808", "REL010"]}, {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "role", "title": "Role to person with disability", "options": [{"LG": "Parent / Legal guardian"}, {"DD": "<PERSON><PERSON> / Deputy"}, {"CG": "Non-legal guardian"}]}, {"type": "DECORATOR", "subType": "HEADER", "title": "Contact details"}, {"type": "PRESET_FIELD", "subType": "mobileNumber", "id": "mobile"}, {"type": "PRESET_FIELD", "subType": "email", "id": "email"}]}}]}]}]}, {"id": "terms", "title": "Terms & Conditions", "subtitle": "Please read and acknowledge the terms and conditions", "member": [{"type": "CUSTOM_GROUP", "subType": "TNC", "id": "consent", "title": "Consent to data sharing", "acknowledge": "I acknowledge and consent to the terms above.", "content": [{"header": "", "indent": [{"paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:", "indent": [{"paragraph": "to verify the identity and relationship of me and my family, for the Services or Schemes;"}, {"paragraph": "to conduct outreach to me and my family to inform us about, and to encourage us to apply for, the Services or Schemes;"}, {"paragraph": "to determine the eligibility of me and my family for the Services or Schemes;"}, {"paragraph": "to provide me and my family with the Services or Schemes;"}, {"paragraph": "to refer me and my family to other Services or Schemes not currently provided to us, if assessed to be useful to me and my family; and"}, {"paragraph": "for data analysis, research, evaluation and policy-making, for the Services or Schemes."}]}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use, and disclose my Personal Information for the purposes stated in Paragraph 1 and any other purpose permitted by law. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that if there are any discrepancies in the Personal Information collected, such discrepancies may be reflected to the relevant Singapore Public Agencies, so that they may take the necessary steps to rectify any inaccurate records relating to me."}, {"paragraph": "My consent shall remain valid until I withdraw it in writing. I accept that it will take up to 10-15 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/onemsf-omnibus-termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}]}]}]}]}, {"id": "parent", "actionLabel": "As a Parent", "section": [{"id": "profile", "title": "Profile", "subtitle": "Tell us about yourself", "member": [{"type": "DECORATOR", "title": "Personal details", "subType": "HEADER"}, {"id": "fullName", "type": "PRESET_FIELD", "subType": "name", "prefillSource": "myInfo.name"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric", "prefillSource": "myInfo.nric", "isUnmasked": true}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob", "prefillSource": "myInfo.dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex", "prefillSource": "myInfo.sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "prefillSource": "myInfo.residentialStatus"}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race", "prefillSource": "myInfo.race"}, {"type": "PRESET_FIELD", "subType": "relationshipTypeV5", "id": "relationship", "title": "Relationship to person with disability", "allowedOptions": ["REL001", "REL002", "REL003", "REL004", "REL006", "REL007", "REL009", "REL209", "REL408", "REL609", "REL507", "REL508", "REL509", "REL805", "REL807", "REL808", "REL010"], "globalAction": {"type": "populateDefaultValue", "defaultValue": "REL002"}}, {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "role", "title": "Role to person with disability", "options": {"LG": "Parent / Legal guardian", "DD": "<PERSON><PERSON> / Deputy", "CG": "Non-legal guardian"}, "globalAction": {"type": "populateDefaultValue", "defaultValue": "LG"}}, {"type": "DECORATOR", "title": "Contact details", "subType": "HEADER"}, {"id": "mobile", "type": "PRESET_FIELD", "subType": "mobileNumber", "prefillSource": "myInfo.mobileNumber", "editable": true}, {"id": "otherPhone", "type": "PRESET_FIELD", "subType": "homeNumber", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email", "prefillSource": "myInfo.email", "template": "application", "editable": true}]}, {"id": "disability", "title": "Person with Disability", "subtitle": "Tell us about the person with disability", "member": [{"type": "DECORATOR", "title": "Personal details", "subType": "HEADER"}, {"id": "fullName", "type": "PRESET_FIELD", "subType": "name", "title": "Name (as in NRIC)"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric", "title": "NRIC"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "excludedOptions": ["A"], "description": ["Only Singapore Citizens or Permanent Residents are eligible."]}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race"}, {"id": "pi_nric", "type": "CUSTOM_FIELD", "title": "NRIC or Birth Certificate", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "Front and back of the NRIC or Birth Certificate"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"]}, {"id": "address", "type": "PRESET_FIELD", "subType": "address", "allowOverseas": true}, {"type": "DECORATOR", "title": "Contact details", "subType": "HEADER"}, {"id": "mobile", "type": "PRESET_FIELD", "subType": "mobileNumber"}, {"id": "otherPhone", "type": "PRESET_FIELD", "subType": "homeNumber", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email"}, {"type": "DECORATOR", "title": "Disability condition", "subType": "HEADER"}, {"type": "CUSTOM_FIELD", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "id": "sh_dcf", "title": "Disability Verification Form (DVF)", "instructions": ["Please upload a copy of:"], "documents": [{"paragraph": "Completed DVF"}], "additionalDetails": ["Please ensure the DVF is endorsed by a registered healthcare professional before proceeding. You can [download the DVF](https://enablingguide.sg/disability-verification) here.", "&nbsp;\n**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"]}]}, {"id": "caregiver<PERSON><PERSON><PERSON>", "title": "Guardian / Caregiver", "subtitle": "Please complete the Person with Disability section before proceeding", "member": [{"type": "SECTION_CONDITIONAL", "id": "caregiver<PERSON><PERSON><PERSON>", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "haveLegalGuardianCaregiver", "title": "Does the person with disability have other guardians / caregivers (including parents)?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "caregiver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "How many other legal guardians / caregivers does the person with disability have, excluding yourself?", "maxGroup": 11, "header": "Guardian / Caregiver details", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "caregiverGuardianDetails", "title": "Guardian / Caregiver", "member": [{"id": "fullName", "type": "PRESET_FIELD", "subType": "name"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus"}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race"}, {"type": "PRESET_FIELD", "subType": "relationshipTypeV5", "id": "relationship", "title": "Relationship to person with disability", "allowedOptions": ["REL001", "REL002", "REL003", "REL004", "REL006", "REL007", "REL009", "REL209", "REL408", "REL609", "REL507", "REL508", "REL509", "REL805", "REL807", "REL808", "REL010"]}, {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "role", "title": "Role to person with disability", "options": [{"LG": "Parent / Legal guardian"}, {"DD": "<PERSON><PERSON> / Deputy"}, {"CG": "Non-legal guardian"}]}, {"type": "DECORATOR", "subType": "HEADER", "title": "Contact details"}, {"type": "PRESET_FIELD", "subType": "mobileNumber", "id": "mobile"}, {"type": "PRESET_FIELD", "subType": "email", "id": "email"}]}}]}]}]}, {"id": "terms", "title": "Terms & Conditions", "subtitle": "Please read and acknowledge the terms and conditions", "member": [{"type": "CUSTOM_GROUP", "subType": "TNC", "id": "consent", "title": "Consent to data sharing", "acknowledge": "I acknowledge and consent to the terms above.", "content": [{"header": "I am providing consent as the parent of the applicant who is/are under 21 years old.", "indent": [{"paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:", "indent": [{"paragraph": "to verify the identity and relationship of me and my family, for the Services or Schemes;"}, {"paragraph": "to conduct outreach to me and my family to inform us about, and to encourage us to apply for, the Services or Schemes;"}, {"paragraph": "to determine the eligibility of me and my family for the Services or Schemes;"}, {"paragraph": "to provide me and my family with the Services or Schemes;"}, {"paragraph": "to refer me and my family to other Services or Schemes not currently provided to us, if assessed to be useful to me and my family; and"}, {"paragraph": "for data analysis, research, evaluation and policy-making, for the Services or Schemes."}]}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use, and disclose my Personal Information for the purposes stated in Paragraph 1 and any other purpose permitted by law. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that if there are any discrepancies in the Personal Information collected, such discrepancies may be reflected to the relevant Singapore Public Agencies, so that they may take the necessary steps to rectify any inaccurate records relating to me."}, {"paragraph": "My consent shall remain valid until I withdraw it in writing. I accept that it will take up to 10-15 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/onemsf-omnibus-termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}]}]}, {"type": "DECORATOR", "subType": "HEADER", "title": "Additional documents, if any"}, {"id": "oth_others", "type": "CUSTOM_FIELD", "title": "Other supporting documents", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "Proof of relationship (e.g. birth certificate)"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}], "fileUploadExternal": {"url": "https://www.ccube.gov.sg/ccube-si-rsh/api/sgw/app-gen/upload-file/v1"}}
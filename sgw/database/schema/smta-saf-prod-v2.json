{"schemeName": "ComCare Short-to-Medium-Term Assistance (SMTA)", "schemeCode": "smta", "bundleSchemeCode": "saf", "schemeDetailsLink": "/schemes/COMCARE-SMTA", "subtitle": "Supported by Ministry of Social and Family Development", "dashboard": {"applicationGuidance": "Your application may take 10 minutes to complete."}, "contacts": [{"faqLink": "https://ask.gov.sg/msf?topic=ComCare&subtopic=Short-to-Medium-Term%20Assistance", "email": "<EMAIL>", "hotlineNumber": "1800 222 0000", "locationLink": "https://www.msf.gov.sg/dfcs/sso/", "helpExtra": ["You may also visit your nearest Social Service Office if you require urgent help.", "If you are receiving SMTA/LTA and require further help, please contact your SSO officer directly.", "Charges apply for mobile calls to 1800 service lines. Calls are free of charge only if made from regular land lines in Singapore."]}], "nextSteps": "Please wait while we assign an officer to your application.", "schema": [{"id": "main", "actionLabel": "Apply for 1 scheme", "descriptionLabel": "Continue to form", "section": [{"id": "profile", "title": "Profile", "subtitle": "Tell us about yourself", "safId": "sect-profile", "safIdx": "5", "member": [{"type": "DECORATOR", "subType": "HEADER", "safId": "personalDetails", "safIdx": "5", "title": "Personal details"}, {"id": "name", "type": "PRESET_FIELD", "subType": "name", "safId": "name", "safIdx": "10", "prefillSource": "myInfo.name"}, {"id": "nric", "type": "PRESET_FIELD", "subType": "nric", "safId": "nric", "safIdx": "15", "prefillSource": "myInfo.nric"}, {"id": "dob", "type": "PRESET_FIELD", "subType": "dob", "safId": "dob", "safIdx": "20", "prefillSource": "myInfo.dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex", "safId": "sex", "safIdx": "25", "prefillSource": "myInfo.sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "safId": "residentialStatus", "safIdx": "30", "prefillSource": "myInfo.residentialStatus"}, {"id": "race", "type": "PRESET_FIELD", "subType": "race", "safId": "race", "safIdx": "35", "prefillSource": "myInfo.race"}, {"id": "countryOfBirth", "type": "PRESET_FIELD", "subType": "countryOfBirth", "safId": "countryOfBirth", "safIdx": "40", "prefillSource": "myInfo.countryOfBirth"}, {"type": "SECTION_CONDITIONAL", "id": "spokenLanguage", "safId": "spokenLanguage", "safIdx": "45", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "language", "title": "Spoken language", "options": {"EL": "English", "MN": "Mandarin", "MY": "Malay", "TM": "Tamil", "XX": "Others (including dialect)"}}, "result": [{"choice": ["XX"], "member": [{"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "others", "safId": "preferredSpokenLanguage", "safIdx": "5", "title": "Tell us your preferred spoken language"}]}]}, {"id": "address", "type": "PRESET_FIELD", "subType": "address", "safId": "address", "safIdx": "50", "prefillSource": "myInfo.address"}, {"id": "mailingAddress", "type": "PRESET_FIELD", "subType": "mailingAddress", "safId": "mailingAddress", "safIdx": "55"}, {"type": "DECORATOR", "subType": "HEADER", "safId": "contactDetails", "safIdx": "60", "title": "Contact details"}, {"id": "mobileNumber", "type": "PRESET_FIELD", "subType": "mobileNumber", "safId": "mobileNumber", "safIdx": "65", "prefillSource": "myInfo.mobileNumber", "editable": true, "optional": true}, {"id": "homeNumber", "prefillSource": "smta.homeNumber", "editable": true, "type": "PRESET_FIELD", "subType": "homeNumber", "safId": "homeNumber", "safIdx": "70", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email", "safId": "email", "safIdx": "75", "prefillSource": "myInfo.email", "template": "application", "editable": true, "optional": true}, {"type": "DECORATOR", "subType": "HEADER", "title": "Education details"}, {"id": "highestEducation", "type": "CUSTOM_FIELD", "subType": "DROPDOWN", "prefillSource": "smta.highestEducation", "editable": true, "title": "Highest education level", "options": {"0": "No Formal Qualification/Lower Primary", "1": "Primary", "2": "Lower Secondary", "3": "Secondary", "4": "Post-Secondary (Non-tertiary): General Vocational", "5": "Polytechnic Diploma Course", "6": "Professional Qualification and Other Diploma", "7": "University First Degree", "8": "University Postgraduate Diploma/Degree", "9": "Other Education (Non-Award Courses/Miscellaneous)", "10": "Others"}}]}, {"id": "bankDetails", "safId": "sect-bank-details", "safIdx": "10", "title": "Bank details", "member": [{"type": "CUSTOM_FIELD", "subType": "FILE_UPLOAD", "id": "applicantBankStatement", "title": "Updated bank statements/bank books of applicant", "optional": true, "instructions": ["Please upload a copy of:"], "documents": [{"paragraph": "Your bank statements/bank books for all bank accounts"}], "additionalDetails": ["For each account, your full name, bank account number, and latest transaction details of at least 1 month before the application date must be shown clearly (e.g. if you are applying on 1 Jun 2024, transactions between 1 May 2024 and 1 Jun 2024 must be reflected)."]}, {"type": "SECTION_CONDITIONAL", "id": "bankAccount", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "ownerType", "prefillSource": "smta.bankOwnerType", "editable": true, "title": "Whose bank account should we credit the assistance to via GIRO?", "options": ["Mine", "My household member", "Someone else"]}, "result": [{"choice": ["0"], "member": [{"type": "PRESET_FIELD", "subType": "msfBankRefund", "id": "bankDetails", "prefillSource": "smta.bankDetails", "priorityRelativeId": ["name", "nric"], "defaultFullId": ["profile.name"]}]}, {"choice": ["1"], "member": [{"type": "DECORATOR", "subType": "HEADER", "title": "My household member", "description": ["Details of this household member needs to be included in the Household section for assessment, as they should be a recipient of the assistance."]}, {"type": "PRESET_FIELD", "subType": "name", "id": "name", "prefillSource": "smta.bankOwnerName", "editable": true}, {"type": "PRESET_FIELD", "subType": "nric", "id": "nric", "skipDuplicateCheck": true, "prefillSource": "smta.bankOwnerNric", "editable": true}, {"type": "PRESET_FIELD", "subType": "msfBankRefund", "id": "bankDetails", "prefillSource": "smta.bankDetails", "priorityRelativeId": ["name", "nric"], "defaultFullId": ["profile.name"]}]}, {"choice": ["2"], "member": [{"type": "DECORATOR", "subType": "HEADER", "title": "Someone else", "description": ["Please select this only if you or your household members do not have a bank account for crediting of assistance via GIRO.\n&nbsp;\nYour officer will follow up with you and your appointed recipient for further verifications after we have received your application."]}, {"type": "PRESET_FIELD", "subType": "name", "id": "name", "prefillSource": "smta.bankOwnerName", "editable": true}, {"type": "PRESET_FIELD", "subType": "nric", "id": "nric", "skipDuplicateCheck": true, "prefillSource": "smta.bankOwnerNric", "editable": true}, {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "relationshipType", "prefillSource": "smta.bankOwnerRelationshipType", "editable": true, "title": "Relationship to applicant", "options": {"RT31": "Family Member", "RT32": "Non-Family Member"}}, {"type": "PRESET_FIELD", "subType": "mobileNumber", "id": "mobileNumber", "prefillSource": "smta.bankOwnerMobile", "editable": true}, {"type": "PRESET_FIELD", "subType": "msfBankRefund", "id": "bankDetails", "prefillSource": "smta.bankDetails", "priorityRelativeId": ["name", "nric"], "defaultFullId": ["profile.name"]}]}]}]}, {"id": "household", "title": "Household", "safId": "sect-household", "safIdx": "15", "member": [{"type": "SECTION_CONDITIONAL", "id": "marital", "safId": "marital", "safIdx": "5", "selector": {"type": "PRESET_FIELD", "subType": "maritalStatus", "title": "Marital status", "id": "maritalStatus", "prefillSource": "myInfo.maritalStatus", "editable": true}, "result": [{"choice": ["4"], "member": [{"type": "CUSTOM_FIELD", "subType": "DATE", "safId": "dateOfSeparation", "safIdx": "5", "id": "dateOfSeparation", "title": "Date of separation"}]}, {"choice": ["2"], "member": [{"type": "DECORATOR", "subType": "HEADER", "safId": "spouseDetails", "safIdx": "5", "title": "Spouse details"}, {"type": "PRESET_FIELD", "subType": "name", "safId": "<PERSON><PERSON><PERSON>", "safIdx": "10", "id": "<PERSON><PERSON><PERSON>"}, {"type": "PRESET_FIELD", "subType": "nric", "safId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "safIdx": "15", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consent": true, "consenterInfo": {"name": "<PERSON><PERSON><PERSON>"}}, {"type": "PRESET_FIELD", "subType": "dob", "safId": "spouse<PERSON><PERSON>", "safIdx": "20", "id": "spouse<PERSON><PERSON>"}, {"type": "PRESET_FIELD", "id": "spouseSex", "subType": "sex", "safId": "spouseSex", "safIdx": "25"}, {"type": "PRESET_FIELD", "subType": "residentialStatus", "safId": "spouseResidentialStatus", "safIdx": "30", "id": "spouseResidentialStatus"}, {"type": "PRESET_FIELD", "subType": "email", "safId": "spouseEmail", "safIdx": "35", "id": "spouseEmail", "optional": true}]}]}, {"type": "DECORATOR", "subType": "HEADER", "safId": "householdMembers", "safIdx": "10", "title": "Household members"}, {"type": "SECTION_CONDITIONAL", "id": "cohabitation", "safId": "cohabitation", "safIdx": "15", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "cohabitationStatus", "prefillSource": "smta.cohabitationStatus", "editable": true, "title": "Do you live with any household member?", "description": ["This refers to family members who are living in the same registered address (e.g. parents, children). You are not required to provide your spouse details (if any) again."], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "familyMember", "safId": "familyMember", "safIdx": "5", "prefillSource": "smta.familyMemberDetails", "editable": true, "title": "How many household members (apart from spouse) live with you?", "maxGroup": 15, "header": "Household member details", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "familyMemberDetails", "title": "Household member", "member": [{"id": "family", "safId": "family", "safIdx": "5", "prefillSource": "family", "editable": true, "type": "PRESET_FIELD", "consent": true, "subType": "family"}, {"type": "PRESET_FIELD", "subType": "residentialStatus", "safId": "residentialStatus", "safIdx": "10", "id": "residentialStatus"}, {"type": "PRESET_FIELD", "subType": "email", "safId": "email", "safIdx": "15", "id": "email", "prefillSource": "email", "editable": true, "optional": true}, {"type": "PRESET_FIELD", "subType": "relationshipType", "safId": "relationshipType", "safIdx": "20", "id": "relationshipType", "title": "Relationship to applicant", "excludedOptions": ["REL001"], "prefillSource": "relationshipType", "editable": true}, {"id": "memberMinorConsent", "safId": "memberMinorConsent", "safIdx": "25", "type": "GLOBAL_GROUP_CONDITIONAL", "result": [{"choice": [{"type": "value", "sourcePath": "computedAge", "operator": "lessThan", "value": "21"}], "member": [{"id": "consent", "safId": "consent", "safIdx": "5", "type": "CUSTOM_FIELD", "subType": "TNC", "title": "Consent / declaration for minors", "acknowledge": "I acknowledge and consent to the terms, on behalf of this minor", "content": [{"header": "Consent for collection, use and disclosure of personal information for the application or renewal of application for the subsidy and/or the Start Up Grant", "indent": [{"paragraph": "I am the parent/legal guardian of the Child who is under 21 years of age."}, {"indent": [{"paragraph": "to verify my, my Child and Family’s identity and relationship for the Subsidy and/or the Start Up Grant (“the Subsidies”) and other Services or Scheme;"}, {"paragraph": "to determine my, my Child and Family’s eligibility for the Subsidies and other Services or Scheme;"}, {"paragraph": "to provide me, my Child and Family with both or any of the Subsidies and other Services or Scheme; and"}, {"paragraph": "for data analysis, evaluation and policy-making, for the Subsidies and other Services or Scheme."}], "paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:"}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information, to the extent permitted by the Singapore Public Agencies and Participating Organisations, for the purposes stated in Paragraph 2. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that my personal information will not be shared with non-participating agencies and organisations."}, {"paragraph": "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}, {"paragraph": "This consent shall be governed by and construed in accordance with the laws of the Republic of Singapore."}]}, {"header": "Declaration", "indent": [{"paragraph": "I, the undersigned, declare that I have read and understood the content in this Application Form. I confirm that the information that I have provided is true and correct and I furnish the information knowing that I may be liable to criminal prosecution if I have stated any information that I know to be false or not believe to be true."}, {"paragraph": "In the event my application is successful and my Child receives any of the Subsidies which I am applying for, I hereby acknowledge that I may also be liable to make full repayment to the Government of the Subsidies which were provided, should I be found to have provided false or inaccurate information in this form."}]}, {"header": "Other terms", "indent": [{"indent": [{"paragraph": "It shall be my responsibility to stay employed (i.e. to be engaged under a contract or service and receive a salary) to continue to enjoy the Subsidies for my Child. If I am unemployed and intend to seek employment, the onus is on me to actively seek employment."}, {"paragraph": "(only applicable to applications for the Start Up Grant) The Start Up Grant shall only be given once to my Child, and any subsequent applications shall be assessed and granted only in MSF’s sole discretion."}, {"paragraph": "In order to continue enjoying the relevant monthly Subsidy, I must ensure that my Child attends at least 30% (June and December) and 50% (Other Calendar months) of the number of days in which the SCC operates per month. My Child must be present at the centre for at least 3 hours in order to be considered present for the day. If my Child does not meet the minimum attendance rate, the Subsidy paid for the relevant month may be refunded to MSF and I am liable to pay the full SCC monthly fee."}, {"paragraph": " I shall provide the SCC with a one-month notice before withdrawing my Child from the SCC."}], "paragraph": "I understand and agree to the following:"}]}]}]}, {"choice": [{"type": "value", "sourcePath": "computedAge", "operator": "greaterThanInclusive", "value": "21"}], "member": [{"type": "DECORATOR", "safId": "consentInfoblock", "safIdx": "5", "title": ["**Household members’ consent is required for the application.**\nMore details on how your household members can provide consent will be given after the application is submitted."], "subType": "INFO_BLOCK"}]}], "selector": {"id": "computedAge", "type": "CUSTOM_FIELD", "subType": "HIDDEN", "globalAction": {"type": "calculateMultiValueAge", "dependsOn": ["household.cohabitation.familyMember.familyMemberDetails"], "relativeIds": ["family.member<PERSON>ob"]}}}]}}]}]}, {"type": "DECORATOR", "subType": "HEADER", "title": "Other family members"}, {"type": "SECTION_CONDITIONAL", "id": "dependent", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "dependentStatus", "title": "Do you have any other family members that live separately but depend on you financially?", "description": ["This refers to family members who are not living in the same registered address (e.g. parents, children). You are not required to provide your spouse and household member details (if any) again."], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "familyMember", "title": "How many of these other family members are there?", "maxGroup": 15, "header": "Other family member details ", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "familyMemberDetails", "title": "Other family member", "member": [{"type": "PRESET_FIELD", "subType": "name", "id": "name"}, {"type": "PRESET_FIELD", "subType": "nric", "id": "nric", "consent": true, "consenterInfo": {"name": "name"}}, {"type": "PRESET_FIELD", "subType": "dob", "id": "dob"}, {"type": "PRESET_FIELD", "id": "sex", "subType": "sex"}, {"type": "PRESET_FIELD", "subType": "residentialStatus", "id": "residentialStatus"}, {"type": "PRESET_FIELD", "subType": "email", "id": "email", "optional": true}, {"type": "PRESET_FIELD", "subType": "relationshipType", "id": "relationshipType", "title": "Relationship to applicant", "excludedOptions": ["REL001"]}, {"id": "memberMinorConsent", "type": "GLOBAL_GROUP_CONDITIONAL", "result": [{"choice": [{"type": "value", "sourcePath": "computedMemberAge", "operator": "lessThan", "value": "21"}], "member": [{"id": "consent", "type": "CUSTOM_FIELD", "subType": "TNC", "title": "Consent / declaration for minors", "acknowledge": "I acknowledge and consent to the terms, on behalf of this minor", "content": [{"header": "Consent for collection, use and disclosure of personal information for the application or renewal of application for the subsidy and/or the Start Up Grant", "indent": [{"paragraph": "I am the parent/legal guardian of the Child who is under 21 years of age."}, {"indent": [{"paragraph": "to verify my, my Child and Family’s identity and relationship for the Subsidy and/or the Start Up Grant (“the Subsidies”) and other Services or Scheme;"}, {"paragraph": "to determine my, my Child and Family’s eligibility for the Subsidies and other Services or Scheme;"}, {"paragraph": "to provide me, my Child and Family with both or any of the Subsidies and other Services or Scheme; and"}, {"paragraph": "for data analysis, evaluation and policy-making, for the Subsidies and other Services or Scheme."}], "paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:"}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information, to the extent permitted by the Singapore Public Agencies and Participating Organisations, for the purposes stated in Paragraph 2. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that my personal information will not be shared with non-participating agencies and organisations."}, {"paragraph": "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}, {"paragraph": "This consent shall be governed by and construed in accordance with the laws of the Republic of Singapore."}]}, {"header": "Declaration", "indent": [{"paragraph": "I, the undersigned, declare that I have read and understood the content in this Application Form. I confirm that the information that I have provided is true and correct and I furnish the information knowing that I may be liable to criminal prosecution if I have stated any information that I know to be false or not believe to be true."}, {"paragraph": "In the event my application is successful and my Child receives any of the Subsidies which I am applying for, I hereby acknowledge that I may also be liable to make full repayment to the Government of the Subsidies which were provided, should I be found to have provided false or inaccurate information in this form."}]}, {"header": "Other terms", "indent": [{"indent": [{"paragraph": "It shall be my responsibility to stay employed (i.e. to be engaged under a contract or service and receive a salary) to continue to enjoy the Subsidies for my Child. If I am unemployed and intend to seek employment, the onus is on me to actively seek employment."}, {"paragraph": "(only applicable to applications for the Start Up Grant) The Start Up Grant shall only be given once to my Child, and any subsequent applications shall be assessed and granted only in MSF’s sole discretion."}, {"paragraph": "In order to continue enjoying the relevant monthly Subsidy, I must ensure that my Child attends at least 30% (June and December) and 50% (Other Calendar months) of the number of days in which the SCC operates per month. My Child must be present at the centre for at least 3 hours in order to be considered present for the day. If my Child does not meet the minimum attendance rate, the Subsidy paid for the relevant month may be refunded to MSF and I am liable to pay the full SCC monthly fee."}, {"paragraph": " I shall provide the SCC with a one-month notice before withdrawing my Child from the SCC."}], "paragraph": "I understand and agree to the following:"}]}]}]}, {"choice": [{"type": "value", "sourcePath": "computedMemberAge", "operator": "greaterThanInclusive", "value": "21"}], "member": [{"type": "DECORATOR", "title": ["**Household members’ consent is required for the application.**\nMore details on how your household members can provide consent will be given after the application is submitted."], "subType": "INFO_BLOCK"}]}], "selector": {"id": "computedMemberAge", "type": "CUSTOM_FIELD", "subType": "HIDDEN", "globalAction": {"type": "calculateMultiValueAge", "dependsOn": ["household.dependent.familyMember.familyMemberDetails"], "relativeIds": ["dob"]}}}]}}]}]}, {"type": "CUSTOM_FIELD", "subType": "FILE_UPLOAD", "id": "hhMemberBankStatement", "title": "Updated bank statements/bank books of all household and other family members", "optional": true, "instructions": ["Please upload a copy of:"], "documents": [{"paragraph": "Your **bank statements/bank books of all household and other family members for all bank accounts,** if any"}], "additionalDetails": ["For each account, your household and other family members’ full name, bank account number, and latest transaction details of at least 1 month before the application date must be shown clearly (e.g. if you are applying on 1 Jun 2024, transactions between 1 May 2024 and 1 Jun 2024 must be reflected).", "I understand that submitting incorrect documents may cause delays in processing my application."]}, {"type": "CUSTOM_FIELD", "subType": "FILE_UPLOAD", "id": "hhMemberIdDoc", "title": "NRIC/FIN/BC of household and other family members", "optional": true, "instructions": ["Please upload a copy of:"], "documents": [{"paragraph": "Front and back of the **NRIC/FIN of all adults,** if any"}, {"paragraph": "**Birth certificate of all children,** if any"}], "additionalDetails": ["Your household and other family member’s name and details must be shown clearly."]}]}, {"id": "additionalDetails", "safId": "sect-additional-details", "safIdx": "20", "title": "Additional details", "subtitle": "Provide additional details about your application, if relevant, to help us better assess your situation.", "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "concerns", "title": "What concerns do you have about your household's situation, and what support are you currently receiving?", "optional": true, "maxLength": 300, "description": ["It can be about a loss of job or income, difficulty paying for basic needs, or any other concerns. Please also share any support you're currently receiving from other sources e.g. community organisations, non-immediate family members, etc. We will do our best to help you."]}, {"type": "CUSTOM_FIELD", "subType": "FILE_UPLOAD", "id": "otherDoc", "title": "SMTA - Other supporting documents", "optional": true, "instructions": ["Please upload a copy of:"], "documents": [{"paragraph": "Any **other documents to support this application**"}]}]}, {"id": "terms", "safId": "sect-tnc", "safIdx": "25", "title": "Terms & Conditions", "subtitle": "Please read and acknowledge the terms and conditions", "member": [{"type": "CUSTOM_GROUP", "subType": "TNC", "id": "dataSharing", "title": "Consent to data sharing", "acknowledge": "I acknowledge and consent to the terms above.", "content": [{"header": "", "indent": [{"paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:", "indent": [{"paragraph": "to verify my and my Family’s identity and relationship for the Services or Scheme;"}, {"paragraph": "to determine my and my Family’s eligibility for the Services or Scheme;"}, {"paragraph": "to provide me and my Family with the Services or Scheme; and"}, {"paragraph": "for data analysis, evaluation and policy-making, for the Services or Scheme."}]}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information for the purposes stated in Paragraph 1 and any other purpose permitted by law. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that if there are any discrepancies in the Personal Information collected, such discrepancies may be reflected to the relevant Singapore Public Agencies, so that they may take the necessary steps to rectify any inaccurate records relating to me."}, {"paragraph": "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}]}]}, {"type": "CUSTOM_GROUP", "subType": "TNC", "id": "lou", "title": "Letter of Undertaking", "acknowledge": "I understand and agree to abide by the undertaking.", "content": [{"header": "I understand that:", "indent": [{"paragraph": "The SSO would need to ask me some questions and may require me to submit some documents to see how they can help."}, {"paragraph": "If I owe any debts to the Government under any other assistance schemes, such debts may be deducted from the amount of any ComCare assistance payable to me."}, {"paragraph": "The Government may withhold or reduce any ComCare assistance payable, pending the conduct of any investigations, checks or audits whether under this scheme or under other Government assistance schemes."}, {"paragraph": "If this application for ComCare assistance is approved, the approved quantum of ComCare assistance (if any) will be paid into the bank account as indicated under the “Bank Details” section of my application."}]}, {"header": "I agree to:", "indent": [{"paragraph": "give complete and correct information about myself and the people living with me. This includes information on the ownership of assets (such as HDB, private property, vehicles, or investments), savings, and income;"}, {"paragraph": "tell all the people living in my household that I am applying for assistance;"}, {"paragraph": "cooperate with any investigations, checks or audits relating to the assistance that I am receiving under ComCare or other Government assistance schemes; and/or"}, {"paragraph": "tell my officer immediately if my contact number changes or my/my household’s situation changes at any point. For example, this could happen if I am/my household member is:", "indent": [{"paragraph": "admitted into a home or residential institution;"}, {"paragraph": "incarcerated;"}, {"paragraph": "hospitalised for more than 6 months; and/or"}, {"paragraph": "earning additional income or receiving any proceeds from sale of an asset."}]}]}, {"header": "If my application is approved, I will:", "indent": [{"paragraph": "make sure no one in my household, including myself, enters a casino in Singapore;"}, {"paragraph": "make sure no one in my household, including myself, opens or maintains a player account with the Singapore Pools;"}, {"paragraph": "make sure no one in my household, including myself, visits any jackpot machine rooms operated by private clubs in Singapore;"}, {"paragraph": "tell the SSO if I, or anyone in my household, have to enter a casino for work; and"}, {"paragraph": "agree that if I am admitted into any home or residential institution, and am still eligible to receive ComCare assistance, the SSO will disburse my assistance to the management of the home or institution that I am in."}]}, {"header": "After the assistance has been granted, I agree to:", "indent": [{"paragraph": "carry out my action plan as discussed with my case officer; and/or"}, {"paragraph": "respond to communications (e.g. email(s), message(s) or call(s)) by my case officer and meet my case officer at planned appointments."}]}, {"header": "I understand that if I do not abide by the undertaking that I have given:", "indent": [{"paragraph": "my application may be rejected;"}, {"paragraph": "the assistance extended to me may be terminated;"}, {"paragraph": "the assistance extended to me may be reduced;"}, {"paragraph": "the assistance may be withheld until such time that I comply with the terms of the undertaking;"}, {"paragraph": "MSF may recover the quantum of any assistance extended to me or the person whose bank account the assistance was credited into (whether such account was held in the person’s sole name or jointly with me); and/or"}, {"paragraph": "legal action may be taken against me for providing any false information in the application."}]}]}]}]}], "consentDocumentsSchema": {"section": [{"id": "outstandingItems", "title": "Documents", "member": [{"id": "hhMemberconsent", "type": "CUSTOM_FIELD", "title": "Household members’ consent form", "subType": "FILE_UPLOAD", "documents": [{"indent": [{"paragraph": "Log in via [SupportGoWhere’s website](https://supportgowhere.life.gov.sg/)."}, {"paragraph": "On My Applications page, select the ''To-do'' tab and find the request for consent."}], "paragraph": "**Digital consent** - For household members with Singpass:"}, {"indent": [{"paragraph": "Download the [consent form](https://go.gov.sg/householdconsent)."}, {"paragraph": "Fill in the details required."}, {"paragraph": "Upload the signed copy with the details shown clearly."}], "paragraph": "**Manual consent** - For household members without Singpass:"}], "instructions": ["**Consent is not required for household members who:**\n(1) Require consent to be provided on behalf by a <PERSON>e/Deputy,\n(2) Have already provided consent in an earlier application.", "**2 ways to provide consent:**"]}], "subtitle": "Upload relevant documents to help us better assess your situation"}]}, "outstandingDocumentsSchema": {"section": [{"id": "outstandingItems", "title": "Documents", "member": [{"id": "applicantBankStatement", "type": "CUSTOM_FIELD", "title": "Updated bank statements/bank books of applicant", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Your bank statements/bank books for all bank accounts"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["For each account, your full name, bank account number, and latest transaction details of at least 1 month before the application date must be shown clearly (e.g. if you are applying on 1 Jun 2024, transactions between 1 May 2024 and 1 Jun 2024 must be reflected)."]}, {"id": "hhMemberIdDocument", "type": "CUSTOM_FIELD", "title": "NRIC/FIN/BC of household and other family members", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Front and back of the **NRIC/FIN of all adults,** if any"}, {"paragraph": "**Birth certificate of all children,** if any"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["Your household and other family member’s name and details must be shown clearly."]}, {"id": "hhMemberBankStatement", "type": "CUSTOM_FIELD", "title": "Updated bank statements/bank books of all household and other family members", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Your **bank statements/bank books of all household and other family members for all bank accounts,** if any"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["For each account, your household and other family members’ full name, bank account number, and latest transaction details of at least 1 month before the application date must be shown clearly (e.g. if you are applying on 1 Jun 2024, transactions between 1 May 2024 and 1 Jun 2024 must be reflected).", "I understand that submitting incorrect documents may cause delays in processing my application."]}, {"id": "hhMemberConsent", "type": "CUSTOM_FIELD", "title": "Household members’ consent form", "subType": "FILE_UPLOAD", "documents": [{"indent": [{"paragraph": "Log in via [SupportGoWhere’s website](https://supportgowhere.life.gov.sg/)."}, {"paragraph": "On My Applications page, select the ''To-do'' tab and find the request for consent."}], "paragraph": "**Digital consent** - For household members with Singpass:"}, {"indent": [{"paragraph": "Download the [consent form](https://go.gov.sg/householdconsent)."}, {"paragraph": "Fill in the details required."}, {"paragraph": "Upload the signed copy with the details shown clearly."}], "paragraph": "**Manual consent** - For household members without Singpass:"}], "instructions": ["**Consent is not required for family members who:**\n(1) Require consent to be provided on behalf by a <PERSON>e/Deputy,\n(2) Have already provided consent in an earlier application.", "**2 ways to provide consent:**"]}, {"id": "proxyRecipient", "type": "CUSTOM_FIELD", "title": "Proxy recipient form", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "The **completed proxy recipient form** signed by **your appointed recipient**"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["Download the [proxy recipient form](https://go.gov.sg/proxyrecipient).", "Fill in the details required and upload a signed copy with the details shown clearly.", "For queries relating to the proxy recipient form, please contact your officer."]}, {"id": "utilityBill", "type": "CUSTOM_FIELD", "title": "Latest utilities bill", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Your **latest bill** from Singapore Power (SP)/private electricity retailer"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["The full name, address, account number, utilities company, and bill amount must be shown clearly."]}, {"id": "medicalDoc", "type": "CUSTOM_FIELD", "title": "Medical Certificate / Medical letter", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Your **Medical Certificate (MC)**"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["The full name, medical centre, dates/duration, medical leave type (e.g. hospitalisation, outpatient leave), and fit/unfit for work status must be shown clearly."]}, {"id": "employmentDoc", "type": "CUSTOM_FIELD", "title": "Supporting documents for employment", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Any relevant **supporting documents for the employment** (e.g. Latest 3 months of payslips or commission earnings statements from date of application)"}], "instructions": ["Please upload a copy of:"]}, {"id": "spouseEmploymentDoc", "type": "CUSTOM_FIELD", "title": "Spouse’s supporting documents for employment", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Any relevant **supporting documents for the employment** (e.g. Latest 3 months of payslips or commission earnings statements from date of application)"}], "instructions": ["Please upload a copy of:"]}, {"id": "hhEmploymentDoc", "type": "CUSTOM_FIELD", "title": "Household member’s supporting documents for employment", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Any relevant **supporting documents for the employment** (e.g. Latest 3 months of payslips or commission earnings statements from date of application)"}], "instructions": ["Please upload a copy of:"]}, {"id": "otherFamilyEmploymentDoc", "type": "CUSTOM_FIELD", "title": "Other family member’s supporting documents for employment", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Any relevant **supporting documents for the employment** (e.g. Latest 3 months of payslips or commission earnings statements from date of application)"}], "instructions": ["Please upload a copy of:"]}, {"id": "unemploymentDoc", "type": "CUSTOM_FIELD", "title": "Supporting documents for unemployment", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Any relevant **supporting documents about the situation** (e.g. Letter of Incarceration (LOI), Medical certificate)"}], "instructions": ["Please upload a copy of:"]}, {"id": "spouseUnemploymentDoc", "type": "CUSTOM_FIELD", "title": "Spouse’s supporting documents for unemployment", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Any relevant **supporting documents about the situation** (e.g. Letter of Incarceration (LOI), Medical certificate)"}], "instructions": ["Please upload a copy of:"]}, {"id": "hhUnemploymentDoc", "type": "CUSTOM_FIELD", "title": "Household member’s supporting documents for unemployment", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Any relevant **supporting documents about the situation** (e.g. Letter of Incarceration (LOI), Medical certificate)"}], "instructions": ["Please upload a copy of:"]}, {"id": "otherFamilyUnemploymentDoc", "type": "CUSTOM_FIELD", "title": "Other family member’s supporting documents for unemployment", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Any relevant **supporting documents about the situation** (e.g. Letter of Incarceration (LOI), Medical certificate)"}], "instructions": ["Please upload a copy of:"]}, {"id": "payslipDoc", "type": "CUSTOM_FIELD", "title": "Payslips", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Your **payslips or employment contract,** if you are employed without CPF contributions"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["The full name, company and date must be shown clearly."]}, {"id": "insuranceDoc", "type": "CUSTOM_FIELD", "title": "Insurance letter", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Your **insurance letter** for lump-sum or regular payouts by private insurer (e.g. Eldershield)"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["The full name, insurance company, date and payout amount must be shown clearly."]}, {"id": "SD8", "type": "CUSTOM_FIELD", "title": "Custody papers", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Custody papers with clause stating who has “care and control” of child from previous marriage"}], "instructions": ["If you are **married with child from previous marriage,** please upload a copy of:"]}, {"id": "SD12", "type": "CUSTOM_FIELD", "title": "Divorce proceedings documents", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Supporting Documents (e.g. Interim Judgement - Divorce)"}], "instructions": ["If you are **in process of divorce** please upload a copy of:"]}, {"id": "otherDoc", "type": "CUSTOM_FIELD", "title": "Other supporting documents", "subType": "FILE_UPLOAD", "documents": [{"paragraph": "Any other documents to support this application"}], "instructions": ["Please upload a copy of:"]}], "subtitle": "Upload relevant documents to help us better assess your situation"}]}, "applicationJourney": {"steps": [{"title": "Apply", "subtitle": "Applied on {{appliedDateTime}}", "description": "Provide details of yourself and your household members.\n&nbsp;\nIf you need help with your application, please refer to this [step-by-step video guide](https://www.youtube.com/watch?v=n2jkwhEyTmU)."}, {"title": "Assigning an officer to you", "description": "An officer is being assigned to your application."}, {"title": "Talk to your officer", "description": "The SSO will call you within 3 working days. Your officer will then follow-up with you to open your case."}, {"title": "Processing", "description": "Your application is being reviewed. Upon submission of all supporting documents, we will take 4-6 weeks to process your application."}, {"title": "Check outcome", "description": "Check your application details."}], "ongoingStatusToStep": {"0": 1, "1": 2, "2": 3, "20": 3, "21": 3}, "action": {"consentDocs": {"title": "Consent", "description": "Your application may require additional consent from others.", "actionLabel": "Get consent", "url": "/grants/smta/{{refId}}/documents"}, "outstandingItems": {"description": "Upload documents requested by your officer.", "deadline": "Upload by {{deadline}}", "actionLabel": "Upload documents", "url": "/grants/smta/{{refId}}/documents"}}, "statusToAction": {"0": ["consentDocs"], "1": ["consentDocs"], "20": ["outstandingItems"]}}, "consentSchema": {"section": [{"id": "consent", "title": "Household member consent", "member": [{"type": "DECORATOR", "subType": "INFO_BLOCK", "title": ["If you are not aware of the above application, or not the intended household member, please email [<EMAIL>](mailto:<EMAIL>) or call [1800 222 0000](tel:18002220000). Charges apply for mobile calls to 1800 service lines. Calls are free of charge only if made from regular land lines in Singapore."]}, {"type": "PRESET_FIELD", "subType": "name", "id": "name", "prefillSource": "myInfo.name", "editable": false}, {"type": "PRESET_FIELD", "subType": "nric", "id": "nric", "prefillSource": "myInfo.nric", "editable": false}, {"type": "PRESET_FIELD", "subType": "dob", "id": "dob", "prefillSource": "myInfo.dob", "editable": false}, {"type": "PRESET_FIELD", "subType": "sex", "id": "sex", "prefillSource": "myInfo.sex", "editable": false}, {"type": "PRESET_FIELD", "subType": "race", "id": "race", "prefillSource": "myInfo.race", "editable": false}, {"type": "DECORATOR", "subType": "HEADER", "title": "Contact details"}, {"type": "PRESET_FIELD", "subType": "mobileNumber", "id": "mobileNumber", "prefillSource": "myInfo.mobileNumber", "editable": true}, {"type": "PRESET_FIELD", "subType": "homeNumber", "id": "homeNumber", "optional": true, "prefillSource": "myInfo.homeNumber", "editable": true}, {"type": "PRESET_FIELD", "subType": "email", "template": "consent", "id": "email", "optional": true, "prefillSource": "myInfo.email", "editable": true}, {"type": "CUSTOM_GROUP", "subType": "TNC", "id": "dataSharing", "title": "Consent to data sharing", "acknowledge": "I acknowledge and consent to the terms above.", "content": [{"header": "Consent for collection, use and disclosure of personal information", "indent": [{"paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:", "indent": [{"paragraph": "to verify my and my Family’s identity and relationship for the Services or Scheme;"}, {"paragraph": "to determine my and my Family’s eligibility for the Services or Scheme;"}, {"paragraph": "to provide me and my Family with the Services or Scheme; and"}, {"paragraph": "for data analysis, evaluation and policy-making, for the Services or Scheme."}]}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information for the purposes stated in Paragraph 1 and any other purpose permitted by law. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that if there are any discrepancies in the Personal Information collected, such discrepancies may be reflected to the relevant Singapore Public Agencies, so that they may take the necessary steps to rectify any inaccurate records relating to me."}, {"paragraph": "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}]}]}]}]}}
{"schemeName": "Job Placement Job Support (JPJS)", "schemeCode": "emps", "schemeDetailsLink": "/schemes/ejdrPN9A", "agencyControlled": true, "subtitle": "Supported by SG Enable", "dashboard": {"applicationGuidance": "The application may take 30 mins to complete.\n&nbsp;\n**Only persons with disabilities who are Singapore citizens or Permanent Residents are eligible to apply.**\n&nbsp;\nPlease ensure that you have all the [required documents](https://supportgowhere.life.gov.sg/schemes/ejdrPN9A) ready as we do not save a draft for this application."}, "contacts": [{"email": "<EMAIL>", "hotlineNumber": "1800 8585 885", "helpExtra": ["Charges apply for mobile calls to 1800 service lines. Calls are free of charge only if made from regular land lines in Singapore."]}], "nextSteps": "Upon the submission of all supporting documents, we will take up to 15 working days to process your application.", "schema": [{"id": "pwd", "actionLabel": "Apply as a Person with disability", "section": [{"id": "profile", "title": "Profile", "subtitle": "Tell us about yourself", "member": [{"type": "DECORATOR", "title": "Personal details", "subType": "HEADER"}, {"id": "fullName", "type": "PRESET_FIELD", "subType": "name", "title": "Name (as in NRIC)", "prefillSource": "myInfo.name"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric", "title": "NRIC", "prefillSource": "myInfo.nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob", "prefillSource": "myInfo.dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex", "prefillSource": "myInfo.sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "prefillSource": "myInfo.residentialStatus", "excludedOptions": ["A"], "description": ["Only Singapore Citizens or Permanent Residents are eligible."]}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race", "prefillSource": "myInfo.race"}, {"id": "address", "type": "PRESET_FIELD", "subType": "address", "prefillSource": "myInfo.address", "allowOverseas": true}, {"type": "DECORATOR", "title": "Contact details", "subType": "HEADER"}, {"id": "mobile", "type": "PRESET_FIELD", "subType": "mobileNumber", "prefillSource": "myInfo.mobileNumber", "editable": true}, {"id": "otherPhone", "type": "PRESET_FIELD", "subType": "homeNumber", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email", "prefillSource": "myInfo.email", "template": "application", "editable": true}]}, {"id": "disability", "title": "Disability", "subtitle": "Tell us about your disability", "member": [{"id": "sh_dcf", "type": "CUSTOM_FIELD", "title": "Disability Verification Form (DVF)", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "optional": true, "instructions": ["Individuals whose disability status **has been verified\\* do not need to submit** the Disability Verification Form.", "Individuals whose disability status has **not been verified**, please upload a copy of:"], "documents": [{"paragraph": "Completed DVF"}], "additionalDetails": ["Please ensure the DVF is endorsed by a registered healthcare professional before proceeding. You can download the DVF [here](https://www.enablingguide.sg/disability-verification).", "*You can check if your disability status is verified at [SupportGoWhere - MSF Disabilty Verification](/grants/pwdr).", "**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"]}, {"type": "SECTION_CONDITIONAL", "id": "mobilityAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useMobilityAids", "title": "Do you use mobility aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "mobilityAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfMobilityAids", "title": "Which mobility aids do you use?", "description": ["You may select more than one option."], "options": {"MA1": "Manual wheelchair", "MA2": "Motorised wheelchair", "MA3": "Prosthesis", "MA4": "Quad stick", "MA5": "Walking frame", "MA6": "Walking stick", "MA99": "Others"}}, "result": [{"choice": ["MA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfMobilityAidsSpecify", "title": "Tell us about the mobility aids", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "hearingAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useHearingAids", "title": "Do you use hearing aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "hearingAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfHearingAids", "title": "Which hearing aids do you use?", "description": ["You may select more than one option."], "options": {"HA1": "Behind-the-ear hearing aids", "HA2": "In-the-ear hearing aids", "HA3": "Receiver-in-the-ear hearing aids", "HA4": "In-the-canal hearing aids", "HA99": "Others"}}, "result": [{"choice": ["HA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfHearingAidsSpecify", "title": "What type of hearing aids do you use?", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "visualAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useVisualAids", "title": "Do you use visual aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "visualAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfVisualAids", "title": "Which visual aids do you use?", "description": ["You may select more than one option."], "options": {"VA1": "Corrective glasses", "VA2": "Optical devices such as Magnifiers", "VA3": "Screen readers", "VA4": "White Cane", "VA99": "Others"}}, "result": [{"choice": ["VA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfVisualAidsSpecify", "title": "What type of visual aids do you use?", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "travelIndependently", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "ableToTravelIndependently", "title": "Are you able to travel independently? ", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "travelIndependentlyDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "abilityToTravelIndependently", "title": "Which modes of transport do you use?", "description": ["You may select more than one option."], "options": {"MT1": "Bus", "MT2": "Car", "MT3": "MRT", "MT4": "Taxi", "MT99": "Others"}}, "result": [{"choice": ["MT99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "abilityToTravelIndependentlySpecify", "title": "Tell us about the modes of transport", "maxLength": 250}]}]}]}, {"choice": ["1"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "travelAssistanceRequired", "title": "Tell us about the assistance you require when you travel", "maxLength": 250}]}]}, {"type": "SECTION_CONDITIONAL", "id": "modeOfCommunication", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "preferredModeOfCommunication", "title": "Preferred mode of communication", "description": ["You may select more than one option."], "options": {"MC1": "Lip reading", "MC2": "Signing", "MC3": "SMS", "MC4": "Verbal", "MC5": "Written", "MC99": "Others"}}, "result": [{"choice": ["MC99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "preferredModeOfCommunicationSpecify", "title": "Tell us about the preferred mode of communication", "maxLength": 250}]}]}]}, {"id": "educationEmployment", "title": "Education and employment", "subtitle": "Tell us about your education and employment details", "member": [{"type": "DECORATOR", "title": "Education details", "subType": "HEADER"}, {"id": "highestLevelOfEducationAttained", "type": "PRESET_FIELD", "subType": "education"}, {"type": "SECTION_CONDITIONAL", "id": "personalEducationRecords", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "providePersonalEducation", "title": "Would you like to provide your education records?", "description": ["Providing education records allows us to find better matching jobs for you."], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "personalEducation", "title": "How many education records would you like to add?", "maxGroup": 10, "header": "Education records", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "personalEducationDetails", "title": "Education", "member": [{"id": "educationLevel", "type": "PRESET_FIELD", "subType": "education", "title": "Education level"}, {"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "educationLevelSpecify", "title": "Tell us about the education level", "description": ["Provide relevant details, which includes incomplete qualifications, certifications, or any special education received."], "maxLength": 250, "optional": true}, {"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "school", "title": "School", "optional": true}, {"type": "GROUP_CONDITIONAL", "id": "fieldOfStudyDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "fieldOfStudy", "title": "Field of study", "options": {"01": "Education", "02": "Fine and Applied Arts", "03": "Humanities and Social Sciences", "04": "Mass Communications and Information Science", "05": "Business and Administration", "06": "Law", "07": "Natural and Mathematical Sciences", "08": "Health Sciences", "09": "Information Technology", "10": "Architecture, Building and Real Estate", "11": "Engineering Sciences", "12": "Engineering, Manufacturing and Related Trades", "13": "Services", "99": "Other Fields", "XX": "Not Applicable"}, "optional": true}, "result": [{"choice": ["99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "fieldOfStudySpecify", "title": "Tell us about the field of study", "maxLength": 250}]}]}, {"type": "CUSTOM_FIELD", "subType": "DATE_RANGE", "id": "durationOfStudy", "title": "Duration of Study", "description": ["If you are still studying, select only a start date."], "optional": true}, {"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "reasonForLeaving", "title": "Reason for leaving", "description": ["If you left school before graduating, tell us about the situation."], "maxLength": 250, "optional": true}]}}, {"id": "edu_schoolCert", "type": "CUSTOM_FIELD", "title": "Educational qualifications", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "**Certificates** of completion for the education records provided"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}, {"type": "DECORATOR", "title": "Employment details", "subType": "HEADER"}, {"type": "SECTION_CONDITIONAL", "id": "employmentStatusDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "employmentStatus", "title": "Employment status", "options": {"ES1": "Working", "ES2": "Looking for work", "ES3": "Not working"}}, "result": [{"choice": ["ES1"], "member": [{"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation", "optional": true}]}, {"choice": ["ES3"], "member": [{"type": "SECTION_CONDITIONAL", "id": "unemployedSituation", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "situation", "title": "Situation", "options": {"UR4": "Homemaker", "UR1": "Incarcerated", "UR6": "Medical reasons", "UR5": "National serviceman", "UR3": "Student", "UR2": "<PERSON><PERSON><PERSON>", "UR99": "Others"}}, "result": [{"choice": ["UR99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "unemployedSituationSpecify", "title": "Please share more about the situation", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "engagedJobAgency", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "hasEngagedJobAgency", "title": "Have you ever been on the Job Placement and Support Programme?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "engagedJobAgencyDetail", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "engagedJobAgencies", "title": "Which agency have you worked with?", "description": ["You may select more than one option."], "options": [{"A1": "Abilities Beyond Limitations and Expectations (ABLE)"}, {"A6": "APSN"}, {"A2": "Autism Resource Centre (ARC)"}, {"A7": "Enabling Business Hub (EBH)"}, {"A3": "Movement for the Intellectually Disabled Singapore (MINDS)"}, {"A4": "SG Enable (SGE)"}, {"A5": "SPD"}, {"A99": "Others"}]}, "result": [{"choice": ["A99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "engagedJobAgencyOther", "title": "Tell us about the agency", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "personalEmploymentRecords", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "providePersonalEmployment", "title": "Would you like to provide your employment records?", "description": ["Providing employment records allows us to find better matching jobs for you."], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "personalEmployment", "title": "How many employment records would you like to add?", "maxGroup": 10, "header": "Employment records", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "personalEmploymentDetails", "title": "Employment", "member": [{"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "employer", "title": "Company name"}, {"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation"}, {"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "position", "title": "Job title", "optional": true}, {"type": "CUSTOM_FIELD", "subType": "MONEY", "id": "monthlyIncome", "title": "Gross monthly income", "optional": true}, {"type": "CUSTOM_FIELD", "subType": "DATE_RANGE", "id": "durationOfWork", "title": "Duration of work", "description": ["If you are still working in this position, select only a start date."], "optional": true}, {"type": "GROUP_CONDITIONAL", "id": "reasonForLeavingDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "reasonForLeaving", "title": "Reason for leaving", "description": ["You may select more than one option."], "options": {"R1": "Career Planning / Advancement", "R2": "End of Contract / Stint", "R3": "Job fit - Lack of experience for the job", "R4": "Job fit - Lack of skills for the job ", "R5": "Medical / Health Reasons", "R6": "Retrenched / Terminated", "R7": "Work environment / culture", "R8": "Work location is inaccesible or too far", "R9": "Working hours is inflexible or too long", "R99": "Others"}, "optional": true}, "result": [{"choice": ["R99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "reasonForLeavingSpecify", "title": "Tell us about the reason for leaving", "maxLength": 250}]}]}]}}, {"id": "emp_resume", "type": "CUSTOM_FIELD", "title": "Resume", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "Resume"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}, {"type": "DECORATOR", "title": "Declaration", "subType": "HEADER"}, {"type": "SECTION_CONDITIONAL", "id": "convictionInCourt", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "previousConvictionInCourt", "title": "Do you have any previous conviction in court?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "previousConvictionInCourtSpecify", "title": "Tell us about your conviction details", "maxLength": 250}]}]}, {"type": "SECTION_CONDITIONAL", "id": "bankruptcy", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "declarationOfBankruptcy", "title": "Have you ever been declared bankrupt?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "declarationOfBankruptcySpecify", "title": "Tell us about your bankruptcy details", "maxLength": 250}]}]}]}, {"id": "caregiver<PERSON><PERSON><PERSON>", "title": "Guardian / Caregiver", "subtitle": "Tell us about your guardian / caregiver", "member": [{"type": "SECTION_CONDITIONAL", "id": "caregiver<PERSON><PERSON><PERSON>", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "haveLegalGuardianCaregiver", "title": "Do you have guardians / caregivers (including parents)?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "caregiver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "How many guardians / caregivers (including parents)?", "maxGroup": 10, "header": "Guardian / Caregiver details", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "caregiverGuardianDetails", "title": "Guardian / Caregiver", "member": [{"type": "PRESET_FIELD", "subType": "relationshipTypeV5", "id": "relationship", "title": "Relationship to person with disability"}, {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "role", "title": "Role to person with disability", "options": {"LG": "Parent / Legal guardian", "DD": "<PERSON><PERSON> / Deputy", "CG": "Non-legal guardian"}}, {"id": "fullName", "type": "PRESET_FIELD", "subType": "name"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus"}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race"}, {"id": "highestLevelOfEducationAttained", "type": "PRESET_FIELD", "subType": "education"}, {"type": "GROUP_CONDITIONAL", "id": "employmentStatusDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "employmentStatus", "title": "Employment status", "options": {"ES1": "Working", "ES2": "Looking for work", "ES3": "Not working"}}, "result": [{"choice": ["ES1"], "member": [{"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation", "optional": true}]}, {"choice": ["ES3"], "member": [{"type": "GROUP_CONDITIONAL", "id": "unemployedSituation", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "situation", "title": "Situation", "options": {"UR4": "Homemaker", "UR1": "Incarcerated", "UR6": "Medical reasons", "UR5": "National serviceman", "UR3": "Student", "UR2": "<PERSON><PERSON><PERSON>", "UR99": "Others"}}, "result": [{"choice": ["UR99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "unemployedSituationSpecify", "title": "Please share more about the situation", "maxLength": 250}]}]}]}]}, {"type": "DECORATOR", "subType": "HEADER", "title": "Contact details"}, {"type": "PRESET_FIELD", "subType": "mobileNumber", "id": "mobile"}, {"type": "PRESET_FIELD", "subType": "email", "id": "email"}]}}]}]}]}, {"id": "terms", "title": "Terms & Conditions", "subtitle": "Please read and acknowledge the terms and conditions", "member": [{"type": "CUSTOM_GROUP", "subType": "TNC", "id": "consent", "title": "Consent to data sharing", "acknowledge": "I acknowledge and consent to the terms above.", "content": [{"header": "", "indent": [{"paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:", "indent": [{"paragraph": "to verify the identity and relationship of me and my family, for the Services or Schemes;"}, {"paragraph": "to conduct outreach to me and my family to inform us about, and to encourage us to apply for, the Services or Schemes;"}, {"paragraph": "to determine the eligibility of me and my family for the Services or Schemes;"}, {"paragraph": "to provide me and my family with the Services or Schemes;"}, {"paragraph": "to refer me and my family to other Services or Schemes not currently provided to us, if assessed to be useful to me and my family; and"}, {"paragraph": "for data analysis, research, evaluation and policy-making, for the Services or Schemes."}]}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use, and disclose my Personal Information for the purposes stated in Paragraph 1 and any other purpose permitted by law. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that if there are any discrepancies in the Personal Information collected, such discrepancies may be reflected to the relevant Singapore Public Agencies, so that they may take the necessary steps to rectify any inaccurate records relating to me."}, {"paragraph": "My consent shall remain valid until I withdraw it in writing. I accept that it will take up to 10-15 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/onemsf-omnibus-termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}]}]}]}]}, {"id": "parent", "actionLabel": "Apply as a Parent", "section": [{"id": "profile", "title": "Profile", "subtitle": "Tell us about yourself", "member": [{"type": "DECORATOR", "title": "Personal details", "subType": "HEADER"}, {"id": "fullName", "type": "PRESET_FIELD", "subType": "name", "prefillSource": "myInfo.name"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric", "prefillSource": "myInfo.nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob", "prefillSource": "myInfo.dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex", "prefillSource": "myInfo.sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "prefillSource": "myInfo.residentialStatus"}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race", "prefillSource": "myInfo.race"}, {"type": "PRESET_FIELD", "subType": "relationshipTypeV5", "id": "relationship", "title": "Relationship to person with disability"}, {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "role", "title": "Role to person with disability", "options": {"LG": "Parent / Legal guardian", "DD": "<PERSON><PERSON> / Deputy", "CG": "Non-legal guardian"}}, {"id": "highestLevelOfEducationAttained", "type": "PRESET_FIELD", "optional": true, "subType": "education"}, {"type": "SECTION_CONDITIONAL", "id": "employmentStatusDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "employmentStatus", "title": "Employment status", "optional": true, "options": {"ES1": "Working", "ES2": "Looking for work", "ES3": "Not working"}}, "result": [{"choice": ["ES1"], "member": [{"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation", "optional": true}]}, {"choice": ["ES3"], "member": [{"type": "SECTION_CONDITIONAL", "id": "unemployedSituation", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "situation", "title": "Situation", "optional": true, "options": {"UR4": "Homemaker", "UR1": "Incarcerated", "UR6": "Medical reasons", "UR5": "National serviceman", "UR3": "Student", "UR2": "<PERSON><PERSON><PERSON>", "UR99": "Others"}}, "result": [{"choice": ["UR99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "unemployedSituationSpecify", "title": "Please share more about the situation", "optional": true, "maxLength": 250}]}]}]}]}, {"type": "DECORATOR", "title": "Contact details", "subType": "HEADER"}, {"id": "mobile", "type": "PRESET_FIELD", "subType": "mobileNumber", "prefillSource": "myInfo.mobileNumber", "editable": true}, {"id": "otherPhone", "type": "PRESET_FIELD", "subType": "homeNumber", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email", "prefillSource": "myInfo.email", "template": "application", "editable": true}]}, {"id": "disability", "title": "Person with Disability", "subtitle": "Tell us about the person with disability", "member": [{"type": "DECORATOR", "title": "Personal details", "subType": "HEADER"}, {"id": "fullName", "type": "PRESET_FIELD", "title": "Name (as in NRIC)", "subType": "name"}, {"id": "idNo", "type": "PRESET_FIELD", "title": "NRIC", "subType": "nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "excludedOptions": ["A"], "description": ["Only Singapore Citizens or Permanent Residents are eligible."]}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race"}, {"id": "pi_nric", "type": "CUSTOM_FIELD", "title": "NRIC", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "Front and back of the NRIC"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"]}, {"id": "address", "type": "PRESET_FIELD", "subType": "address", "allowOverseas": true}, {"type": "DECORATOR", "title": "Contact details", "subType": "HEADER"}, {"id": "mobile", "type": "PRESET_FIELD", "subType": "mobileNumber"}, {"id": "otherPhone", "type": "PRESET_FIELD", "subType": "homeNumber", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email"}, {"type": "DECORATOR", "title": "Disability condition", "subType": "HEADER"}, {"id": "sh_dcf", "type": "CUSTOM_FIELD", "title": "Disability Verification Form (DVF)", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "optional": true, "instructions": ["Individuals whose disability status **has been verified\\* do not need to submit** the Disability Verification Form.", "Individuals whose disability status has **not been verified**, please upload a copy of:"], "documents": [{"paragraph": "Completed DVF"}], "additionalDetails": ["Please ensure the DVF is endorsed by a registered healthcare professional before proceeding. You can download the DVF [here](https://www.enablingguide.sg/disability-verification).", "*You can check if your disability status is verified at [SupportGoWhere - MSF Disabilty Verification](/grants/pwdr).", "**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"]}, {"type": "SECTION_CONDITIONAL", "id": "mobilityAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useMobilityAids", "title": "Does the person with disability use mobility aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "mobilityAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfMobilityAids", "title": "Which mobility aids does the person with disability use?", "description": ["You may select more than one option."], "options": {"MA1": "Manual wheelchair", "MA2": "Motorised wheelchair", "MA3": "Prosthesis", "MA4": "Quad stick", "MA5": "Walking frame", "MA6": "Walking stick", "MA99": "Others"}}, "result": [{"choice": ["MA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfMobilityAidsSpecify", "title": "Tell us about the mobility aids", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "hearingAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useHearingAids", "title": "Does the person with disability use hearing aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "hearingAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfHearingAids", "title": "Which hearing aids does the person with disability use?", "description": ["You may select more than one option."], "options": {"HA1": "Behind-the-ear hearing aids", "HA2": "In-the-ear hearing aids", "HA3": "Receiver-in-the-ear hearing aids", "HA4": "In-the-canal hearing aids", "HA99": "Others"}}, "result": [{"choice": ["HA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfHearingAidsSpecify", "title": "What type of hearing aids does the person with disability use?", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "visualAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useVisualAids", "title": "Does the person with disability use visual aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "visualAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfVisualAids", "title": "Which visual aids does the person with disability use?", "description": ["You may select more than one option."], "options": {"VA1": "Corrective glasses", "VA2": "Optical devices such as Magnifiers", "VA3": "Screen readers", "VA4": "White Cane", "VA99": "Others"}}, "result": [{"choice": ["VA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfVisualAidsSpecify", "title": "What type of visual aids does the person with disability use?", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "travelIndependently", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "ableToTravelIndependently", "title": "Is the person with disability able to travel independently? ", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "travelIndependentlyDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "abilityToTravelIndependently", "title": "Which modes of transport does the person with disability use?", "description": ["You may select more than one option."], "options": {"MT1": "Bus", "MT2": "Car", "MT3": "MRT", "MT4": "Taxi", "MT99": "Others"}}, "result": [{"choice": ["MT99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "abilityToTravelIndependentlySpecify", "title": "Tell us about the modes of transport", "maxLength": 250}]}]}]}, {"choice": ["1"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "travelAssistanceRequired", "title": "Tell us about the assistance that the person with disability requires when travelling", "maxLength": 250}]}]}, {"type": "SECTION_CONDITIONAL", "id": "modeOfCommunication", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "preferredModeOfCommunication", "title": "Preferred mode of communication", "description": ["You may select more than one option."], "options": {"MC1": "Lip reading", "MC2": "Signing", "MC3": "SMS", "MC4": "Verbal", "MC5": "Written", "MC99": "Others"}}, "result": [{"choice": ["MC99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "preferredModeOfCommunicationSpecify", "title": "Tell us about the preferred mode of communication", "maxLength": 250}]}]}]}, {"id": "educationEmployment", "title": "Education and employment", "subtitle": "Tell us about the person with disability’s education and employment details", "member": [{"type": "DECORATOR", "title": "Education details", "subType": "HEADER"}, {"id": "highestLevelOfEducationAttained", "type": "PRESET_FIELD", "subType": "education"}, {"type": "SECTION_CONDITIONAL", "id": "personalEducationRecords", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "providePersonalEducation", "title": "Would you like to provide the person with disability’s education records?", "description": ["Providing education records allows us to find better matching jobs for the person with disability."], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "personalEducation", "title": "How many education records would you like to add?", "maxGroup": 10, "header": "Education records", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "personalEducationDetails", "title": "Education", "member": [{"id": "educationLevel", "type": "PRESET_FIELD", "subType": "education", "title": "Education level"}, {"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "educationLevelSpecify", "title": "Tell us about the education level", "description": ["Provide relevant details, which includes incomplete qualifications, certifications, or any special education received."], "maxLength": 250, "optional": true}, {"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "school", "title": "School", "optional": true}, {"type": "GROUP_CONDITIONAL", "id": "fieldOfStudyDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "fieldOfStudy", "title": "Field of study", "options": {"01": "Education", "02": "Fine and Applied Arts", "03": "Humanities and Social Sciences", "04": "Mass Communications and Information Science", "05": "Business and Administration", "06": "Law", "07": "Natural and Mathematical Sciences", "08": "Health Sciences", "09": "Information Technology", "10": "Architecture, Building and Real Estate", "11": "Engineering Sciences", "12": "Engineering, Manufacturing and Related Trades", "13": "Services", "99": "Other Fields", "XX": "Not Applicable"}, "optional": true}, "result": [{"choice": ["99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "fieldOfStudySpecify", "title": "Tell us about the field of study", "maxLength": 250}]}]}, {"type": "CUSTOM_FIELD", "subType": "DATE_RANGE", "id": "durationOfStudy", "title": "Duration of Study", "description": ["If the person with disability is still studying, select only a start date."], "optional": true}, {"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "reasonForLeaving", "title": "Reason for leaving", "description": ["If the person with disability left school before graduating, tell us about the situation."], "maxLength": 250, "optional": true}]}}, {"id": "edu_schoolCert", "type": "CUSTOM_FIELD", "title": "Educational qualifications", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "**Certificates** of completion for the education records provided"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}, {"type": "DECORATOR", "title": "Employment details", "subType": "HEADER"}, {"type": "SECTION_CONDITIONAL", "id": "employmentStatusDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "employmentStatus", "title": "Employment status", "options": {"ES1": "Working", "ES2": "Looking for work", "ES3": "Not working"}}, "result": [{"choice": ["ES1"], "member": [{"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation", "optional": true}]}, {"choice": ["ES3"], "member": [{"type": "SECTION_CONDITIONAL", "id": "unemployedSituation", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "situation", "title": "Situation", "options": {"UR4": "Homemaker", "UR1": "Incarcerated", "UR6": "Medical reasons", "UR5": "National serviceman", "UR3": "Student", "UR2": "<PERSON><PERSON><PERSON>", "UR99": "Others"}}, "result": [{"choice": ["UR99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "unemployedSituationSpecify", "title": "Please share more about the situation", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "engagedJobAgency", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "hasEngagedJobAgency", "title": "Has the person with disability ever been on the Job Placement and Support Programme?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "engagedJobAgencyDetail", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "engagedJobAgencies", "title": "Which agency has the person with disability worked with?", "description": ["You may select more than one option."], "options": [{"A1": "Abilities Beyond Limitations and Expectations (ABLE)"}, {"A6": "APSN"}, {"A2": "Autism Resource Centre (ARC)"}, {"A7": "Enabling Business Hub (EBH)"}, {"A3": "Movement for the Intellectually Disabled Singapore (MINDS)"}, {"A4": "SG Enable (SGE)"}, {"A5": "SPD"}, {"A99": "Others"}]}, "result": [{"choice": ["A99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "engagedJobAgencyOther", "title": "Tell us about the agency", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "personalEmploymentRecords", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "providePersonalEmployment", "title": "Would you like to provide the person with disability’s employment records?", "description": ["Providing employment records allows us to find better matching jobs for the person with disability."], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "personalEmployment", "title": "How many employment records would you like to add?", "maxGroup": 10, "header": "Employment records", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "personalEmploymentDetails", "title": "Employment", "member": [{"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "employer", "title": "Company name"}, {"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation"}, {"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "position", "title": "Job title", "optional": true}, {"type": "CUSTOM_FIELD", "subType": "MONEY", "id": "monthlyIncome", "title": "Gross monthly income", "optional": true}, {"type": "CUSTOM_FIELD", "subType": "DATE_RANGE", "id": "durationOfWork", "title": "Duration of work", "description": ["If the person with disability is still working in this position, select only a start date."], "optional": true}, {"type": "GROUP_CONDITIONAL", "id": "reasonForLeavingDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "reasonForLeaving", "title": "Reason for leaving", "description": ["You may select more than one option."], "options": {"R1": "Career Planning / Advancement", "R2": "End of Contract / Stint", "R3": "Job fit - Lack of experience for the job", "R4": "Job fit - Lack of skills for the job ", "R5": "Medical / Health Reasons", "R6": "Retrenched / Terminated", "R7": "Work environment / culture", "R8": "Work location is inaccesible or too far", "R9": "Working hours is inflexible or too long", "R99": "Others"}, "optional": true}, "result": [{"choice": ["R99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "reasonForLeavingSpecify", "title": "Tell us about the reason for leaving", "maxLength": 250}]}]}]}}, {"id": "emp_resume", "type": "CUSTOM_FIELD", "title": "Resume", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "Resume"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}, {"type": "DECORATOR", "title": "Declaration", "subType": "HEADER"}, {"type": "SECTION_CONDITIONAL", "id": "convictionInCourt", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "previousConvictionInCourt", "title": "Does the person with disability have any previous conviction in court?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "previousConvictionInCourtSpecify", "title": "Tell us about the person with disability’s conviction details", "maxLength": 250}]}]}, {"type": "SECTION_CONDITIONAL", "id": "bankruptcy", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "declarationOfBankruptcy", "title": "Has the person with disability ever been declared bankrupt?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "declarationOfBankruptcySpecify", "title": "Tell us about the person with disability’s bankruptcy details", "maxLength": 250}]}]}]}, {"id": "caregiver<PERSON><PERSON><PERSON>", "title": "Guardian / Caregiver", "subtitle": "Tell us about the guardian / caregiver of the person with disability", "member": [{"type": "SECTION_CONDITIONAL", "id": "caregiver<PERSON><PERSON><PERSON>", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "haveLegalGuardianCaregiver", "title": "Does the person with disability have other guardians / caregivers (including parents)?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "caregiver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "How many other guardians / caregivers does the person with disability have, excluding yourself?", "maxGroup": 10, "header": "Guardian / Caregiver details", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "caregiverGuardianDetails", "title": "Guardian / Caregiver", "member": [{"type": "PRESET_FIELD", "subType": "relationshipTypeV5", "id": "relationship", "title": "Relationship to person with disability"}, {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "role", "title": "Role to person with disability", "options": {"LG": "Parent / Legal guardian", "DD": "<PERSON><PERSON> / Deputy", "CG": "Non-legal guardian"}}, {"id": "fullName", "type": "PRESET_FIELD", "subType": "name"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus"}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race"}, {"id": "highestLevelOfEducationAttained", "type": "PRESET_FIELD", "subType": "education", "optional": true}, {"type": "GROUP_CONDITIONAL", "id": "employmentStatusDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "employmentStatus", "title": "Employment status", "options": {"ES1": "Working", "ES2": "Looking for work", "ES3": "Not working"}, "optional": true}, "result": [{"choice": ["ES1"], "member": [{"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation", "optional": true}]}, {"choice": ["ES3"], "member": [{"type": "GROUP_CONDITIONAL", "id": "unemployedSituation", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "situation", "title": "Situation", "options": {"UR4": "Homemaker", "UR1": "Incarcerated", "UR6": "Medical reasons", "UR5": "National serviceman", "UR3": "Student", "UR2": "<PERSON><PERSON><PERSON>", "UR99": "Others"}}, "result": [{"choice": ["UR99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "unemployedSituationSpecify", "title": "Please share more about the situation", "maxLength": 250}]}]}]}]}, {"type": "DECORATOR", "subType": "HEADER", "title": "Contact details"}, {"type": "PRESET_FIELD", "subType": "mobileNumber", "id": "mobile"}, {"type": "PRESET_FIELD", "subType": "email", "id": "email"}]}}]}]}]}, {"id": "terms", "title": "Terms & Conditions", "subtitle": "Please read and acknowledge the terms and conditions", "member": [{"type": "CUSTOM_GROUP", "subType": "TNC", "id": "consent", "title": "Consent to data sharing", "acknowledge": "I acknowledge and consent to the terms above.", "content": [{"header": "I am providing consent as the parent of the applicant who is/are under 21 years old.", "indent": [{"paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:", "indent": [{"paragraph": "to verify the identity and relationship of me and my family, for the Services or Schemes;"}, {"paragraph": "to conduct outreach to me and my family to inform us about, and to encourage us to apply for, the Services or Schemes;"}, {"paragraph": "to determine the eligibility of me and my family for the Services or Schemes;"}, {"paragraph": "to provide me and my family with the Services or Schemes;"}, {"paragraph": "to refer me and my family to other Services or Schemes not currently provided to us, if assessed to be useful to me and my family; and"}, {"paragraph": "for data analysis, research, evaluation and policy-making, for the Services or Schemes."}]}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use, and disclose my Personal Information for the purposes stated in Paragraph 1 and any other purpose permitted by law. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that if there are any discrepancies in the Personal Information collected, such discrepancies may be reflected to the relevant Singapore Public Agencies, so that they may take the necessary steps to rectify any inaccurate records relating to me."}, {"paragraph": "My consent shall remain valid until I withdraw it in writing. I accept that it will take up to 10-15 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/onemsf-omnibus-termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}]}]}, {"type": "DECORATOR", "subType": "HEADER", "title": "Additional documents, if any"}, {"id": "oth_others", "type": "CUSTOM_FIELD", "title": "Other supporting documents", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "Proof of relationship (e.g. birth certificate)"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}, {"id": "guardian", "actionLabel": "Apply as a Legal guardian / Deputy / Donee", "section": [{"id": "profile", "title": "Profile", "subtitle": "Tell us about yourself", "member": [{"type": "DECORATOR", "title": "Personal details", "subType": "HEADER"}, {"id": "fullName", "type": "PRESET_FIELD", "subType": "name", "prefillSource": "myInfo.name"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric", "prefillSource": "myInfo.nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob", "prefillSource": "myInfo.dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex", "prefillSource": "myInfo.sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "prefillSource": "myInfo.residentialStatus"}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race", "prefillSource": "myInfo.race"}, {"type": "PRESET_FIELD", "subType": "relationshipTypeV5", "id": "relationship", "title": "Relationship to person with disability"}, {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "role", "title": "Role to person with disability", "options": {"LG": "Parent / Legal guardian", "DD": "<PERSON><PERSON> / Deputy", "CG": "Non-legal guardian"}}, {"id": "highestLevelOfEducationAttained", "type": "PRESET_FIELD", "optional": true, "subType": "education"}, {"type": "SECTION_CONDITIONAL", "id": "employmentStatusDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "employmentStatus", "title": "Employment status", "optional": true, "options": {"ES1": "Working", "ES2": "Looking for work", "ES3": "Not working"}}, "result": [{"choice": ["ES1"], "member": [{"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation", "optional": true}]}, {"choice": ["ES3"], "member": [{"type": "SECTION_CONDITIONAL", "id": "unemployedSituation", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "situation", "title": "Situation", "optional": true, "options": {"UR4": "Homemaker", "UR1": "Incarcerated", "UR6": "Medical reasons", "UR5": "National serviceman", "UR3": "Student", "UR2": "<PERSON><PERSON><PERSON>", "UR99": "Others"}}, "result": [{"choice": ["UR99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "unemployedSituationSpecify", "title": "Please share more about the situation", "optional": true, "maxLength": 250}]}]}]}]}, {"type": "DECORATOR", "title": "Contact details", "subType": "HEADER"}, {"id": "mobile", "type": "PRESET_FIELD", "subType": "mobileNumber", "prefillSource": "myInfo.mobileNumber", "editable": true}, {"id": "otherPhone", "type": "PRESET_FIELD", "subType": "homeNumber", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email", "prefillSource": "myInfo.email", "template": "application", "editable": true}]}, {"id": "disability", "title": "Person with Disability", "subtitle": "Tell us about the person with disability", "member": [{"type": "DECORATOR", "title": "Personal details", "subType": "HEADER"}, {"id": "fullName", "type": "PRESET_FIELD", "title": "Name (as in NRIC)", "subType": "name"}, {"id": "idNo", "type": "PRESET_FIELD", "title": "NRIC", "subType": "nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "excludedOptions": ["A"], "description": ["Only Singapore Citizens or Permanent Residents are eligible."]}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race"}, {"id": "pi_nric", "type": "CUSTOM_FIELD", "title": "NRIC", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "Front and back of the NRIC"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"]}, {"id": "address", "type": "PRESET_FIELD", "subType": "address", "allowOverseas": true}, {"type": "DECORATOR", "title": "Contact details", "subType": "HEADER"}, {"id": "mobile", "type": "PRESET_FIELD", "subType": "mobileNumber"}, {"id": "otherPhone", "type": "PRESET_FIELD", "subType": "homeNumber", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email"}, {"type": "DECORATOR", "title": "Disability condition", "subType": "HEADER"}, {"id": "sh_dcf", "type": "CUSTOM_FIELD", "title": "Disability Verification Form (DVF)", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "optional": true, "instructions": ["Individuals whose disability status **has been verified\\* do not need to submit** the Disability Verification Form.", "Individuals whose disability status has **not been verified**, please upload a copy of:"], "documents": [{"paragraph": "Completed DVF"}], "additionalDetails": ["Please ensure the DVF is endorsed by a registered healthcare professional before proceeding. You can download the DVF [here](https://www.enablingguide.sg/disability-verification).", "*You can check if your disability status is verified at [SupportGoWhere - MSF Disabilty Verification](/grants/pwdr).", "**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"]}, {"type": "SECTION_CONDITIONAL", "id": "mobilityAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useMobilityAids", "title": "Does the person with disability use mobility aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "mobilityAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfMobilityAids", "title": "Which mobility aids does the person with disability use?", "description": ["You may select more than one option."], "options": {"MA1": "Manual wheelchair", "MA2": "Motorised wheelchair", "MA3": "Prosthesis", "MA4": "Quad stick", "MA5": "Walking frame", "MA6": "Walking stick", "MA99": "Others"}}, "result": [{"choice": ["MA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfMobilityAidsSpecify", "title": "Tell us about the mobility aids", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "hearingAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useHearingAids", "title": "Does the person with disability use hearing aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "hearingAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfHearingAids", "title": "Which hearing aids does the person with disability use?", "description": ["You may select more than one option."], "options": {"HA1": "Behind-the-ear hearing aids", "HA2": "In-the-ear hearing aids", "HA3": "Receiver-in-the-ear hearing aids", "HA4": "In-the-canal hearing aids", "HA99": "Others"}}, "result": [{"choice": ["HA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfHearingAidsSpecify", "title": "What type of hearing aids does the person with disability use?", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "visualAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useVisualAids", "title": "Does the person with disability use visual aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "visualAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfVisualAids", "title": "Which visual aids does the person with disability use?", "description": ["You may select more than one option."], "options": {"VA1": "Corrective glasses", "VA2": "Optical devices such as Magnifiers", "VA3": "Screen readers", "VA4": "White Cane", "VA99": "Others"}}, "result": [{"choice": ["VA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfVisualAidsSpecify", "title": "What type of visual aids does the person with disability use?", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "travelIndependently", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "ableToTravelIndependently", "title": "Is the person with disability able to travel independently? ", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "travelIndependentlyDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "abilityToTravelIndependently", "title": "Which modes of transport does the person with disability use?", "description": ["You may select more than one option."], "options": {"MT1": "Bus", "MT2": "Car", "MT3": "MRT", "MT4": "Taxi", "MT99": "Others"}}, "result": [{"choice": ["MT99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "abilityToTravelIndependentlySpecify", "title": "Tell us about the modes of transport", "maxLength": 250}]}]}]}, {"choice": ["1"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "travelAssistanceRequired", "title": "Tell us about the assistance that the person with disability requires when travelling", "maxLength": 250}]}]}, {"type": "SECTION_CONDITIONAL", "id": "modeOfCommunication", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "preferredModeOfCommunication", "title": "Preferred mode of communication", "description": ["You may select more than one option."], "options": {"MC1": "Lip reading", "MC2": "Signing", "MC3": "SMS", "MC4": "Verbal", "MC5": "Written", "MC99": "Others"}}, "result": [{"choice": ["MC99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "preferredModeOfCommunicationSpecify", "title": "Tell us about the preferred mode of communication", "maxLength": 250}]}]}]}, {"id": "educationEmployment", "title": "Education and employment", "subtitle": "Tell us about the person with disability’s education and employment details", "member": [{"type": "DECORATOR", "title": "Education details", "subType": "HEADER"}, {"id": "highestLevelOfEducationAttained", "type": "PRESET_FIELD", "subType": "education"}, {"type": "SECTION_CONDITIONAL", "id": "personalEducationRecords", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "providePersonalEducation", "title": "Would you like to provide the person with disability’s education records?", "description": ["Providing education records allows us to find better matching jobs for the person with disability."], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "personalEducation", "title": "How many education records would you like to add?", "maxGroup": 10, "header": "Education records", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "personalEducationDetails", "title": "Education", "member": [{"id": "educationLevel", "type": "PRESET_FIELD", "subType": "education", "title": "Education level"}, {"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "educationLevelSpecify", "title": "Tell us about the education level", "description": ["Provide relevant details, which includes incomplete qualifications, certifications, or any special education received."], "maxLength": 250, "optional": true}, {"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "school", "title": "School", "optional": true}, {"type": "GROUP_CONDITIONAL", "id": "fieldOfStudyDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "fieldOfStudy", "title": "Field of study", "options": {"01": "Education", "02": "Fine and Applied Arts", "03": "Humanities and Social Sciences", "04": "Mass Communications and Information Science", "05": "Business and Administration", "06": "Law", "07": "Natural and Mathematical Sciences", "08": "Health Sciences", "09": "Information Technology", "10": "Architecture, Building and Real Estate", "11": "Engineering Sciences", "12": "Engineering, Manufacturing and Related Trades", "13": "Services", "99": "Other Fields", "XX": "Not Applicable"}, "optional": true}, "result": [{"choice": ["99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "fieldOfStudySpecify", "title": "Tell us about the field of study", "maxLength": 250}]}]}, {"type": "CUSTOM_FIELD", "subType": "DATE_RANGE", "id": "durationOfStudy", "title": "Duration of Study", "description": ["If the person with disability is still studying, select only a start date."], "optional": true}, {"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "reasonForLeaving", "title": "Reason for leaving", "description": ["If the person with disability left school before graduating, tell us about the situation."], "maxLength": 250, "optional": true}]}}, {"id": "edu_schoolCert", "type": "CUSTOM_FIELD", "title": "Educational qualifications", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "**Certificates** of completion for the education records provided"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}, {"type": "DECORATOR", "title": "Employment details", "subType": "HEADER"}, {"type": "SECTION_CONDITIONAL", "id": "employmentStatusDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "employmentStatus", "title": "Employment status", "options": {"ES1": "Working", "ES2": "Looking for work", "ES3": "Not working"}}, "result": [{"choice": ["ES1"], "member": [{"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation", "optional": true}]}, {"choice": ["ES3"], "member": [{"type": "SECTION_CONDITIONAL", "id": "unemployedSituation", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "situation", "title": "Situation", "options": {"UR4": "Homemaker", "UR1": "Incarcerated", "UR6": "Medical reasons", "UR5": "National serviceman", "UR3": "Student", "UR2": "<PERSON><PERSON><PERSON>", "UR99": "Others"}}, "result": [{"choice": ["UR99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "unemployedSituationSpecify", "title": "Please share more about the situation", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "engagedJobAgency", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "hasEngagedJobAgency", "title": "Has the person with disability ever been on the Job Placement and Support Programme?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "engagedJobAgencyDetail", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "engagedJobAgencies", "title": "Which agency has the person with disability worked with?", "description": ["You may select more than one option."], "options": [{"A1": "Abilities Beyond Limitations and Expectations (ABLE)"}, {"A6": "APSN"}, {"A2": "Autism Resource Centre (ARC)"}, {"A7": "Enabling Business Hub (EBH)"}, {"A3": "Movement for the Intellectually Disabled Singapore (MINDS)"}, {"A4": "SG Enable (SGE)"}, {"A5": "SPD"}, {"A99": "Others"}]}, "result": [{"choice": ["A99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "engagedJobAgencyOther", "title": "Tell us about the agency", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "personalEmploymentRecords", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "providePersonalEmployment", "title": "Would you like to provide the person with disability’s employment records?", "description": ["Providing employment records allows us to find better matching jobs for the person with disability."], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "personalEmployment", "title": "How many employment records would you like to add?", "maxGroup": 10, "header": "Employment records", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "personalEmploymentDetails", "title": "Employment", "member": [{"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "employer", "title": "Company name"}, {"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation"}, {"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "position", "title": "Job title", "optional": true}, {"type": "CUSTOM_FIELD", "subType": "MONEY", "id": "monthlyIncome", "title": "Gross monthly income", "optional": true}, {"type": "CUSTOM_FIELD", "subType": "DATE_RANGE", "id": "durationOfWork", "title": "Duration of work", "description": ["If the person with disability is still working in this position, select only a start date."], "optional": true}, {"type": "GROUP_CONDITIONAL", "id": "reasonForLeavingDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "reasonForLeaving", "title": "Reason for leaving", "description": ["You may select more than one option."], "options": {"R1": "Career Planning / Advancement", "R2": "End of Contract / Stint", "R3": "Job fit - Lack of experience for the job", "R4": "Job fit - Lack of skills for the job ", "R5": "Medical / Health Reasons", "R6": "Retrenched / Terminated", "R7": "Work environment / culture", "R8": "Work location is inaccesible or too far", "R9": "Working hours is inflexible or too long", "R99": "Others"}, "optional": true}, "result": [{"choice": ["R99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "reasonForLeavingSpecify", "title": "Tell us about the reason for leaving", "maxLength": 250}]}]}]}}, {"id": "emp_resume", "type": "CUSTOM_FIELD", "title": "Resume", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "Resume"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}, {"type": "DECORATOR", "title": "Declaration", "subType": "HEADER"}, {"type": "SECTION_CONDITIONAL", "id": "convictionInCourt", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "previousConvictionInCourt", "title": "Does the person with disability have any previous conviction in court?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "previousConvictionInCourtSpecify", "title": "Tell us about the person with disability’s conviction details", "maxLength": 250}]}]}, {"type": "SECTION_CONDITIONAL", "id": "bankruptcy", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "declarationOfBankruptcy", "title": "Has the person with disability ever been declared bankrupt?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "declarationOfBankruptcySpecify", "title": "Tell us about the person with disability’s bankruptcy details", "maxLength": 250}]}]}]}, {"id": "caregiver<PERSON><PERSON><PERSON>", "title": "Guardian / Caregiver", "subtitle": "Tell us about the guardian / caregiver of the person with disability", "member": [{"type": "SECTION_CONDITIONAL", "id": "caregiver<PERSON><PERSON><PERSON>", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "haveLegalGuardianCaregiver", "title": "Does the person with disability have other guardians / caregivers (including parents)?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "caregiver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "How many other guardians / caregivers does the person with disability have, excluding yourself?", "maxGroup": 10, "header": "Guardian / Caregiver details", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "caregiverGuardianDetails", "title": "Guardian / Caregiver", "member": [{"type": "PRESET_FIELD", "subType": "relationshipTypeV5", "id": "relationship", "title": "Relationship to person with disability"}, {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "role", "title": "Role to person with disability", "options": {"LG": "Parent / Legal guardian", "DD": "<PERSON><PERSON> / Deputy", "CG": "Non-legal guardian"}}, {"id": "fullName", "type": "PRESET_FIELD", "subType": "name"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus"}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race"}, {"id": "highestLevelOfEducationAttained", "type": "PRESET_FIELD", "subType": "education", "optional": true}, {"type": "GROUP_CONDITIONAL", "id": "employmentStatusDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "employmentStatus", "title": "Employment status", "options": {"ES1": "Working", "ES2": "Looking for work", "ES3": "Not working"}, "optional": true}, "result": [{"choice": ["ES1"], "member": [{"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation", "optional": true}]}, {"choice": ["ES3"], "member": [{"type": "GROUP_CONDITIONAL", "id": "unemployedSituation", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "situation", "title": "Situation", "options": {"UR4": "Homemaker", "UR1": "Incarcerated", "UR6": "Medical reasons", "UR5": "National serviceman", "UR3": "Student", "UR2": "<PERSON><PERSON><PERSON>", "UR99": "Others"}}, "result": [{"choice": ["UR99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "unemployedSituationSpecify", "title": "Please share more about the situation", "maxLength": 250}]}]}]}]}, {"type": "DECORATOR", "subType": "HEADER", "title": "Contact details"}, {"type": "PRESET_FIELD", "subType": "mobileNumber", "id": "mobile"}, {"type": "PRESET_FIELD", "subType": "email", "id": "email"}]}}]}]}]}, {"id": "terms", "title": "Terms & Conditions", "subtitle": "Please read and acknowledge the terms and conditions", "member": [{"type": "CUSTOM_GROUP", "subType": "TNC", "id": "consent", "title": "Consent to data sharing", "acknowledge": "I acknowledge and consent to the terms above.", "content": [{"header": "I am providing consent:\n\ni. as the legal guardian of the applicant who is/are under 21 years old; or\n\nii. on behalf of the applicant as Donee(s) acting under a Registered Lasting Power of Attorney granted by the applicant / Deputy(s) appointed by the Court under the Mental Capacity Act 2008 to act on behalf of the applicant.", "indent": [{"paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:", "indent": [{"paragraph": "to verify the identity and relationship of me and my family, for the Services or Schemes;"}, {"paragraph": "to conduct outreach to me and my family to inform us about, and to encourage us to apply for, the Services or Schemes;"}, {"paragraph": "to determine the eligibility of me and my family for the Services or Schemes;"}, {"paragraph": "to provide me and my family with the Services or Schemes;"}, {"paragraph": "to refer me and my family to other Services or Schemes not currently provided to us, if assessed to be useful to me and my family; and"}, {"paragraph": "for data analysis, research, evaluation and policy-making, for the Services or Schemes."}]}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use, and disclose my Personal Information for the purposes stated in Paragraph 1 and any other purpose permitted by law. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that if there are any discrepancies in the Personal Information collected, such discrepancies may be reflected to the relevant Singapore Public Agencies, so that they may take the necessary steps to rectify any inaccurate records relating to me."}, {"paragraph": "My consent shall remain valid until I withdraw it in writing. I accept that it will take up to 10-15 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/onemsf-omnibus-termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}]}]}, {"type": "DECORATOR", "subType": "HEADER", "title": "Additional documents, if any"}, {"id": "oth_others", "type": "CUSTOM_FIELD", "title": "Other supporting documents", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "**For legal guardians**, please upload a copy of:", "indent": [{"paragraph": "Proof of relationship (e.g. guardianship order or voluntary care agreement)"}]}, {"paragraph": "**For Donee(s) or Deputy(s)**, please upload a copy of:", "indent": [{"paragraph": "Registered Lasting Power of Attorney / Order of Court"}]}], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}, {"id": "caregiver", "actionLabel": "Apply as a Non-legal guardian", "section": [{"id": "profile", "title": "Profile", "subtitle": "Tell us about yourself", "member": [{"type": "DECORATOR", "title": "Personal details", "subType": "HEADER"}, {"id": "fullName", "type": "PRESET_FIELD", "subType": "name", "prefillSource": "myInfo.name"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric", "prefillSource": "myInfo.nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob", "prefillSource": "myInfo.dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex", "prefillSource": "myInfo.sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "prefillSource": "myInfo.residentialStatus"}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race", "prefillSource": "myInfo.race"}, {"type": "PRESET_FIELD", "subType": "relationshipTypeV5", "id": "relationship", "title": "Relationship to person with disability"}, {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "role", "title": "Role to person with disability", "options": {"LG": "Parent / Legal guardian", "DD": "<PERSON><PERSON> / Deputy", "CG": "Non-legal guardian"}}, {"id": "highestLevelOfEducationAttained", "type": "PRESET_FIELD", "optional": true, "subType": "education"}, {"type": "SECTION_CONDITIONAL", "id": "employmentStatusDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "employmentStatus", "title": "Employment status", "optional": true, "options": {"ES1": "Working", "ES2": "Looking for work", "ES3": "Not working"}}, "result": [{"choice": ["ES1"], "member": [{"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation", "optional": true}]}, {"choice": ["ES3"], "member": [{"type": "SECTION_CONDITIONAL", "id": "unemployedSituation", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "situation", "title": "Situation", "optional": true, "options": {"UR4": "Homemaker", "UR1": "Incarcerated", "UR6": "Medical reasons", "UR5": "National serviceman", "UR3": "Student", "UR2": "<PERSON><PERSON><PERSON>", "UR99": "Others"}}, "result": [{"choice": ["UR99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "unemployedSituationSpecify", "title": "Please share more about the situation", "optional": true, "maxLength": 250}]}]}]}]}, {"type": "DECORATOR", "title": "Contact details", "subType": "HEADER"}, {"id": "mobile", "type": "PRESET_FIELD", "subType": "mobileNumber", "prefillSource": "myInfo.mobileNumber", "editable": true}, {"id": "otherPhone", "type": "PRESET_FIELD", "subType": "homeNumber", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email", "prefillSource": "myInfo.email", "template": "application", "editable": true}]}, {"id": "disability", "title": "Person with Disability", "subtitle": "Tell us about the person with disability", "member": [{"type": "DECORATOR", "title": "Personal details", "subType": "HEADER"}, {"id": "fullName", "type": "PRESET_FIELD", "title": "Name (as in NRIC)", "subType": "name"}, {"id": "idNo", "type": "PRESET_FIELD", "title": "NRIC", "subType": "nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus", "excludedOptions": ["A"], "description": ["Only Singapore Citizens or Permanent Residents are eligible."]}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race"}, {"id": "pi_nric", "type": "CUSTOM_FIELD", "title": "NRIC", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "Front and back of the NRIC"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"]}, {"id": "address", "type": "PRESET_FIELD", "subType": "address", "allowOverseas": true}, {"type": "DECORATOR", "title": "Contact details", "subType": "HEADER"}, {"id": "mobile", "type": "PRESET_FIELD", "subType": "mobileNumber"}, {"id": "otherPhone", "type": "PRESET_FIELD", "subType": "homeNumber", "optional": true}, {"id": "email", "type": "PRESET_FIELD", "subType": "email"}, {"type": "DECORATOR", "title": "Disability condition", "subType": "HEADER"}, {"id": "sh_dcf", "type": "CUSTOM_FIELD", "title": "Disability Verification Form (DVF)", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "optional": true, "instructions": ["Individuals whose disability status **has been verified\\* do not need to submit** the Disability Verification Form.", "Individuals whose disability status has **not been verified**, please upload a copy of:"], "documents": [{"paragraph": "Completed DVF"}], "additionalDetails": ["Please ensure the DVF is endorsed by a registered healthcare professional before proceeding. You can download the DVF [here](https://www.enablingguide.sg/disability-verification).", "*You can check if your disability status is verified at [SupportGoWhere - MSF Disabilty Verification](/grants/pwdr).", "**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"]}, {"type": "SECTION_CONDITIONAL", "id": "mobilityAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useMobilityAids", "title": "Does the person with disability use mobility aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "mobilityAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfMobilityAids", "title": "Which mobility aids does the person with disability use?", "description": ["You may select more than one option."], "options": {"MA1": "Manual wheelchair", "MA2": "Motorised wheelchair", "MA3": "Prosthesis", "MA4": "Quad stick", "MA5": "Walking frame", "MA6": "Walking stick", "MA99": "Others"}}, "result": [{"choice": ["MA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfMobilityAidsSpecify", "title": "Tell us about the mobility aids", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "hearingAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useHearingAids", "title": "Does the person with disability use hearing aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "hearingAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfHearingAids", "title": "Which hearing aids does the person with disability use?", "description": ["You may select more than one option."], "options": {"HA1": "Behind-the-ear hearing aids", "HA2": "In-the-ear hearing aids", "HA3": "Receiver-in-the-ear hearing aids", "HA4": "In-the-canal hearing aids", "HA99": "Others"}}, "result": [{"choice": ["HA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfHearingAidsSpecify", "title": "What type of hearing aids does the person with disability use?", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "visualAid", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "useVisualAids", "title": "Does the person with disability use visual aids?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "visualAidsDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "usageOfVisualAids", "title": "Which visual aids does the person with disability use?", "description": ["You may select more than one option."], "options": {"VA1": "Corrective glasses", "VA2": "Optical devices such as Magnifiers", "VA3": "Screen readers", "VA4": "White Cane", "VA99": "Others"}}, "result": [{"choice": ["VA99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "usageOfVisualAidsSpecify", "title": "What type of visual aids does the person with disability use?", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "travelIndependently", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "ableToTravelIndependently", "title": "Is the person with disability able to travel independently? ", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "travelIndependentlyDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "abilityToTravelIndependently", "title": "Which modes of transport does the person with disability use?", "description": ["You may select more than one option."], "options": {"MT1": "Bus", "MT2": "Car", "MT3": "MRT", "MT4": "Taxi", "MT99": "Others"}}, "result": [{"choice": ["MT99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "abilityToTravelIndependentlySpecify", "title": "Tell us about the modes of transport", "maxLength": 250}]}]}]}, {"choice": ["1"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "travelAssistanceRequired", "title": "Tell us about the assistance that the person with disability requires when travelling", "maxLength": 250}]}]}, {"type": "SECTION_CONDITIONAL", "id": "modeOfCommunication", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "preferredModeOfCommunication", "title": "Preferred mode of communication", "description": ["You may select more than one option."], "options": {"MC1": "Lip reading", "MC2": "Signing", "MC3": "SMS", "MC4": "Verbal", "MC5": "Written", "MC99": "Others"}}, "result": [{"choice": ["MC99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "preferredModeOfCommunicationSpecify", "title": "Tell us about the preferred mode of communication", "maxLength": 250}]}]}]}, {"id": "educationEmployment", "title": "Education and employment", "subtitle": "Tell us about the person with disability’s education and employment details", "member": [{"type": "DECORATOR", "title": "Education details", "subType": "HEADER"}, {"id": "highestLevelOfEducationAttained", "type": "PRESET_FIELD", "subType": "education"}, {"type": "SECTION_CONDITIONAL", "id": "personalEducationRecords", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "providePersonalEducation", "title": "Would you like to provide the person with disability’s education records?", "description": ["Providing education records allows us to find better matching jobs for the person with disability."], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "personalEducation", "title": "How many education records would you like to add?", "maxGroup": 10, "header": "Education records", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "personalEducationDetails", "title": "Education", "member": [{"id": "educationLevel", "type": "PRESET_FIELD", "subType": "education", "title": "Education level"}, {"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "educationLevelSpecify", "title": "Tell us about the education level", "description": ["Provide relevant details, which includes incomplete qualifications, certifications, or any special education received."], "maxLength": 250, "optional": true}, {"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "school", "title": "School", "optional": true}, {"type": "GROUP_CONDITIONAL", "id": "fieldOfStudyDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "fieldOfStudy", "title": "Field of study", "options": {"01": "Education", "02": "Fine and Applied Arts", "03": "Humanities and Social Sciences", "04": "Mass Communications and Information Science", "05": "Business and Administration", "06": "Law", "07": "Natural and Mathematical Sciences", "08": "Health Sciences", "09": "Information Technology", "10": "Architecture, Building and Real Estate", "11": "Engineering Sciences", "12": "Engineering, Manufacturing and Related Trades", "13": "Services", "99": "Other Fields", "XX": "Not Applicable"}, "optional": true}, "result": [{"choice": ["99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "fieldOfStudySpecify", "title": "Tell us about the field of study", "maxLength": 250}]}]}, {"type": "CUSTOM_FIELD", "subType": "DATE_RANGE", "id": "durationOfStudy", "title": "Duration of Study", "description": ["If the person with disability is still studying, select only a start date."], "optional": true}, {"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "reasonForLeaving", "title": "Reason for leaving", "description": ["If the person with disability left school before graduating, tell us about the situation."], "maxLength": 250, "optional": true}]}}, {"id": "edu_schoolCert", "type": "CUSTOM_FIELD", "title": "Educational qualifications", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "**Certificates** of completion for the education records provided"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}, {"type": "DECORATOR", "title": "Employment details", "subType": "HEADER"}, {"type": "SECTION_CONDITIONAL", "id": "employmentStatusDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "employmentStatus", "title": "Employment status", "options": {"ES1": "Working", "ES2": "Looking for work", "ES3": "Not working"}}, "result": [{"choice": ["ES1"], "member": [{"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation", "optional": true}]}, {"choice": ["ES3"], "member": [{"type": "SECTION_CONDITIONAL", "id": "unemployedSituation", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "situation", "title": "Situation", "options": {"UR4": "Homemaker", "UR1": "Incarcerated", "UR6": "Medical reasons", "UR5": "National serviceman", "UR3": "Student", "UR2": "<PERSON><PERSON><PERSON>", "UR99": "Others"}}, "result": [{"choice": ["UR99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "unemployedSituationSpecify", "title": "Please share more about the situation", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "engagedJobAgency", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "hasEngagedJobAgency", "title": "Has the person with disability ever been on the Job Placement and Support Programme?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "SECTION_CONDITIONAL", "id": "engagedJobAgencyDetail", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "engagedJobAgencies", "title": "Which agency has the person with disability worked with?", "description": ["You may select more than one option."], "options": [{"A1": "Abilities Beyond Limitations and Expectations (ABLE)"}, {"A6": "APSN"}, {"A2": "Autism Resource Centre (ARC)"}, {"A7": "Enabling Business Hub (EBH)"}, {"A3": "Movement for the Intellectually Disabled Singapore (MINDS)"}, {"A4": "SG Enable (SGE)"}, {"A5": "SPD"}, {"A99": "Others"}]}, "result": [{"choice": ["A99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "engagedJobAgencyOther", "title": "Tell us about the agency", "maxLength": 250}]}]}]}]}, {"type": "SECTION_CONDITIONAL", "id": "personalEmploymentRecords", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "providePersonalEmployment", "title": "Would you like to provide the person with disability’s employment records?", "description": ["Providing employment records allows us to find better matching jobs for the person with disability."], "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "personalEmployment", "title": "How many employment records would you like to add?", "maxGroup": 10, "header": "Employment records", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "personalEmploymentDetails", "title": "Employment", "member": [{"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "employer", "title": "Company name"}, {"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation"}, {"type": "CUSTOM_FIELD", "subType": "FREE_TEXT", "id": "position", "title": "Job title", "optional": true}, {"type": "CUSTOM_FIELD", "subType": "MONEY", "id": "monthlyIncome", "title": "Gross monthly income", "optional": true}, {"type": "CUSTOM_FIELD", "subType": "DATE_RANGE", "id": "durationOfWork", "title": "Duration of work", "description": ["If the person with disability is still working in this position, select only a start date."], "optional": true}, {"type": "GROUP_CONDITIONAL", "id": "reasonForLeavingDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "CHECKBOX", "id": "reasonForLeaving", "title": "Reason for leaving", "description": ["You may select more than one option."], "options": {"R1": "Career Planning / Advancement", "R2": "End of Contract / Stint", "R3": "Job fit - Lack of experience for the job", "R4": "Job fit - Lack of skills for the job ", "R5": "Medical / Health Reasons", "R6": "Retrenched / Terminated", "R7": "Work environment / culture", "R8": "Work location is inaccesible or too far", "R9": "Working hours is inflexible or too long", "R99": "Others"}, "optional": true}, "result": [{"choice": ["R99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "reasonForLeavingSpecify", "title": "Tell us about the reason for leaving", "maxLength": 250}]}]}]}}, {"id": "emp_resume", "type": "CUSTOM_FIELD", "title": "Resume", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "Resume"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}, {"type": "DECORATOR", "title": "Declaration", "subType": "HEADER"}, {"type": "SECTION_CONDITIONAL", "id": "convictionInCourt", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "previousConvictionInCourt", "title": "Does the person with disability have any previous conviction in court?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "previousConvictionInCourtSpecify", "title": "Tell us about the person with disability’s conviction details", "maxLength": 250}]}]}, {"type": "SECTION_CONDITIONAL", "id": "bankruptcy", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "declarationOfBankruptcy", "title": "Has the person with disability ever been declared bankrupt?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "declarationOfBankruptcySpecify", "title": "Tell us about the person with disability’s bankruptcy details", "maxLength": 250}]}]}]}, {"id": "caregiver<PERSON><PERSON><PERSON>", "title": "Guardian / Caregiver", "subtitle": "Tell us about the guardian / caregiver of the person with disability", "member": [{"type": "SECTION_CONDITIONAL", "id": "caregiver<PERSON><PERSON><PERSON>", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "haveLegalGuardianCaregiver", "title": "Does the person with disability have other guardians / caregivers (including parents)?", "options": ["Yes", "No"]}, "result": [{"choice": ["0"], "member": [{"type": "MULTI_VALUE", "id": "caregiver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "How many other guardians / caregivers does the person with disability have, excluding yourself?", "maxGroup": 10, "header": "Guardian / Caregiver details", "group": {"type": "CUSTOM_GROUP", "subType": "BLANK", "id": "caregiverGuardianDetails", "title": "Guardian / Caregiver", "member": [{"type": "PRESET_FIELD", "subType": "relationshipTypeV5", "id": "relationship", "title": "Relationship to person with disability"}, {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "role", "title": "Role to person with disability", "options": {"LG": "Parent / Legal guardian", "DD": "<PERSON><PERSON> / Deputy", "CG": "Non-legal guardian"}}, {"id": "fullName", "type": "PRESET_FIELD", "subType": "name"}, {"id": "idNo", "type": "PRESET_FIELD", "subType": "nric"}, {"id": "birthDate", "type": "PRESET_FIELD", "subType": "dob"}, {"id": "sex", "type": "PRESET_FIELD", "subType": "sex"}, {"id": "residentialStatus", "type": "PRESET_FIELD", "subType": "residentialStatus"}, {"id": "primaryRace", "type": "PRESET_FIELD", "subType": "race"}, {"id": "highestLevelOfEducationAttained", "type": "PRESET_FIELD", "subType": "education", "optional": true}, {"type": "GROUP_CONDITIONAL", "id": "employmentStatusDetails", "selector": {"type": "CUSTOM_FIELD", "subType": "RADIO", "id": "employmentStatus", "title": "Employment status", "options": {"ES1": "Working", "ES2": "Looking for work", "ES3": "Not working"}, "optional": true}, "result": [{"choice": ["ES1"], "member": [{"type": "PRESET_FIELD", "subType": "occupation", "id": "occupation", "optional": true}]}, {"choice": ["ES3"], "member": [{"type": "GROUP_CONDITIONAL", "id": "unemployedSituation", "selector": {"type": "CUSTOM_FIELD", "subType": "DROPDOWN", "id": "situation", "title": "Situation", "options": {"UR4": "Homemaker", "UR1": "Incarcerated", "UR6": "Medical reasons", "UR5": "National serviceman", "UR3": "Student", "UR2": "<PERSON><PERSON><PERSON>", "UR99": "Others"}}, "result": [{"choice": ["UR99"], "member": [{"type": "CUSTOM_FIELD", "subType": "MULTILINE_TEXT", "id": "unemployedSituationSpecify", "title": "Please share more about the situation", "maxLength": 250}]}]}]}]}, {"type": "DECORATOR", "subType": "HEADER", "title": "Contact details"}, {"type": "PRESET_FIELD", "subType": "mobileNumber", "id": "mobile"}, {"type": "PRESET_FIELD", "subType": "email", "id": "email"}]}}]}]}]}, {"id": "terms", "title": "Terms & Conditions", "subtitle": "Please read and acknowledge the terms and conditions", "member": [{"type": "CUSTOM_GROUP", "subType": "TNC", "id": "consent", "title": "Consent to data sharing", "acknowledge": "I acknowledge and consent to the terms above.", "content": [{"header": "I am acknowledging the terms on behalf of the applicant as a family member/caregiver.", "indent": [{"paragraph": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:", "indent": [{"paragraph": "to verify the identity and relationship of me and my family, for the Services or Schemes;"}, {"paragraph": "to conduct outreach to me and my family to inform us about, and to encourage us to apply for, the Services or Schemes;"}, {"paragraph": "to determine the eligibility of me and my family for the Services or Schemes;"}, {"paragraph": "to provide me and my family with the Services or Schemes;"}, {"paragraph": "to refer me and my family to other Services or Schemes not currently provided to us, if assessed to be useful to me and my family; and"}, {"paragraph": "for data analysis, research, evaluation and policy-making, for the Services or Schemes."}]}, {"paragraph": "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use, and disclose my Personal Information for the purposes stated in Paragraph 1 and any other purpose permitted by law. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that if there are any discrepancies in the Personal Information collected, such discrepancies may be reflected to the relevant Singapore Public Agencies, so that they may take the necessary steps to rectify any inaccurate records relating to me."}, {"paragraph": "My consent shall remain valid until I withdraw it in writing. I accept that it will take up to 10-15 working days from the date of receipt before the withdrawal of consent takes place."}, {"paragraph": "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/onemsf-omnibus-termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form."}]}]}, {"type": "DECORATOR", "subType": "HEADER", "title": "Additional documents, if any"}, {"id": "oth_others", "type": "CUSTOM_FIELD", "title": "Other supporting documents", "subType": "FILE_UPLOAD", "maxFileSizeMb": 2, "documents": [{"paragraph": "Proof of relationship"}, {"paragraph": "Doctor’s certification that client has mental incapacity"}], "instructions": ["Please upload a copy of:"], "additionalDetails": ["**File Requirements**\nAccepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG\nMax. file size: 2 MB per file"], "optional": true}]}]}], "fileUploadExternal": {"url": "https://www.ccube.gov.sg/ccube-si-rsh/api/sgw/app-gen/upload-file/v1"}}
{"actionConfig": {"cancel": {"redirectUrl": "https://www.dev.lifesg.io?cancel"}, "logout": {"redirectUrl": "https://www.dev.lifesg.io?logout"}, "failure": {"webhook": "https://www.dev.lifesg.io/icecap/api/mock/webhook/failure/200", "redirectUrl": "https://www.dev.lifesg.io/get-in-touch"}, "success": {"webhook": "https://webhook.site/963a8b03-324a-4335-ab85-5aeaa9c0bb70", "redirectUrl": "https://www.dev.lifesg.io?success"}}, "copyConfig": {"backButton": {"label": "Back", "showIcon": true}, "nextButton": {"label": "Continue", "showIcon": true}}, "behaviourConfig": {"pageActionConfig": {"enableBack": true, "enableCancel": false}}, "sessionDuration": 30, "sessionDataLifetime": 1, "flowActions": [{"type": "ui", "name": "Dashboard", "key": "dashboard", "hasAuthRequirement": true, "runConditions": null, "schema": [{"title": "IHL Internship Programme", "description": "Supported by SG Enable", "schema": {"defaultValues": {"myInfoUsed": true}, "sections": {"section": {"uiType": "section", "layoutType": "grid", "children": {"header": {"children": "New application", "uiType": "text-h3", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}}, "alert-warning": {"children": "<p>The application may take 30 mins to complete. </p> </br> <p> Please ensure that you have all the <a href=\"https://www.google.com\" rel=\"noopener noreferrer\" target=\"_blank\">required documents</a> ready as we do not save a draft for this application. </p>", "type": "warning", "uiType": "alert", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}}, "myInfoUsed": {"uiType": "hidden-field", "valueType": "boolean"}}}}}}]}, {"type": "ui", "name": "Profile section", "key": "ui-profile", "hasAuthRequirement": true, "runConditions": null, "schema": [{"title": "Profile", "description": "Tell us about yourself", "progressIndicator": {"section": "profile"}, "schema": {"sections": {"section": {"uiType": "section", "layoutType": "grid", "children": {"fullName": {"label": "Name (as in NRIC or FIN)", "uiType": "text-field", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"matches": "/^[A-Za-z0-9 ,'/()@%~\\.-]*$/", "errorMessage": "Use only letters, numbers, spaces and the following characters: @ . , ' ( ) - / \\ ~ %"}, {"max": 66}]}, "idNo": {"label": "NRIC or FIN", "uiType": "text-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"uinfin": true, "errorMessage": "Enter a valid NRIC/FIN."}]}, "residentialStatus": {"label": "Residential status", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Singapore citizen", "value": "C"}, {"label": "Permanent Resident (PR)", "value": "P"}, {"label": "Foreigner", "value": "A"}], "validation": [{"required": true}]}, "birthDate": {"label": "Date of birth", "uiType": "date-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"notFuture": true, "errorMessage": "No future dates"}]}, "primaryRace": {"label": "Race", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Chinese", "value": "CN"}, {"label": "Malay", "value": "MY"}, {"label": "Indian", "value": "IN"}, {"label": "Achehnese", "value": "AJ"}, {"label": "Afghan", "value": "AG"}, {"label": "African", "value": "AF"}, {"label": "Albanian", "value": "AL"}, {"label": "Ambonese", "value": "AO"}, {"label": "American", "value": "US"}, {"label": "Amerindian", "value": "AD"}, {"label": "Anglo Saxon", "value": "AX"}, {"label": "Arab", "value": "AR"}, {"label": "Armenian", "value": "AM"}, {"label": "Aryan", "value": "AY"}, {"label": "Assami", "value": "AS"}, {"label": "Australian", "value": "AU"}, {"label": "Austrian", "value": "AT"}, {"label": "Baja<PERSON>", "value": "BF"}, {"label": "Balinese", "value": "BM"}, {"label": "Bangladeshi", "value": "BD"}, {"label": "Banjarese", "value": "BJ"}, {"label": "Basque", "value": "BQ"}, {"label": "Batak", "value": "BA"}, {"label": "Belgian", "value": "BE"}, {"label": "Bengali", "value": "BI"}, {"label": "<PERSON><PERSON>", "value": "BW"}, {"label": "Bhutanese", "value": "BN"}, {"label": "Bidayuh", "value": "DH"}, {"label": "<PERSON><PERSON><PERSON>", "value": "BS"}, {"label": "Bosniak", "value": "BV"}, {"label": "Boyanese", "value": "BY"}, {"label": "Brazilian", "value": "BZ"}, {"label": "British", "value": "BT"}, {"label": "<PERSON><PERSON><PERSON>", "value": "BG"}, {"label": "Bulgarian", "value": "BB"}, {"label": "<PERSON><PERSON><PERSON>", "value": "BH"}, {"label": "Burmese", "value": "BU"}, {"label": "Butonese", "value": "BC"}, {"label": "Cambodian", "value": "CD"}, {"label": "Canadian", "value": "CB"}, {"label": "Cape Coloured", "value": "CC"}, {"label": "Caucasian", "value": "CA"}, {"label": "Ceylonese", "value": "CE"}, {"label": "<PERSON><PERSON><PERSON>", "value": "CJ"}, {"label": "Cornish", "value": "CF"}, {"label": "Creole", "value": "CG"}, {"label": "Croat", "value": "CI"}, {"label": "Czech", "value": "CZ"}, {"label": "<PERSON>", "value": "DA"}, {"label": "<PERSON><PERSON>", "value": "DY"}, {"label": "<PERSON><PERSON>", "value": "DS"}, {"label": "Dutch", "value": "DU"}, {"label": "Egyptian", "value": "EY"}, {"label": "English", "value": "EL"}, {"label": "Ethiopian", "value": "ET"}, {"label": "Eurasian", "value": "EU"}, {"label": "Fijian", "value": "FJ"}, {"label": "Filipino", "value": "PH"}, {"label": "<PERSON>", "value": "FI"}, {"label": "Flemish", "value": "FM"}, {"label": "French", "value": "FR"}, {"label": "German", "value": "GM"}, {"label": "Ghanaian", "value": "GH"}, {"label": "Goan", "value": "GA"}, {"label": "Greek", "value": "GR"}, {"label": "Gujarati", "value": "GE"}, {"label": "Gurkha", "value": "GK"}, {"label": "Haitian", "value": "HA"}, {"label": "Hawaiian", "value": "HW"}, {"label": "Hindustani", "value": "HT"}, {"label": "Hispanic", "value": "HI"}, {"label": "Hungarian", "value": "HU"}, {"label": "<PERSON><PERSON>", "value": "IB"}, {"label": "<PERSON><PERSON>", "value": "IS"}, {"label": "Indonesian", "value": "ID"}, {"label": "Inuit", "value": "IU"}, {"label": "Iranian", "value": "IA"}, {"label": "Iraqi", "value": "IQ"}, {"label": "Irish", "value": "IR"}, {"label": "Israeli", "value": "IL"}, {"label": "Italian", "value": "IT"}, {"label": "<PERSON><PERSON><PERSON>", "value": "JK"}, {"label": "Jamaican", "value": "JM"}, {"label": "Japanese", "value": "JP"}, {"label": "Javanese", "value": "JA"}, {"label": "Jew", "value": "JW"}, {"label": "<PERSON><PERSON>", "value": "JO"}, {"label": "<PERSON><PERSON>", "value": "KA"}, {"label": "Kadazan", "value": "KZ"}, {"label": "<PERSON>", "value": "KN"}, {"label": "<PERSON><PERSON>", "value": "KC"}, {"label": "<PERSON><PERSON>", "value": "KY"}, {"label": "<PERSON><PERSON>", "value": "KD"}, {"label": "Kazakh", "value": "KK"}, {"label": "Kelabit", "value": "KL"}, {"label": "Kenyah", "value": "KI"}, {"label": "Kenyan", "value": "KE"}, {"label": "<PERSON><PERSON><PERSON>", "value": "KB"}, {"label": "Khmer", "value": "KH"}, {"label": "<PERSON><PERSON>", "value": "KF"}, {"label": "Korean", "value": "KR"}, {"label": "Kyrgyz", "value": "KG"}, {"label": "Lao", "value": "LA"}, {"label": "Latin", "value": "LT"}, {"label": "Latvian", "value": "LV"}, {"label": "Lebanese", "value": "LB"}, {"label": "Libyan", "value": "LY"}, {"label": "Li<PERSON>", "value": "LS"}, {"label": "Lithuanian", "value": "LI"}, {"label": "<PERSON>er", "value": "LX"}, {"label": "<PERSON><PERSON><PERSON>", "value": "MA"}, {"label": "Mahratta", "value": "MH"}, {"label": "Makasarese", "value": "MK"}, {"label": "<PERSON><PERSON><PERSON>", "value": "MB"}, {"label": "Malagasy", "value": "MG"}, {"label": "Malayalee", "value": "MM"}, {"label": "Maldivian", "value": "ML"}, {"label": "Maltese", "value": "MT"}, {"label": "Manipuri", "value": "MP"}, {"label": "<PERSON><PERSON>", "value": "MI"}, {"label": "Marathi", "value": "MR"}, {"label": "<PERSON><PERSON><PERSON>", "value": "MU"}, {"label": "Melanau", "value": "MZ"}, {"label": "Melanesian", "value": "MN"}, {"label": "Mexican", "value": "MX"}, {"label": "Minangkabau", "value": "ME"}, {"label": "Moldavian", "value": "MD"}, {"label": "Mon", "value": "MV"}, {"label": "Mongolian", "value": "MO"}, {"label": "Moroccan", "value": "MW"}, {"label": "<PERSON><PERSON><PERSON>", "value": "MJ"}, {"label": "Naga", "value": "NA"}, {"label": "Nauruan", "value": "NR"}, {"label": "Nepalese", "value": "NP"}, {"label": "New Zealander", "value": "NZ"}, {"label": "Newar", "value": "NW"}, {"label": "Nigerian", "value": "NI"}, {"label": "Norwegian", "value": "NO"}, {"label": "Others", "value": "XX"}, {"label": "Pakistani", "value": "PK"}, {"label": "Palestine", "value": "PB"}, {"label": "Parsee", "value": "PS"}, {"label": "<PERSON><PERSON>", "value": "PN"}, {"label": "<PERSON><PERSON>", "value": "PA"}, {"label": "Persian", "value": "PE"}, {"label": "Peruvian", "value": "PR"}, {"label": "Pole", "value": "PL"}, {"label": "Polynesian", "value": "PY"}, {"label": "Portuguese", "value": "PO"}, {"label": "Punan", "value": "PU"}, {"label": "Punjabi", "value": "PJ"}, {"label": "Rajput", "value": "RJ"}, {"label": "<PERSON><PERSON><PERSON>", "value": "RK"}, {"label": "Romanian", "value": "RO"}, {"label": "Russian", "value": "RU"}, {"label": "Samoan", "value": "SG"}, {"label": "Scot", "value": "SC"}, {"label": "Serbia/Montengerin", "value": "SF"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "SY"}, {"label": "<PERSON>", "value": "SH"}, {"label": "Sikh", "value": "SK"}, {"label": "Sindhi", "value": "SD"}, {"label": "Sinhalese", "value": "SI"}, {"label": "Slavic", "value": "SQ"}, {"label": "Slovak", "value": "SL"}, {"label": "Somali", "value": "SO"}, {"label": "Spanish", "value": "ES"}, {"label": "Sri Lankan", "value": "LK"}, {"label": "Sudanese", "value": "SU"}, {"label": "Sumatran", "value": "SM"}, {"label": "Sundanese", "value": "SS"}, {"label": "Swede", "value": "SE"}, {"label": "Swiss", "value": "CH"}, {"label": "Tajik", "value": "TJ"}, {"label": "Tamil", "value": "TM"}, {"label": "Telugu", "value": "TE"}, {"label": "Thai", "value": "TH"}, {"label": "Tibetan", "value": "TI"}, {"label": "Timor", "value": "TP"}, {"label": "Tongan", "value": "TO"}, {"label": "Turk", "value": "TR"}, {"label": "Turkmen", "value": "TN"}, {"label": "Ukrainian", "value": "UR"}, {"label": "Uyghur", "value": "UY"}, {"label": "Uzbek", "value": "UZ"}, {"label": "Venezuelan", "value": "VE"}, {"label": "Vietnamese", "value": "VN"}, {"label": "Welsh", "value": "WE"}, {"label": "Yugoslav", "value": "YU"}, {"label": "Zimbabwean", "value": "ZW"}], "validation": [{"required": true}]}, "sex": {"label": "Sex", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Female", "value": "F"}, {"label": "Male", "value": "M"}], "validation": [{"required": true}]}, "address-header": {"children": "Residential address", "uiType": "text-h3", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}}, "country": {"label": "Country", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "validation": [{"required": true}], "options": [{"label": "Singapore", "value": "SG"}, {"label": "Afghanistan", "value": "AF"}, {"label": "Albania", "value": "AL"}, {"label": "Algeria", "value": "DZ"}, {"label": "American Samoa", "value": "AS"}, {"label": "Andorra", "value": "AD"}, {"label": "Angola", "value": "AO"}, {"label": "<PERSON><PERSON><PERSON>", "value": "AI"}, {"label": "Antigua", "value": "AG"}, {"label": "Argentina", "value": "AR"}, {"label": "Armenia", "value": "AM"}, {"label": "Aruba", "value": "AB"}, {"label": "Australia", "value": "AU"}, {"label": "Austria", "value": "AT"}, {"label": "Azerbaijan", "value": "AZ"}, {"label": "Bahamas", "value": "BS"}, {"label": "Bahrain", "value": "BH"}, {"label": "Bangladesh", "value": "BD"}, {"label": "Barbados", "value": "BB"}, {"label": "Belarus", "value": "BL"}, {"label": "Belgium", "value": "BE"}, {"label": "Belize", "value": "BZ"}, {"label": "Benin", "value": "BJ"}, {"label": "Bermuda", "value": "BM"}, {"label": "Bhutan", "value": "BT"}, {"label": "Bolivia", "value": "BO"}, {"label": "Bosnia-Herzegovina", "value": "BA"}, {"label": "Botswana", "value": "BW"}, {"label": "Brazil", "value": "BR"}, {"label": "British Antarctic Territory", "value": "BQ"}, {"label": "British Indian Ocean Territory", "value": "IO"}, {"label": "British Virgin Islands", "value": "VG"}, {"label": "Brunei", "value": "BN"}, {"label": "Bulgaria", "value": "BG"}, {"label": "Burkina Faso", "value": "BF"}, {"label": "Burundi", "value": "BI"}, {"label": "Cambodia", "value": "KA"}, {"label": "Cameroon", "value": "CM"}, {"label": "Canada", "value": "CA"}, {"label": "Canton & Enderbury Islands", "value": "CT"}, {"label": "Cape Verde", "value": "CV"}, {"label": "Cayman Islands", "value": "KY"}, {"label": "Central African Republic", "value": "CF"}, {"label": "Chad", "value": "TD"}, {"label": "Channel Islands", "value": "CD"}, {"label": "Chile", "value": "CL"}, {"label": "China", "value": "CN"}, {"label": "Christmas Island", "value": "CX"}, {"label": "Cocos Keeling Island", "value": "CC"}, {"label": "Colombia", "value": "CO"}, {"label": "Comoros", "value": "KM"}, {"label": "Congo", "value": "CG"}, {"label": "Cook Islands", "value": "CK"}, {"label": "Costa Rica", "value": "CR"}, {"label": "Croatia", "value": "CB"}, {"label": "Cuba", "value": "CU"}, {"label": "Cyprus", "value": "CY"}, {"label": "Czech Republic", "value": "CZ"}, {"label": "D.P.R. Korea", "value": "KP"}, {"label": "Democratic Republic Of Congo", "value": "ZR"}, {"label": "Denmark", "value": "DK"}, {"label": "Djibouti", "value": "DJ"}, {"label": "Dominica", "value": "DM"}, {"label": "Dominican Republic", "value": "DO"}, {"label": "East Timor", "value": "TP"}, {"label": "Ecuador", "value": "EC"}, {"label": "Egypt", "value": "EG"}, {"label": "El Salvador", "value": "SV"}, {"label": "Equatorial Guinea", "value": "GQ"}, {"label": "Eritrea", "value": "ER"}, {"label": "Estonia", "value": "EN"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "EW"}, {"label": "Ethiopia", "value": "ET"}, {"label": "Faeroe Islands", "value": "FO"}, {"label": "Falkland Islands", "value": "FK"}, {"label": "Fiji", "value": "FJ"}, {"label": "Finland", "value": "FI"}, {"label": "France", "value": "FR"}, {"label": "French Guiana", "value": "GF"}, {"label": "French Polynesia", "value": "PF"}, {"label": "French Southern & Antarctic Territories", "value": "FQ"}, {"label": "Gabon", "value": "GA"}, {"label": "Gambia", "value": "GM"}, {"label": "Gaza", "value": "GZ"}, {"label": "Georgia", "value": "GO"}, {"label": "Germany", "value": "DG"}, {"label": "Ghana", "value": "GH"}, {"label": "Gibraltar", "value": "GI"}, {"label": "Greece", "value": "GR"}, {"label": "Greenland", "value": "GL"}, {"label": "Grenada", "value": "GD"}, {"label": "Guadeloupe", "value": "GP"}, {"label": "Guam", "value": "GU"}, {"label": "Guatemala", "value": "GT"}, {"label": "Guernsey", "value": "GK"}, {"label": "Guinea", "value": "GN"}, {"label": "Guinea-Bissau", "value": "GW"}, {"label": "Guyana", "value": "GY"}, {"label": "Haiti", "value": "HT"}, {"label": "Heard Island & McDonald Islands", "value": "HM"}, {"label": "Honduras", "value": "HN"}, {"label": "Hong Kong", "value": "HK"}, {"label": "Hong Kong SAR", "value": "HS"}, {"label": "Hungary", "value": "HU"}, {"label": "Iceland", "value": "IS"}, {"label": "India", "value": "IN"}, {"label": "Indonesia", "value": "ID"}, {"label": "Iran", "value": "IR"}, {"label": "Iraq", "value": "IQ"}, {"label": "Ireland", "value": "IE"}, {"label": "Isle Of Man", "value": "MM"}, {"label": "Israel", "value": "IL"}, {"label": "Italy", "value": "IT"}, {"label": "Ivory Coast", "value": "CI"}, {"label": "Jamaica", "value": "JM"}, {"label": "Japan", "value": "JP"}, {"label": "Johnston Island", "value": "JT"}, {"label": "Jordan", "value": "JO"}, {"label": "Kazakhstan", "value": "KZ"}, {"label": "Kenya", "value": "KE"}, {"label": "Kirghizia", "value": "KG"}, {"label": "Kiribati", "value": "KI"}, {"label": "Korea, South", "value": "KR"}, {"label": "Kosovo", "value": "KV"}, {"label": "Kuwait", "value": "KW"}, {"label": "Kyrgyzstan", "value": "KS"}, {"label": "Laos", "value": "LA"}, {"label": "Latvia", "value": "LV"}, {"label": "Lebanon", "value": "LB"}, {"label": "Lesotho", "value": "LS"}, {"label": "Liberia", "value": "LR"}, {"label": "Libya", "value": "LY"}, {"label": "Liechtenstein", "value": "LI"}, {"label": "Lithuania", "value": "LH"}, {"label": "Luxembourg", "value": "LU"}, {"label": "Macao", "value": "MO"}, {"label": "Macao SAR", "value": "MF"}, {"label": "Macedonia", "value": "MB"}, {"label": "Madagascar", "value": "MG"}, {"label": "Malawi", "value": "MW"}, {"label": "Malaysia", "value": "MY"}, {"label": "Maldives", "value": "MV"}, {"label": "Mali", "value": "ML"}, {"label": "Malta", "value": "MT"}, {"label": "Marshall Islands", "value": "MH"}, {"label": "Martinique", "value": "MQ"}, {"label": "Mauritania", "value": "MR"}, {"label": "Mauritius", "value": "MU"}, {"label": "Mayotte", "value": "ME"}, {"label": "Mexico", "value": "MX"}, {"label": "Midway Islands", "value": "MI"}, {"label": "Moldova", "value": "MD"}, {"label": "Monaco", "value": "MC"}, {"label": "Mongolia", "value": "MN"}, {"label": "Montenegro", "value": "MJ"}, {"label": "Montserrat", "value": "MS"}, {"label": "Morocco", "value": "MA"}, {"label": "Mozambique", "value": "MZ"}, {"label": "Myanmar", "value": "BU"}, {"label": "Namibia", "value": "NA"}, {"label": "Nauru", "value": "NR"}, {"label": "Nepal", "value": "NP"}, {"label": "Netherlands", "value": "NL"}, {"label": "Netherlands Antilles", "value": "AN"}, {"label": "New Caledonia", "value": "NC"}, {"label": "New Zealand", "value": "NZ"}, {"label": "Nicaragua", "value": "NI"}, {"label": "Niger", "value": "NE"}, {"label": "Nigeria", "value": "NG"}, {"label": "Niue Island", "value": "NU"}, {"label": "Norfolk Island", "value": "NF"}, {"label": "North Macedonia", "value": "NM"}, {"label": "Norway", "value": "NO"}, {"label": "Oman", "value": "OM"}, {"label": "Others", "value": "XX"}, {"label": "Pacific Island Trust Territory", "value": "PC"}, {"label": "Pakistan", "value": "PK"}, {"label": "<PERSON><PERSON>", "value": "PW"}, {"label": "Palestine", "value": "PB"}, {"label": "Panama", "value": "PA"}, {"label": "Panama Canal Zone", "value": "PZ"}, {"label": "Papua New Guinea", "value": "PG"}, {"label": "Paraguay", "value": "PY"}, {"label": "Peru", "value": "PE"}, {"label": "Philippines", "value": "PH"}, {"label": "Pitcairn Island", "value": "PN"}, {"label": "Poland", "value": "PL"}, {"label": "Portugal", "value": "PT"}, {"label": "Puerto Rico", "value": "PR"}, {"label": "Qatar", "value": "QA"}, {"label": "Reunion", "value": "RE"}, {"label": "Romania", "value": "RO"}, {"label": "Russia", "value": "RF"}, {"label": "Rwanda", "value": "RW"}, {"label": "Samoa", "value": "WM"}, {"label": "San Marino", "value": "SM"}, {"label": "Sao Tome & Principe", "value": "ST"}, {"label": "Saudi Arabia", "value": "SA"}, {"label": "Senegal", "value": "SN"}, {"label": "Serbia", "value": "RS"}, {"label": "Serbia/Montenegro", "value": "SF"}, {"label": "Seychelles", "value": "SC"}, {"label": "Sierra Leone", "value": "SL"}, {"label": "Slovak Republic", "value": "SK"}, {"label": "Slovenia", "value": "SI"}, {"label": "Solomon Islands", "value": "SB"}, {"label": "Somalia", "value": "SO"}, {"label": "South Africa", "value": "ZA"}, {"label": "South Sudan", "value": "SX"}, {"label": "Spain", "value": "ES"}, {"label": "Sri Lanka", "value": "LK"}, {"label": "St. Helena", "value": "SH"}, {"label": "<PERSON>. <PERSON>-N<PERSON>is", "value": "KN"}, {"label": "St. Lucia", "value": "LC"}, {"label": "St. Pierre & Miquelon", "value": "PM"}, {"label": "St. Vincent", "value": "VC"}, {"label": "Sudan", "value": "SD"}, {"label": "Suriname", "value": "SR"}, {"label": "Swaziland", "value": "SZ"}, {"label": "Sweden", "value": "SE"}, {"label": "Switzerland", "value": "CH"}, {"label": "Syria", "value": "SY"}, {"label": "Taiwan", "value": "TW"}, {"label": "Tajikistan", "value": "TI"}, {"label": "Tanzania", "value": "TZ"}, {"label": "Thailand", "value": "TH"}, {"label": "Timor", "value": "TE"}, {"label": "Togo", "value": "TG"}, {"label": "Tokelau Islands", "value": "TK"}, {"label": "Tonga", "value": "TO"}, {"label": "Trinidad & Tobago", "value": "TT"}, {"label": "Tunisia", "value": "TN"}, {"label": "Türkiye", "value": "TR"}, {"label": "Turkmenistan", "value": "TM"}, {"label": "Turks & Caicos Islands", "value": "TC"}, {"label": "Tuvalu", "value": "TV"}, {"label": "Upper Volta", "value": "HV"}, {"label": "Uganda", "value": "UG"}, {"label": "Ukraine", "value": "UR"}, {"label": "United Arab Emirates", "value": "AE"}, {"label": "United Kingdom", "value": "GB"}, {"label": "United States", "value": "US"}, {"label": "Unknown", "value": "UN"}, {"label": "Uruguay", "value": "UY"}, {"label": "US Virgin Islands", "value": "VI"}, {"label": "Uzbekistan", "value": "UZ"}, {"label": "Vanuatu", "value": "VU"}, {"label": "Vatican City State", "value": "VA"}, {"label": "Venezuela", "value": "VE"}, {"label": "Vietnam", "value": "VN"}, {"label": "Wake Island", "value": "WK"}, {"label": "Wallis and Futuna", "value": "WF"}, {"label": "Western Sahara", "value": "EH"}, {"label": "Yemen", "value": "YM"}, {"label": "Yugoslavia", "value": "YU"}, {"label": "Zambia", "value": "ZM"}, {"label": "Zimbabwe", "value": "ZW"}]}, "block": {"label": "Block or house number", "uiType": "text-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"matches": "/^[A-Za-z0-9]*$/", "errorMessage": "Use only letters and numbers."}, {"max": 10}], "showIf": [{"country": [{"filled": true}, {"equals": "SG"}]}]}, "street": {"label": "Street", "uiType": "text-field", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"matches": "/^[A-Za-z0-9 .,'():;\\-/?@]*$/", "errorMessage": "Use only letters, numbers, spaces and the following characters: . , ' ( ) : ; - / ? @"}, {"max": 32}], "showIf": [{"country": [{"filled": true}, {"equals": "SG"}]}]}, "building": {"label": "Building name (optional)", "uiType": "text-field", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "validation": [{"matches": "/^[A-Za-z0-9 .,'():;\\-/?@]*$/", "errorMessage": "Use only letters, numbers, spaces and the following characters: . , ' ( ) : ; - / ? @"}, {"max": 66}], "showIf": [{"country": [{"filled": true}, {"equals": "SG"}]}]}, "levelUnit": {"label": "Floor and unit number (optional)", "uiType": "unit-number-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"unitNumberFormat": true, "errorMessage": "Please enter a valid unit number"}], "showIf": [{"country": [{"filled": true}, {"equals": "SG"}]}]}, "postalCode": {"label": "Postal code", "uiType": "text-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "maxLength": 6, "validation": [{"required": true}, {"integer": true, "errorMessage": "Use numbers only."}, {"length": 6, "errorMessage": "Enter 6 digits."}], "showIf": [{"country": [{"filled": true}, {"equals": "SG"}]}]}, "addressLine1": {"label": "Address line 1", "uiType": "text-field", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"matches": "/^[A-Za-z0-9 .,'():;\\-/?@]*$/", "errorMessage": "Use only letters, numbers, spaces and the following characters: . , ' ( ) : ; - / ? @"}, {"max": 256}], "showIf": [{"country": [{"filled": true}, {"notEquals": "SG"}]}]}, "addressLine2": {"label": "Address line 2 (optional)", "uiType": "text-field", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "validation": [{"matches": "/^[A-Za-z0-9 .,'():;\\-/?@]*$/", "errorMessage": "Use only letters, numbers, spaces and the following characters: . , ' ( ) : ; - / ? @"}, {"max": 256}], "showIf": [{"country": [{"filled": true}, {"notEquals": "SG"}]}]}, "contact-header": {"children": "Contact details", "uiType": "text-h3", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}}, "mobile": {"label": "Mobile number", "uiType": "contact-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"contactNumber": {"singaporeNumber": "mobile"}, "errorMessage": "Enter a valid mobile number that starts with 8 or 9."}]}, "otherPhone": {"label": "Home number (optional)", "uiType": "contact-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"contactNumber": {"singaporeNumber": "house"}, "errorMessage": "Enter a valid home number that starts with 6."}]}, "email": {"label": "Email", "uiType": "email-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"email": true, "errorMessage": "Enter in the format: <EMAIL>"}, {"max": 256}]}, "schoolEmail": {"label": "School email", "uiType": "email-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"email": true, "errorMessage": "Enter in the format: <EMAIL>"}, {"max": 256}]}}}}}}], "schemaConfig": {"overridesTemplate": {"fullName": {"disabled": {"$pattern": [{"conditionalPattern": "$.myinfo.name.value", "trueValue": true, "falseValue": false}]}}, "idNo": {"disabled": {"$pattern": [{"conditionalPattern": "$.myinfo.uinfin.value", "trueValue": true, "falseValue": false}]}}, "birthDate": {"disabled": {"$pattern": [{"conditionalPattern": "$.myinfo.dob.value", "trueValue": true, "falseValue": false}]}}, "sex": {"disabled": {"$pattern": [{"conditionalPattern": "$.myinfo.sex.code", "trueValue": true, "falseValue": false}]}}, "residentialStatus": {"disabled": {"$pattern": [{"conditionalPattern": "$.myinfo.residentialstatus.code", "trueValue": true, "falseValue": false}]}}, "primaryRace": {"disabled": {"$pattern": [{"conditionalPattern": "$.myinfo.race.code", "trueValue": true, "falseValue": false}]}}}, "prefillTemplate": {"fullName": {"$pattern": [{"stringPattern": "{{$.myinfo.name.value}}"}]}, "idNo": {"$pattern": [{"stringPattern": "{{$.myinfo.uinfin.value}}"}]}, "birthDate": {"$pattern": [{"stringPattern": "{{$.myinfo.dob.value}}"}]}, "sex": {"$pattern": [{"stringPattern": "{{$.myinfo.sex.code}}"}]}, "residentialStatus": {"$pattern": [{"stringPattern": "{{$.myinfo.residentialstatus.code}}"}]}, "primaryRace": {"$pattern": [{"stringPattern": "{{$.myinfo.race.code}}"}]}, "country": {"$pattern": [{"stringPattern": "{{$.myinfo.regadd.country.code}}"}]}, "postalCode": {"$pattern": [{"stringPattern": "{{$.myinfo.regadd.postal.value}}"}]}, "block": {"$pattern": [{"stringPattern": "{{$.myinfo.regadd.block.value}}"}]}, "street": {"$pattern": [{"stringPattern": "{{$.myinfo.regadd.street.value}}"}]}, "levelUnit": {"$pattern": [{"stringPattern": "{{$.myinfo.regadd.floor.value}}-{{$.myinfo.regadd.unit.value}}"}]}, "building": {"$pattern": [{"stringPattern": "{{$.myinfo.regadd.building.value}}"}]}}}}, {"type": "ui", "name": "Disability condition section", "key": "ui-disability", "hasAuthRequirement": true, "runConditions": null, "schema": [{"title": "Disability condition", "description": "Tell us about your disability", "progressIndicator": {"section": "disability"}, "schema": {"sections": {"section": {"uiType": "section", "layoutType": "grid", "children": {"useMobilityAids": {"label": "Do you use mobility aids?", "uiType": "switch", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "validation": [{"required": true}]}, "usageOfMobilityAids": {"label": {"mainLabel": "Which mobility aids do you use?", "subLabel": "You may select more than one option."}, "uiType": "checkbox", "customOptions": {"indicator": true, "styleType": "toggle", "layoutType": "vertical"}, "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "options": [{"label": "Manual wheelchair", "value": "MA1"}, {"label": "Motorised wheelchair", "value": "MA2"}, {"label": "Prosthesis", "value": "MA3"}, {"label": "Quad stick", "value": "MA4"}, {"label": "Walking frame", "value": "MA5"}, {"label": "Walking stick", "value": "MA6"}, {"label": "Others", "value": "MA99"}], "validation": [{"required": true}], "showIf": [{"useMobilityAids": [{"equals": true}]}]}, "usageOfMobilityAidsSpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "placeholder": "Tell us about the mobility aids", "showIf": [{"usageOfMobilityAids": [{"shown": true}, {"filled": true}, {"includes": ["MA99"]}]}], "resizable": true, "rows": 4, "validation": [{"required": true}, {"max": 250}]}, "useHearingAids": {"label": "Do you use hearing aids?", "uiType": "switch", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "validation": [{"required": true}]}, "usageOfHearingAids": {"label": {"mainLabel": "Which hearing aids do you use?", "subLabel": "You may select more than one option."}, "uiType": "checkbox", "customOptions": {"indicator": true, "styleType": "toggle", "layoutType": "vertical"}, "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "options": [{"label": "Behind-the-ear hearing aids", "value": "HA1"}, {"label": "In-the-ear hearing aids", "value": "HA2"}, {"label": "Receiver-in-the-ear hearing aids", "value": "HA3"}, {"label": "In-the-canal hearing aids", "value": "HA4"}, {"label": "Others", "value": "HA99"}], "validation": [{"required": true}], "showIf": [{"useHearingAids": [{"equals": true}]}]}, "usageOfHearingAidsSpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "placeholder": "What type of hearing aids do you use?", "showIf": [{"usageOfHearingAids": [{"shown": true}, {"filled": true}, {"includes": ["HA99"]}]}], "resizable": true, "rows": 4, "validation": [{"required": true}, {"max": 250}]}, "useVisualAids": {"label": "Do you use visual aids?", "uiType": "switch", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "validation": [{"required": true}]}, "usageOfVisualAids": {"label": {"mainLabel": "Which visual aids do you use?", "subLabel": "You may select more than one option."}, "uiType": "checkbox", "customOptions": {"indicator": true, "styleType": "toggle", "layoutType": "vertical"}, "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "options": [{"label": "Corrective glasses", "value": "VA1"}, {"label": "Optical devices such as Magnifiers", "value": "VA2"}, {"label": "Screen readers", "value": "VA3"}, {"label": "White Cane", "value": "VA4"}, {"label": "Others", "value": "VA99"}], "validation": [{"required": true}], "showIf": [{"useVisualAids": [{"equals": true}]}]}, "usageOfVisualAidsSpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "placeholder": "What type of visual aids do you use?", "showIf": [{"usageOfVisualAids": [{"shown": true}, {"filled": true}, {"includes": ["VA99"]}]}], "resizable": true, "rows": 4, "validation": [{"required": true}, {"max": 250}]}, "ableToTravelIndependently": {"label": "Are you able to travel independently?", "uiType": "switch", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "validation": [{"required": true}]}, "travelAssistanceRequired": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Tell us about the assistance you require when you travel", "showIf": [{"ableToTravelIndependently": [{"equals": false}]}], "resizable": true, "rows": 4, "validation": [{"required": true}, {"max": 250}]}, "abilityToTravelIndependently": {"label": {"mainLabel": "Which modes of transport do you use?", "subLabel": "You may select more than one option."}, "uiType": "checkbox", "customOptions": {"indicator": true, "styleType": "toggle", "layoutType": "vertical"}, "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "options": [{"label": "Bus", "value": "MT1"}, {"label": "Car", "value": "MT2"}, {"label": "MRT", "value": "MT3"}, {"label": "Taxi", "value": "MT4"}, {"label": "Others", "value": "MT99"}], "validation": [{"required": true}], "showIf": [{"ableToTravelIndependently": [{"equals": true}]}]}, "abilityToTravelIndependentlySpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "placeholder": "Tell us about the modes of transport", "showIf": [{"abilityToTravelIndependently": [{"shown": true}, {"filled": true}, {"includes": ["MT99"]}]}], "resizable": true, "rows": 4, "validation": [{"required": true}, {"max": 250}]}, "accommodationRequired": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Do you require accommodation? (Optional)", "resizable": true, "rows": 4, "validation": [{"max": 250}]}, "preferredModeOfCommunication": {"label": {"mainLabel": "Preferred mode of communication", "subLabel": "You may select more than one option."}, "uiType": "checkbox", "customOptions": {"indicator": true, "styleType": "toggle", "layoutType": "vertical"}, "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "options": [{"label": "Lip reading", "value": "MC1"}, {"label": "Signing", "value": "MC2"}, {"label": "SMS", "value": "MC3"}, {"label": "Verbal", "value": "MC4"}, {"label": "Written", "value": "MC5"}, {"label": "Others", "value": "MC99"}], "validation": [{"required": true}]}, "preferredModeOfCommunicationSpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "placeholder": "Tell us about the preferred mode of communication", "showIf": [{"preferredModeOfCommunication": [{"filled": true}, {"includes": ["MC99"]}]}], "resizable": true, "rows": 4, "validation": [{"required": true}, {"max": 250}]}}}}}}]}, {"type": "ui", "name": "Education and employment section", "key": "ui-educationEmployment", "hasAuthRequirement": true, "runConditions": null, "schema": [{"title": "Education and employment", "description": "Tell us about your education and employment details", "progressIndicator": {"section": "educationEmployment"}, "schema": {"sections": {"section": {"uiType": "section", "layoutType": "grid", "children": {"education-header": {"children": "Education details", "uiType": "text-h3", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}}, "highestLevelOfEducationAttained": {"label": "Highest education level", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "validation": [{"required": true}], "options": [{"label": "No Formal Qualification / Pre-primary / Lower Primary", "value": "0"}, {"label": "Primary", "value": "1"}, {"label": "Lower Secondary", "value": "2"}, {"label": "Secondary", "value": "3"}, {"label": "Post-secondary (Non-tertiary): General and Vocational", "value": "4"}, {"label": "Polytechnic Diploma", "value": "5"}, {"label": "Professional Qualification and Other Diploma", "value": "6"}, {"label": "Bachelor's or Equivalent", "value": "7"}, {"label": "Postgraduate Diploma / Certificate (Excluding Master's and Doctorate)", "value": "8"}, {"label": "Master's and Doctorate or Equivalent", "value": "9"}, {"label": "Modular Certification (Non-award Courses / Non-Full Qualifications)", "value": "N"}, {"label": "Others", "value": "N9"}]}, "instituteOfHigherLearning": {"label": "Current Institute of Higher Learning (IHL)", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "validation": [{"required": true}], "options": [{"label": "Nanyang Technological University (NTU)", "value": "IHL1"}, {"label": "National University of Singapore (NUS)", "value": "IHL2"}, {"label": "Singapore Institute of Technology (SIT)", "value": "IHL3"}, {"label": "Singapore Management University (SMU)", "value": "IHL4"}, {"label": "Singapore University of Social Sciences (SUSS)", "value": "IHL5"}, {"label": "Singapore University of Technology and Design (SUTD)", "value": "IHL6"}, {"label": "Nanyang Polytechnic (NYP)", "value": "IHL7"}, {"label": "<PERSON><PERSON> (NP)", "value": "IHL8"}, {"label": "Republic Polytechnic (RP)", "value": "IHL9"}, {"label": "Singapore Polytechnic (SP)", "value": "IHL10"}, {"label": "Temasek Polytechnic (TP)", "value": "IHL11"}, {"label": "ITE College Central", "value": "IHL12"}, {"label": "ITE College East", "value": "IHL13"}, {"label": "ITE College West", "value": "IHL14"}, {"label": "LASALLE College of the Arts", "value": "IHL15"}, {"label": "Nanyang Academy of Fine Arts (NAFA)", "value": "IHL16"}, {"label": "Others (Please Specify)", "value": "IHL99"}]}, "instituteOfHigherLearningSpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Please specify your current Institute of Higher Learning (IHL)", "showIf": [{"instituteOfHigherLearning": [{"filled": true}, {"equals": "IHL99"}]}], "resizable": true, "rows": 4, "validation": [{"required": true}, {"max": 250}]}, "faculty": {"label": "Faculty", "uiType": "text-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"matches": "/^[A-Za-z0-9 .,'():;\\-/?@]*$/", "errorMessage": "Use only letters, numbers, spaces and the following characters: . , ' ( ) : ; - / ? @"}, {"max": 256}]}, "subjectMajor": {"label": "Subject major", "uiType": "text-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"matches": "/^[A-Za-z0-9 .,'():;\\-/?@]*$/", "errorMessage": "Use only letters, numbers, spaces and the following characters: . , ' ( ) : ; - / ? @"}, {"max": 256}]}, "courseDuration": {"label": "Course duration", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "1 year", "value": "D1"}, {"label": "2 years", "value": "D2"}, {"label": "3 years", "value": "D3"}, {"label": "4 years", "value": "D4"}], "validation": [{"required": true}]}, "yearOfStudy": {"label": "Year of study", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "1st year", "value": "Y1"}, {"label": "2nd year", "value": "Y2"}, {"label": "3rd year", "value": "Y3"}, {"label": "4th year", "value": "Y4"}], "validation": [{"required": true}]}, "durationOfInternship": {"label": "Duration of internship", "uiType": "date-range-field", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 5]}, "validation": [{"required": true}]}, "typeOfInternship": {"label": "Type of internship", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Graded internship", "value": "I1"}, {"label": "Non-graded Internship", "value": "I2"}], "validation": [{"required": true}]}, "internshipPreferenceRemarks": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Internship preference remarks (optional)", "resizable": true, "rows": 4, "validation": [{"max": 250}]}, "education-details-accordion": {"uiType": "accordion", "title": "Education records", "button": false, "collapsible": true, "expanded": true, "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "children": {"personalEducationDetails": {"referenceKey": "array-field", "sectionTitle": "Education", "validation": [{"min": 1}], "addButton": {"label": "Add education record"}, "fieldSchema": {"grid": {"uiType": "grid", "children": {"educationLevel": {"label": "Education level", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "validation": [{"required": true}], "options": [{"label": "No Formal Qualification / Pre-primary / Lower Primary", "value": "0"}, {"label": "Primary", "value": "1"}, {"label": "Lower Secondary", "value": "2"}, {"label": "Secondary", "value": "3"}, {"label": "Post-secondary (Non-tertiary): General and Vocational", "value": "4"}, {"label": "Polytechnic Diploma", "value": "5"}, {"label": "Professional Qualification and Other Diploma", "value": "6"}, {"label": "Bachelor's or Equivalent", "value": "7"}, {"label": "Postgraduate Diploma / Certificate (Excluding Master's and Doctorate)", "value": "8"}, {"label": "Master's and Doctorate or Equivalent", "value": "9"}, {"label": "Modular Certification (Non-award Courses / Non-Full Qualifications)", "value": "N"}, {"label": "Others", "value": "N9"}]}, "educationLevelSpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": {"mainLabel": "Tell us about the education level (optional)", "subLabel": "Provide relevant details, which includes  incomplete qualifications, certifications, or any special education received."}, "resizable": true, "rows": 4, "validation": [{"max": 250}]}, "school": {"label": "School", "uiType": "text-field", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"matches": "/^[A-Za-z0-9 .,'():;\\-/?@]*$/", "errorMessage": "Use only letters, numbers, spaces and the following characters: . , ' ( ) : ; - / ? @"}, {"max": 256}]}, "fieldOfStudy": {"label": "Field of study (optional)", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Education", "value": "01"}, {"label": "Fine and Applied Arts", "value": "02"}, {"label": "Humanities and Social Sciences", "value": "03"}, {"label": "Mass Communications and Information Science", "value": "04"}, {"label": "Business and Administration", "value": "05"}, {"label": "Law", "value": "06"}, {"label": "Natural and Mathematical Sciences", "value": "07"}, {"label": "Health Sciences", "value": "08"}, {"label": "Information Technology", "value": "09"}, {"label": "Architecture, Building and Real Estate", "value": "10"}, {"label": "Engineering Sciences", "value": "11"}, {"label": "Engineering, Manufacturing and Related Trades", "value": "12"}, {"label": "Services", "value": "13"}, {"label": "Other Fields", "value": "99"}, {"label": "Not Applicable", "value": "XX"}]}, "fieldOfStudySpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Tell us about the field of study", "showIf": [{"fieldOfStudy": [{"filled": true}, {"equals": "99"}]}], "resizable": true, "rows": 4, "validation": [{"max": 250}]}, "durationOfStudy": {"label": "Duration of study", "uiType": "date-range-field", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "showIf": [{"isStudying": [{"empty": true}]}], "validation": [{"required": true}]}, "studyStartDate": {"label": "Start date of study (optional)", "uiType": "date-field", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "showIf": [{"isStudying": [{"filled": true}, {"includes": ["1"]}]}]}, "isStudying": {"customOptions": {"indicator": true, "styleType": "toggle"}, "uiType": "checkbox", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "options": [{"label": "I am currently still studying.", "value": "1"}]}, "reasonForLeaving": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": {"mainLabel": "Reason for leaving (optional)", "subLabel": "If the PWD left school before graduating, tell us about the situation."}, "resizable": true, "rows": 4, "validation": [{"max": 250}]}}}}}}}, "employment-header": {"children": "Employment details", "uiType": "text-h3", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}}, "employmentStatus": {"label": "Employment status", "uiType": "radio", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Working", "value": "ES1"}, {"label": "Looking for work", "value": "ES2"}, {"label": "Not working", "value": "ES3"}], "validation": [{"required": true}]}, "occupation": {"label": "Occupation (optional)", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Legislators, Senior Officials and Managers", "value": "1"}, {"label": "Professionals", "value": "2"}, {"label": "Associate Professionals and Technicians", "value": "3"}, {"label": "Clerical Support Workers", "value": "4"}, {"label": "Services and Sales Workers", "value": "5"}, {"label": "Agricultural and Fishery Workers", "value": "6"}, {"label": "Craftsmen and related Trades Workers", "value": "7"}, {"label": "Plant and Machine Operators and Assemblers", "value": "8"}, {"label": "Cleaners, Labourers and Related Workers", "value": "9"}, {"label": "Others", "value": "X"}], "showIf": [{"employmentStatus": [{"filled": true}, {"equals": "ES1"}]}]}, "situation": {"label": "Situation", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Homemaker", "value": "UR4"}, {"label": "Incarcerated", "value": "UR1"}, {"label": "Medical reasons", "value": "UR6"}, {"label": "National serviceman", "value": "UR5"}, {"label": "Student", "value": "UR3"}, {"label": "<PERSON><PERSON><PERSON>", "value": "UR2"}, {"label": "Others", "value": "UR99"}], "validation": [{"required": true}], "showIf": [{"employmentStatus": [{"filled": true}, {"equals": "ES3"}]}]}, "unemployedSituationSpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Please share more about the situation", "showIf": [{"situation": [{"shown": true}, {"filled": true}, {"includes": ["UR99"]}]}], "resizable": true, "rows": 4, "validation": [{"max": 250}]}, "providePersonalEmployment": {"label": "Would you like to provide your employment records?", "uiType": "switch", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "validation": [{"required": true}]}, "employment-details-accordion": {"uiType": "accordion", "title": "Employment records", "button": false, "collapsible": true, "expanded": true, "showIf": [{"providePersonalEmployment": [{"equals": true}]}], "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "children": {"personalEmploymentDetails": {"referenceKey": "array-field", "sectionTitle": "Employment", "addButton": {"label": "Add employment record"}, "fieldSchema": {"grid": {"uiType": "grid", "children": {"employer": {"label": "Company name", "uiType": "text-field", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"matches": "/^[A-Za-z0-9 .,'():;\\-/?@]*$/", "errorMessage": "Use only letters, numbers, spaces and the following characters: . , ' ( ) : ; - / ? @"}, {"max": 256}]}, "position": {"label": "Job title (optional)", "uiType": "text-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"matches": "/^[A-Za-z0-9 .,'():;\\-/?@]*$/", "errorMessage": "Use only letters, numbers, spaces and the following characters: . , ' ( ) : ; - / ? @"}, {"max": 256}]}, "monthlyIncome": {"label": "Average gross monthly income (optional)", "uiType": "text-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"matches": "/^(0|[1-9][0-9]*)(\\.[0-9]{2})?$/", "errorMessage": "Enter in the format: 0.00"}, {"max": 10}]}, "durationOfWork": {"label": "Duration of work", "uiType": "date-range-field", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "showIf": [{"isWorking": [{"empty": true}]}], "validation": [{"required": true}]}, "workStartDate": {"label": "Start date of work (optional)", "uiType": "date-field", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "showIf": [{"isWorking": [{"filled": true}, {"includes": ["1"]}]}]}, "isWorking": {"customOptions": {"indicator": true, "styleType": "toggle"}, "uiType": "checkbox", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "options": [{"label": "I am currently working in this job", "value": "1"}]}, "reasonForLeaving": {"label": {"mainLabel": "Reason for leaving (optional)", "subLabel": "You may select more than one option."}, "uiType": "checkbox", "customOptions": {"indicator": true, "styleType": "toggle", "layoutType": "vertical"}, "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "options": [{"label": "End of Contract / Internship", "value": "R2"}, {"label": "Medical / Health Reasons", "value": "R5"}, {"label": "Retrenched / Terminated", "value": "R6"}, {"label": "Work environment / culture", "value": "R7"}, {"label": "<PERSON><PERSON><PERSON> further studies", "value": "R10"}, {"label": "Others", "value": "R99"}]}, "reasonForLeavingSpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "showIf": [{"reasonForLeaving": [{"filled": true}, {"includes": ["R99"]}]}], "resizable": true, "rows": 4, "validation": [{"max": 250}]}}}}}}}}}}}}]}, {"type": "ui", "name": "Caregiver section", "key": "ui-caregiver<PERSON><PERSON><PERSON>", "hasAuthRequirement": true, "runConditions": null, "schema": [{"title": "Caregiver", "description": "Tell us about your caregiver details", "progressIndicator": {"section": "caregiver<PERSON><PERSON><PERSON>"}, "schema": {"sections": {"section": {"uiType": "section", "layoutType": "grid", "children": {"haveLegalGuardianCaregiver": {"label": "Would you like to provide your caregiver records?", "uiType": "switch", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "validation": [{"required": true}]}, "caregiver-details-accordion": {"uiType": "accordion", "title": "Caregiver details", "button": false, "collapsible": true, "expanded": true, "showIf": [{"haveLegalGuardianCaregiver": [{"equals": true}]}], "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "children": {"caregiverGuardianDetails": {"referenceKey": "array-field", "sectionTitle": "Caregiver", "addButton": {"label": "Add caregiver"}, "fieldSchema": {"grid": {"uiType": "grid", "children": {"relationship": {"label": "Relationship to person with disability", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "validation": [{"required": true}], "options": [{"label": "Spouse", "value": "REL001"}, {"label": "Husband", "value": "REL101"}, {"label": "Wife", "value": "REL102"}, {"label": "Child", "value": "REL004"}, {"label": "Son", "value": "REL401"}, {"label": "Daughter", "value": "REL402"}, {"label": "Parent", "value": "REL002"}, {"label": "Father", "value": "REL202"}, {"label": "Mother", "value": "REL201"}, {"label": "Sibling", "value": "REL006"}, {"label": "Brother", "value": "REL601"}, {"label": "Sister", "value": "REL602"}, {"label": "Grandchild", "value": "REL007"}, {"label": "<PERSON><PERSON>", "value": "REL701"}, {"label": "Granddaughter", "value": "REL702"}, {"label": "Grandparent", "value": "REL003"}, {"label": "Grandfather", "value": "REL301"}, {"label": "Grandmother", "value": "REL302"}, {"label": "Son In-Law", "value": "REL505"}, {"label": "Daughter In-Law", "value": "REL506"}, {"label": "Father In-Law", "value": "REL501"}, {"label": "Mother In-Law", "value": "REL502"}, {"label": "Brother In-Law", "value": "REL503"}, {"label": "Sister In-<PERSON>", "value": "REL504"}, {"label": "Child In-Law", "value": "REL507"}, {"label": "Parent In-Law", "value": "REL508"}, {"label": "Sibling In-Law", "value": "REL509"}, {"label": "In-Laws", "value": "REL005"}, {"label": "Ward", "value": "REL008"}, {"label": "Guardian", "value": "REL009"}, {"label": "Step-Son", "value": "REL405"}, {"label": "Step-Daughter", "value": "REL406"}, {"label": "Step-Child", "value": "REL408"}, {"label": "Step-Father", "value": "REL205"}, {"label": "Step-Mother", "value": "REL206"}, {"label": "Step-Parent", "value": "REL209"}, {"label": "Step-Brother", "value": "REL605"}, {"label": "Step-Sister", "value": "REL606"}, {"label": "Step-Sibling", "value": "REL609"}, {"label": "Cousin", "value": "REL805"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "REL803"}, {"label": "<PERSON><PERSON><PERSON>", "value": "REL804"}, {"label": "Uncle", "value": "REL801"}, {"label": "Aunt", "value": "REL802"}, {"label": "Uncle / Auntie", "value": "REL807"}, {"label": "Nephew / Niece", "value": "REL808"}, {"label": "Other Family Member", "value": "REL010"}]}, "role": {"label": "Role to person with disability", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "validation": [{"required": true}], "options": [{"label": "Parent / Legal guardian", "value": "LG"}, {"label": "<PERSON><PERSON> / Deputy", "value": "DD"}, {"label": "Caregiver / Non-legal guardian", "value": "CG"}]}, "fullName": {"label": "Name (as in NRIC or FIN)", "uiType": "text-field", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"matches": "/^[A-Za-z0-9 ,'/()@%~\\.-]*$/", "errorMessage": "Use only letters, numbers, spaces and the following characters: @ . , ' ( ) - / \\ ~ %"}, {"max": 66}]}, "idNo": {"label": "NRIC or FIN", "uiType": "text-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"uinfin": true, "errorMessage": "Enter a valid NRIC/FIN."}]}, "residentialStatus": {"label": "Residential status", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Singapore citizen", "value": "C"}, {"label": "Permanent Resident (PR)", "value": "P"}, {"label": "Foreigner", "value": "A"}], "validation": [{"required": true}]}, "birthDate": {"label": "Date of birth", "uiType": "date-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"notFuture": true, "errorMessage": "No future dates"}]}, "primaryRace": {"label": "Race", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Chinese", "value": "CN"}, {"label": "Malay", "value": "MY"}, {"label": "Indian", "value": "IN"}, {"label": "Achehnese", "value": "AJ"}, {"label": "Afghan", "value": "AG"}, {"label": "African", "value": "AF"}, {"label": "Albanian", "value": "AL"}, {"label": "Ambonese", "value": "AO"}, {"label": "American", "value": "US"}, {"label": "Amerindian", "value": "AD"}, {"label": "Anglo Saxon", "value": "AX"}, {"label": "Arab", "value": "AR"}, {"label": "Armenian", "value": "AM"}, {"label": "Aryan", "value": "AY"}, {"label": "Assami", "value": "AS"}, {"label": "Australian", "value": "AU"}, {"label": "Austrian", "value": "AT"}, {"label": "Baja<PERSON>", "value": "BF"}, {"label": "Balinese", "value": "BM"}, {"label": "Bangladeshi", "value": "BD"}, {"label": "Banjarese", "value": "BJ"}, {"label": "Basque", "value": "BQ"}, {"label": "Batak", "value": "BA"}, {"label": "Belgian", "value": "BE"}, {"label": "Bengali", "value": "BI"}, {"label": "<PERSON><PERSON>", "value": "BW"}, {"label": "Bhutanese", "value": "BN"}, {"label": "Bidayuh", "value": "DH"}, {"label": "<PERSON><PERSON><PERSON>", "value": "BS"}, {"label": "Bosniak", "value": "BV"}, {"label": "Boyanese", "value": "BY"}, {"label": "Brazilian", "value": "BZ"}, {"label": "British", "value": "BT"}, {"label": "<PERSON><PERSON><PERSON>", "value": "BG"}, {"label": "Bulgarian", "value": "BB"}, {"label": "<PERSON><PERSON><PERSON>", "value": "BH"}, {"label": "Burmese", "value": "BU"}, {"label": "Butonese", "value": "BC"}, {"label": "Cambodian", "value": "CD"}, {"label": "Canadian", "value": "CB"}, {"label": "Cape Coloured", "value": "CC"}, {"label": "Caucasian", "value": "CA"}, {"label": "Ceylonese", "value": "CE"}, {"label": "<PERSON><PERSON><PERSON>", "value": "CJ"}, {"label": "Cornish", "value": "CF"}, {"label": "Creole", "value": "CG"}, {"label": "Croat", "value": "CI"}, {"label": "Czech", "value": "CZ"}, {"label": "<PERSON>", "value": "DA"}, {"label": "<PERSON><PERSON>", "value": "DY"}, {"label": "<PERSON><PERSON>", "value": "DS"}, {"label": "Dutch", "value": "DU"}, {"label": "Egyptian", "value": "EY"}, {"label": "English", "value": "EL"}, {"label": "Ethiopian", "value": "ET"}, {"label": "Eurasian", "value": "EU"}, {"label": "Fijian", "value": "FJ"}, {"label": "Filipino", "value": "PH"}, {"label": "<PERSON>", "value": "FI"}, {"label": "Flemish", "value": "FM"}, {"label": "French", "value": "FR"}, {"label": "German", "value": "GM"}, {"label": "Ghanaian", "value": "GH"}, {"label": "Goan", "value": "GA"}, {"label": "Greek", "value": "GR"}, {"label": "Gujarati", "value": "GE"}, {"label": "Gurkha", "value": "GK"}, {"label": "Haitian", "value": "HA"}, {"label": "Hawaiian", "value": "HW"}, {"label": "Hindustani", "value": "HT"}, {"label": "Hispanic", "value": "HI"}, {"label": "Hungarian", "value": "HU"}, {"label": "<PERSON><PERSON>", "value": "IB"}, {"label": "<PERSON><PERSON>", "value": "IS"}, {"label": "Indonesian", "value": "ID"}, {"label": "Inuit", "value": "IU"}, {"label": "Iranian", "value": "IA"}, {"label": "Iraqi", "value": "IQ"}, {"label": "Irish", "value": "IR"}, {"label": "Israeli", "value": "IL"}, {"label": "Italian", "value": "IT"}, {"label": "<PERSON><PERSON><PERSON>", "value": "JK"}, {"label": "Jamaican", "value": "JM"}, {"label": "Japanese", "value": "JP"}, {"label": "Javanese", "value": "JA"}, {"label": "Jew", "value": "JW"}, {"label": "<PERSON><PERSON>", "value": "JO"}, {"label": "<PERSON><PERSON>", "value": "KA"}, {"label": "Kadazan", "value": "KZ"}, {"label": "<PERSON>", "value": "KN"}, {"label": "<PERSON><PERSON>", "value": "KC"}, {"label": "<PERSON><PERSON>", "value": "KY"}, {"label": "<PERSON><PERSON>", "value": "KD"}, {"label": "Kazakh", "value": "KK"}, {"label": "Kelabit", "value": "KL"}, {"label": "Kenyah", "value": "KI"}, {"label": "Kenyan", "value": "KE"}, {"label": "<PERSON><PERSON><PERSON>", "value": "KB"}, {"label": "Khmer", "value": "KH"}, {"label": "<PERSON><PERSON>", "value": "KF"}, {"label": "Korean", "value": "KR"}, {"label": "Kyrgyz", "value": "KG"}, {"label": "Lao", "value": "LA"}, {"label": "Latin", "value": "LT"}, {"label": "Latvian", "value": "LV"}, {"label": "Lebanese", "value": "LB"}, {"label": "Libyan", "value": "LY"}, {"label": "Li<PERSON>", "value": "LS"}, {"label": "Lithuanian", "value": "LI"}, {"label": "<PERSON>er", "value": "LX"}, {"label": "<PERSON><PERSON><PERSON>", "value": "MA"}, {"label": "Mahratta", "value": "MH"}, {"label": "Makasarese", "value": "MK"}, {"label": "<PERSON><PERSON><PERSON>", "value": "MB"}, {"label": "Malagasy", "value": "MG"}, {"label": "Malayalee", "value": "MM"}, {"label": "Maldivian", "value": "ML"}, {"label": "Maltese", "value": "MT"}, {"label": "Manipuri", "value": "MP"}, {"label": "<PERSON><PERSON>", "value": "MI"}, {"label": "Marathi", "value": "MR"}, {"label": "<PERSON><PERSON><PERSON>", "value": "MU"}, {"label": "Melanau", "value": "MZ"}, {"label": "Melanesian", "value": "MN"}, {"label": "Mexican", "value": "MX"}, {"label": "Minangkabau", "value": "ME"}, {"label": "Moldavian", "value": "MD"}, {"label": "Mon", "value": "MV"}, {"label": "Mongolian", "value": "MO"}, {"label": "Moroccan", "value": "MW"}, {"label": "<PERSON><PERSON><PERSON>", "value": "MJ"}, {"label": "Naga", "value": "NA"}, {"label": "Nauruan", "value": "NR"}, {"label": "Nepalese", "value": "NP"}, {"label": "New Zealander", "value": "NZ"}, {"label": "Newar", "value": "NW"}, {"label": "Nigerian", "value": "NI"}, {"label": "Norwegian", "value": "NO"}, {"label": "Others", "value": "XX"}, {"label": "Pakistani", "value": "PK"}, {"label": "Palestine", "value": "PB"}, {"label": "Parsee", "value": "PS"}, {"label": "<PERSON><PERSON>", "value": "PN"}, {"label": "<PERSON><PERSON>", "value": "PA"}, {"label": "Persian", "value": "PE"}, {"label": "Peruvian", "value": "PR"}, {"label": "Pole", "value": "PL"}, {"label": "Polynesian", "value": "PY"}, {"label": "Portuguese", "value": "PO"}, {"label": "Punan", "value": "PU"}, {"label": "Punjabi", "value": "PJ"}, {"label": "Rajput", "value": "RJ"}, {"label": "<PERSON><PERSON><PERSON>", "value": "RK"}, {"label": "Romanian", "value": "RO"}, {"label": "Russian", "value": "RU"}, {"label": "Samoan", "value": "SG"}, {"label": "Scot", "value": "SC"}, {"label": "Serbia/Montengerin", "value": "SF"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "SY"}, {"label": "<PERSON>", "value": "SH"}, {"label": "Sikh", "value": "SK"}, {"label": "Sindhi", "value": "SD"}, {"label": "Sinhalese", "value": "SI"}, {"label": "Slavic", "value": "SQ"}, {"label": "Slovak", "value": "SL"}, {"label": "Somali", "value": "SO"}, {"label": "Spanish", "value": "ES"}, {"label": "Sri Lankan", "value": "LK"}, {"label": "Sudanese", "value": "SU"}, {"label": "Sumatran", "value": "SM"}, {"label": "Sundanese", "value": "SS"}, {"label": "Swede", "value": "SE"}, {"label": "Swiss", "value": "CH"}, {"label": "Tajik", "value": "TJ"}, {"label": "Tamil", "value": "TM"}, {"label": "Telugu", "value": "TE"}, {"label": "Thai", "value": "TH"}, {"label": "Tibetan", "value": "TI"}, {"label": "Timor", "value": "TP"}, {"label": "Tongan", "value": "TO"}, {"label": "Turk", "value": "TR"}, {"label": "Turkmen", "value": "TN"}, {"label": "Ukrainian", "value": "UR"}, {"label": "Uyghur", "value": "UY"}, {"label": "Uzbek", "value": "UZ"}, {"label": "Venezuelan", "value": "VE"}, {"label": "Vietnamese", "value": "VN"}, {"label": "Welsh", "value": "WE"}, {"label": "Yugoslav", "value": "YU"}, {"label": "Zimbabwean", "value": "ZW"}], "validation": [{"required": true}]}, "sex": {"label": "Sex", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Female", "value": "F"}, {"label": "Male", "value": "M"}], "validation": [{"required": true}]}, "mobile": {"label": "Mobile number", "uiType": "contact-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"contactNumber": {"singaporeNumber": "mobile"}, "errorMessage": "Enter a valid mobile number that starts with 8 or 9."}]}, "email": {"label": "Email", "uiType": "email-field", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "validation": [{"required": true}, {"email": true, "errorMessage": "Enter in the format: <EMAIL>"}, {"max": 256}]}, "highestLevelOfEducationAttained": {"label": "Highest education level", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "No Formal Qualification / Pre-primary / Lower Primary", "value": "0"}, {"label": "Primary", "value": "1"}, {"label": "Lower Secondary", "value": "2"}, {"label": "Secondary", "value": "3"}, {"label": "Post-secondary (Non-tertiary): General and Vocational", "value": "4"}, {"label": "Polytechnic Diploma", "value": "5"}, {"label": "Professional Qualification and Other Diploma", "value": "6"}, {"label": "Bachelor's or Equivalent", "value": "7"}, {"label": "Postgraduate Diploma / Certificate (Excluding Master's and Doctorate)", "value": "8"}, {"label": "Master's and Doctorate or Equivalent", "value": "9"}, {"label": "Modular Certification (Non-award Courses / Non-Full Qualifications)", "value": "N"}, {"label": "Others", "value": "N9"}], "validation": [{"required": true}]}, "employmentStatus": {"label": "Employment status", "uiType": "radio", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Working", "value": "ES1"}, {"label": "Looking for work", "value": "ES2"}, {"label": "Not working", "value": "ES3"}], "validation": [{"required": true}]}, "occupation": {"label": "Occupation (optional)", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Legislators, Senior Officials and Managers", "value": "1"}, {"label": "Professionals", "value": "2"}, {"label": "Associate Professionals and Technicians", "value": "3"}, {"label": "Clerical Support Workers", "value": "4"}, {"label": "Services and Sales Workers", "value": "5"}, {"label": "Agricultural and Fishery Workers", "value": "6"}, {"label": "Craftsmen and related Trades Workers", "value": "7"}, {"label": "Plant and Machine Operators and Assemblers", "value": "8"}, {"label": "Cleaners, Labourers and Related Workers", "value": "9"}, {"label": "Others", "value": "X"}], "showIf": [{"employmentStatus": [{"filled": true}, {"equals": "ES1"}]}]}, "situation": {"label": "Situation", "uiType": "select", "columns": {"desktop": [1, 5], "tablet": 8, "mobile": 4}, "placeholder": "Select", "options": [{"label": "Homemaker", "value": "UR4"}, {"label": "Incarcerated", "value": "UR1"}, {"label": "Medical reasons", "value": "UR6"}, {"label": "National serviceman", "value": "UR5"}, {"label": "Student", "value": "UR3"}, {"label": "<PERSON><PERSON><PERSON>", "value": "UR2"}, {"label": "Others", "value": "UR99"}], "validation": [{"required": true}], "showIf": [{"employmentStatus": [{"filled": true}, {"equals": "ES3"}]}]}, "unemployedSituationSpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Please share more about the situation", "showIf": [{"situation": [{"shown": true}, {"filled": true}, {"includes": ["UR99"]}]}], "resizable": true, "rows": 4, "validation": [{"required": true}, {"max": 250}]}}}}}}}}}}}}]}, {"type": "ui", "name": "Supporting documents section", "key": "ui-supportingDoc", "hasAuthRequirement": true, "runConditions": null, "schema": [{"title": "Supporting documents", "description": "Please upload the following documents.", "progressIndicator": {"section": "supportingDoc"}, "schema": {"sections": {"section": {"uiType": "section", "layoutType": "grid", "children": {"alert-warning": {"children": "<p><strong>File Requirements</strong></p><p>Accepted file formats: PDF, PNG, JFIF, JPE, JPG, JPEG</p><p>Max. file size: 2 MB per file</p>", "type": "warning", "uiType": "alert", "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}}, "sh_dvf": {"uiType": "file-upload", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Disability Verification Form (DVF)", "description": "<p>For person with disabilities who are <strong>not verified</strong> with SGEnable, please upload a copy of DVF. Please ensure the DVF is endorsed by a registered healthcare professional before proceeding. You can <a href=\"https://enablingguide.sg/disability-verification\" rel=\"noopener noreferrer\" target=\"_blank\">download the DVF</a> here.</p><p>Optional</p>", "validation": [{"maxSizeInKb": 2048, "errorMessage": "File size exceeded 2 MB. Reduce the file size and try again."}, {"max": 10, "errorMessage": "Reached limit of 10 files. Try uploading the files elsewhere or reducing the number of files."}], "uploadOnAddingFile": {"type": "multipart", "url": "https://webhook.site/963a8b03-324a-4335-ab85-5aeaa9c0bb70"}}, "edu_schoolCert": {"uiType": "file-upload", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Certificates of completion for the education records provided", "description": "Optional", "validation": [{"maxSizeInKb": 2048, "errorMessage": "File size exceeded 2 MB. Reduce the file size and try again."}, {"max": 10, "errorMessage": "Reached limit of 10 files. Try uploading the files elsewhere or reducing the number of files."}], "uploadOnAddingFile": {"type": "multipart", "url": "https://webhook.site/963a8b03-324a-4335-ab85-5aeaa9c0bb70"}}, "emp_resume": {"uiType": "file-upload", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Resume", "description": "Required", "validation": [{"required": true}, {"maxSizeInKb": 2048, "errorMessage": "File size exceeded 2 MB. Reduce the file size and try again."}, {"max": 10, "errorMessage": "Reached limit of 10 files. Try uploading the files elsewhere or reducing the number of files."}], "uploadOnAddingFile": {"type": "multipart", "url": "https://webhook.site/963a8b03-324a-4335-ab85-5aeaa9c0bb70"}}, "oth_disabilityProof": {"uiType": "file-upload", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Proof of relationship", "description": "<p>Please upload a copy of:</p><ol><li>Proof of relationship</li><li>Doctor's certification that client has mental incapacity</li></ol><p>Optional</p>", "validation": [{"maxSizeInKb": 2048, "errorMessage": "File size exceeded 2 MB. Reduce the file size and try again."}, {"max": 10, "errorMessage": "Reached limit of 10 files. Try uploading the files elsewhere or reducing the number of files."}], "uploadOnAddingFile": {"type": "multipart", "url": "https://webhook.site/963a8b03-324a-4335-ab85-5aeaa9c0bb70"}}}}}}}]}, {"type": "ui", "name": "Consent and declaration section", "key": "ui-declaration", "hasAuthRequirement": true, "runConditions": null, "schema": [{"title": "Declarations", "description": "Please complete the declaration", "progressIndicator": {"section": "declaration"}, "schema": {"sections": {"section": {"uiType": "section", "layoutType": "grid", "children": {"previousConvictionInCourt": {"label": "Do you have any previous conviction in court?", "uiType": "switch", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "validation": [{"required": true}]}, "previousConvictionInCourtSpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "label": "Tell us about your conviction details", "showIf": [{"previousConvictionInCourt": [{"equals": true}]}], "resizable": true, "rows": 4, "validation": [{"required": true}, {"max": 250}]}, "interestedServices": {"label": {"mainLabel": "Which of the following services are you interested in? (optional)", "subLabel": "You may select more than one option."}, "uiType": "checkbox", "customOptions": {"indicator": true, "styleType": "toggle", "layoutType": "vertical"}, "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "options": [{"label": "Employment", "value": "S1"}, {"label": "Disability Services - Residential", "value": "S2"}, {"label": "Disabiltiy Services - Non-residential", "value": "S3"}, {"label": "Transport subsidies", "value": "S4"}, {"label": "Financial assistance", "value": "S5"}, {"label": "Caregiver support", "value": "S6"}, {"label": "Assistive Technology", "value": "S7"}, {"label": "Respite Care", "value": "S8"}]}, "foundSGEnableVia": {"label": {"mainLabel": "How did you find out about S<PERSON> Enable?", "subLabel": "You may select more than one option."}, "uiType": "checkbox", "customOptions": {"indicator": true, "styleType": "toggle", "layoutType": "vertical"}, "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "options": [{"label": "SG Enable - Newsletter/Emails", "value": "1"}, {"label": "SG Enable - Website", "value": "2"}, {"label": "SG Enable - Facebook", "value": "3"}, {"label": "SG Enable - Linkedin", "value": "4"}, {"label": "School", "value": "5"}, {"label": "Family/Friends", "value": "6"}, {"label": "Other Social Service Agencies e.g. SPD, SADeaf", "value": "7"}, {"label": "News Media e.g. Straits Times, Channel NewsAsia, Mothership", "value": "8"}, {"label": "Others", "value": "9"}], "validation": [{"required": true}]}, "foundSGEnableViaSpecify": {"uiType": "textarea", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "placeholder": "How else did you find out about S<PERSON> Enable?", "showIf": [{"foundSGEnableVia": [{"filled": true}, {"includes": ["9"]}]}], "resizable": true, "rows": 4, "validation": [{"required": true}, {"max": 250}]}}}}}}]}, {"type": "ui", "name": "Consent and declaration section", "key": "ui-terms", "hasAuthRequirement": true, "runConditions": null, "schema": [{"title": "Terms & Conditions", "description": "Please read and acknowledge the terms and conditions", "progressIndicator": {"section": "consent"}, "schema": {"sections": {"section": {"uiType": "section", "layoutType": "grid", "children": {"tnc-accordion": {"uiType": "accordion", "title": "Consent to data sharing", "button": false, "collapsible": true, "expanded": true, "columns": {"desktop": [1, 9], "tablet": 8, "mobile": 4}, "children": {"consent-list-contents": {"children": [{"listItem": {"children": {"text": {"children": "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:", "uiType": "text-body"}, "list": {"children": ["to verify the identity and relationship of me and my family, for the Services or Schemes;", "to conduct outreach to me and my family to inform us about, and to encourage us to apply for, the Services or Schemes;", "to determine the eligibility of me and my family for the Services or Schemes;", "to provide me and my family with the Services or Schemes;", "to refer me and my family to other Services or Schemes not currently provided to us, if assessed to be useful to me and my family; and", "for data analysis, research, evaluation and policy-making, for the Services or Schemes."], "counterSeparator": ".", "counterType": "lower-alpha", "uiType": "ordered-list"}}, "uiType": "list-item"}}, "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use, and disclose my Personal Information for the purposes stated in Paragraph 1 and any other purpose permitted by law. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that if there are any discrepancies in the Personal Information collected, such discrepancies may be reflected to the relevant Singapore Public Agencies, so that they may take the necessary steps to rectify any inaccurate records relating to me.", "My consent shall remain valid until I withdraw it in writing. I accept that it will take up to 10-15 working days from the date of receipt before the withdrawal of consent takes place.", "I have read and understood this consent form fully, including the attached <a href=\"https://go.gov.sg/termsofconsent\" rel=\"noopener noreferrer\" target=\"_blank\">Terms of Consent</a>. I declare that the information that I have provided is accurate at the time I submit this form."], "uiType": "ordered-list"}}}, "consent": {"customOptions": {"indicator": true, "styleType": "toggle"}, "uiType": "checkbox", "columns": {"mobile": 4, "tablet": 8, "desktop": [1, 9]}, "options": [{"label": "I acknowledge and consent to the terms above.", "value": "1"}], "validation": [{"required": true}]}}}}}}]}, {"type": "review", "name": "review action", "key": "review-action", "actionCopyConfig": null, "runConditions": null, "actionBehaviourConfig": null, "config": {"title": "Review", "description": "Make sure the information you've provided for your application is correct.", "sectionConfigs": [{"title": "Profile", "sectionType": "default", "targetActionKey": "ui-profile", "fields": [{"fieldId": "fullName", "displayWidth": "half"}, {"fieldId": "idNo", "displayWidth": "half"}, {"fieldId": "residentialStatus", "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"conditionalPattern": "$.sessionData.ui-profile.residentialStatus", "conditionalCheck": "equal", "conditionalCheckValue": "C", "trueValue": "Singapore citizen", "falseValue": {"conditionalCheck": "equal", "conditionalCheckValue": "P", "trueValue": "Permanent Resident (PR)", "falseValue": {"conditionalCheck": "equal", "conditionalCheckValue": "A", "trueValue": "Foreigner", "falseValue": "-"}}}]}}, {"fieldId": "birthDate", "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"objectPattern": "$.sessionData.ui-profile.birthDate", "wrap": false, "parseString": "datetime", "datetimeFormat": "dd <PERSON><PERSON> yyyy"}]}}, {"fieldId": "primaryRace", "displayWidth": "half"}, {"fieldId": "sex", "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"conditionalPattern": "$.sessionData.ui-profile.sex", "conditionalCheck": "equal", "conditionalCheckValue": "F", "trueValue": "Female", "falseValue": {"conditionalCheck": "equal", "conditionalCheckValue": "M", "trueValue": "Male", "falseValue": "-"}}]}}, {"fieldId": "country", "displayWidth": "half"}, {"fieldId": "block", "hideIfNotPresent": true, "displayWidth": "half"}, {"fieldId": "street", "hideIfNotPresent": true, "displayWidth": "half"}, {"fieldId": "building", "hideIfNotPresent": true, "displayWidth": "half"}, {"fieldId": "levelUnit", "hideIfNotPresent": true, "displayWidth": "half"}, {"fieldId": "postalCode", "hideIfNotPresent": true, "displayWidth": "half"}, {"fieldId": "addressLine1", "hideIfNotPresent": true}, {"fieldId": "addressLine2", "hideIfNotPresent": true}, {"fieldId": "mobile", "displayWidth": "half"}, {"fieldId": "otherPhone", "displayWidth": "half"}, {"fieldId": "email", "displayWidth": "half"}, {"fieldId": "schoolEmail", "displayWidth": "half"}]}, {"title": "Disability condition", "sectionType": "default", "targetActionKey": "ui-disability", "fields": [{"fieldId": "useMobilityAids", "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"conditionalPattern": "$.sessionData.ui-disability.useMobilityAids", "conditionalCheck": "boolean", "trueValue": "Yes", "falseValue": "No"}]}}, {"fieldId": "usageOfMobilityAids", "hideIfNotPresent": true, "displayWidth": "half"}, {"fieldId": "usageOfMobilityAidsSpecify", "hideIfNotPresent": true}, {"fieldId": "useHearingAids", "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"conditionalPattern": "$.sessionData.ui-disability.useHearingAids", "conditionalCheck": "boolean", "trueValue": "Yes", "falseValue": "No"}]}}, {"fieldId": "usageOfHearingAids", "hideIfNotPresent": true, "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"stringJoinPattern": {"arrayMapPattern": "$.sessionData.ui-disability.usageOfHearingAids", "itemMapping": {"conditionalPattern": "$.mapItem", "conditionalCheck": "equal", "conditionalCheckValue": "HA1", "trueValue": "Behind-the-ear hearing aids", "falseValue": {"conditionalPattern": "$.mapItem", "conditionalCheck": "equal", "conditionalCheckValue": "HA2", "trueValue": "In-the-ear hearing aids", "falseValue": "-"}}}, "separator": ", "}]}}, {"fieldId": "usageOfHearingAidsSpecify", "hideIfNotPresent": true}, {"fieldId": "useVisualAids", "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"conditionalPattern": "$.sessionData.ui-disability.useVisualAids", "conditionalCheck": "boolean", "trueValue": "Yes", "falseValue": "No"}]}}, {"fieldId": "usageOfVisualAids", "hideIfNotPresent": true, "displayWidth": "half"}, {"fieldId": "usageOfVisualAidsSpecify", "hideIfNotPresent": true}, {"fieldId": "ableToTravelIndependently", "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"conditionalPattern": "$.sessionData.ui-disability.ableToTravelIndependently", "conditionalCheck": "boolean", "trueValue": "Yes", "falseValue": "No"}]}}, {"fieldId": "travelAssistanceRequired", "hideIfNotPresent": true}, {"fieldId": "abilityToTravelIndependently", "hideIfNotPresent": true, "displayWidth": "half"}, {"fieldId": "abilityToTravelIndependentlySpecify", "hideIfNotPresent": true}, {"fieldId": "accommodationRequired"}, {"fieldId": "preferredModeOfCommunication", "displayWidth": "half"}, {"fieldId": "preferredModeOfCommunicationSpecify", "hideIfNotPresent": true}]}, {"title": "Education and employment", "sectionType": "default", "targetActionKey": "ui-educationEmployment", "fields": [{"fieldId": "highestLevelOfEducationAttained", "displayWidth": "half"}, {"fieldId": "instituteOfHigherLearning", "displayWidth": "half"}, {"fieldId": "instituteOfHigherLearningSpecify", "displayWidth": "half", "hideIfNotPresent": true}, {"fieldId": "faculty", "displayWidth": "half"}, {"fieldId": "<PERSON><PERSON><PERSON><PERSON>", "displayWidth": "half"}, {"fieldId": "courseDuration", "displayWidth": "half"}, {"fieldId": "yearOfStudy", "displayWidth": "half"}, {"fieldId": "durationOfInternship", "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"stringJoinPattern": [{"objectPattern": "$.sessionData.ui-educationEmployment.durationOfInternship.from", "datetimeFormat": "dd <PERSON><PERSON> yyyy", "wrap": false, "parseString": "datetime"}, {"objectPattern": "$.sessionData.ui-educationEmployment.durationOfInternship.to", "datetimeFormat": "dd <PERSON><PERSON> yyyy", "wrap": false}], "separator": "-"}]}}, {"fieldId": "typeOfInternship", "displayWidth": "half"}, {"fieldId": "internshipPreferenceRemarks"}, {"fieldId": "personalEducationDetails", "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"arrayMapPattern": "$.sessionData.ui-educationEmployment", "itemMapping": {"educationLevel": {"stringPattern": "{{$.mapItem.educationLevel}}"}, "educationLevelSpecify": {"stringPattern": "{{$.mapItem.educationLevelSpecify}}"}, "school": {"stringPattern": "{{$.mapItem.school}}"}, "fieldOfStudy": {"stringPattern": "{{$.mapItem.fieldOfStudy}}"}, "fieldOfStudySpecify": {"stringPattern": "{{$.mapItem.fieldOfStudySpecify}}"}, "durationOfStudy": {"stringPattern": "{{$.mapItem.durationOfStudy}}"}, "reasonForLeaving": {"stringPattern": "{{$.mapItem.educationLevel}}"}}}]}}, {"fieldId": "employmentStatus", "displayWidth": "half"}, {"fieldId": "occupation", "displayWidth": "half", "hideIfNotPresent": true}, {"fieldId": "situation", "displayWidth": "half", "hideIfNotPresent": true}, {"fieldId": "unemployedSituationSpecify", "hideIfNotPresent": true}, {"fieldId": "providePersonalEmployment", "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"conditionalPattern": "$.sessionData.ui-educationEmployment.providePersonalEmployment", "conditionalCheck": "boolean", "trueValue": "Yes", "falseValue": "No"}]}}]}, {"title": "Caregiver", "sectionType": "default", "targetActionKey": "ui-caregiver<PERSON><PERSON><PERSON>", "fields": [{"fieldId": "haveLegalGuardianCaregiver", "displayWidth": "half", "displayValueTemplate": {"$pattern": [{"conditionalPattern": "$.sessionData.ui-caregiverGuardian.haveLegalGuardianCaregiver", "conditionalCheck": "boolean", "trueValue": "Yes", "falseValue": "No"}]}}]}, {"title": "Supporting documents", "sectionType": "schema", "targetActionKey": "ui-supportingDoc", "disableSchemaContentInset": true, "schema": {"supportingDocsList": {"referenceKey": "supporting-docs-list", "supportingDocs": []}}}, {"title": "Declarations", "sectionType": "default", "targetActionKey": "ui-declaration", "fields": [{"fieldId": "previousConvictionInCourt", "displayValueTemplate": {"$pattern": [{"conditionalPattern": "$.sessionData.ui-declaration.previousConvictionInCourt", "conditionalCheck": "boolean", "trueValue": "Yes", "falseValue": "No"}]}}, {"fieldId": "previousConvictionInCourtSpecify", "hideIfNotPresent": true}, {"fieldId": "interestedServices"}, {"fieldId": "foundSGEnableVia"}, {"fieldId": "foundSGEnableViaSpecify", "hideIfNotPresent": true}]}, {"title": "Terms & Conditions", "sectionType": "default", "targetActionKey": "ui-terms", "fields": [{"fieldId": "consent"}]}], "progressIndicator": {"section": "review"}}}], "userDataTemplate": {"myinfo": ["name", "email", "sex", "dob", "mobile", "residentialstatus", "race", "u<PERSON><PERSON>", "regadd"], "myinfoBusiness": {"history": ["previous-names"], "corppass": ["email", "corppass-user"], "addresses": true, "basic-profile": ["entity-name", "entity-status"]}}, "authRequirement": {"SINGPASS": {"enabled": true, "minLevel": 2}}, "progressIndicators": [{"label": "Profile", "section": "profile"}, {"label": "Disability condition", "section": "disability"}, {"label": "Education and employment", "section": "educationEmployment"}, {"label": "Caregiver", "section": "caregiver<PERSON><PERSON><PERSON>"}, {"label": "Supporting documents", "section": "supportingDoc"}, {"label": "Declaration", "section": "declaration"}, {"label": "Consent", "section": "consent"}, {"label": "Review", "section": "review"}], "serviceConfigName": "ihl v1.0.0"}
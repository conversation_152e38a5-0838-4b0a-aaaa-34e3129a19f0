# EP-A2: SAF Dynamic Merge

- [Summary](#summary)
- [Motivation](#motivation)
  - [Goals](#goals)
  - [Non-Goals](#non-goals)
- [Proposal](#proposal)
  - [Acceptance criteria (Optional)](#acceptance-criteria-optional)
  - [Notes/Constraints/Caveats (Optional)](#notesconstraintscaveats-optional)
  - [Risks and Mitigation](#risks-and-mitigation)
- [Design Details](#design-details)
- [Implementation History](#implementation-history)
- [Alternatives](#alternatives)
- [Infrastructure Needed (Optional)](#infrastructure-needed-optional)

## Summary

To enable users to complete a single application form (SAF) for multiple
schemes. It pulls the questions, contacts card, and application guidance from
each scheme into a single form, Simple labels are added to show which scheme
each question is for. It also keeps pre-filled details working and returns one
final form.

## Motivation

SAF lets applicants apply for multiple schemes in a single submission. Our
current approach hand-crafts a single SAF schema, which only works for a fixed
combination and doesn’t scale as more schemes are added. To support any user
selected mix of schemes, we need to generate the form schema dynamically at
runtime.

### Goals

1. Display the SAF form correctly based on user selected schemes (>1 scheme
   selected)
2. Form prefilling to work properly
3. Application form schema is aligned with the API specs
4. Logs the number of schemes merged and the duration

### Non-Goals

1. Controller functions, routing logic, or DAO functions to fetch data from the
   database.
2. Dynamic logic for any schema properties other than the form schema, contacts,
   and application guidance.
3. Implementation of any caching services to enhance performance.
4. Any schema definition changes apart from those required to support the
   merging logic.
5. Display of array of contacts in an accordion, masthead display and
   application guidance.

## Proposal

1. saf/utils-schema-merge.ts
   1. Merges multiple schemas into a single, unified schema
   2. Merges contact details from a pre-sorted array of schemes into a single
      array of merged contact objects
   3. Merges application guidance from a pre-sorted array of schemes into a
      single, formatted markdown string
2. saf/service.ts
   1. Orchestrates retrieval, logging, merge call, guidance/contacts
      aggregation, prefill rewrite, and final assembly.

### Acceptance criteria (Optional)

Refer to [ticket](https://sgtechstack.atlassian.net/browse/DCUBESDQLR-1883)

### Notes/Constraints/Caveats (Optional)

1. The hybrid merge approach assumes human-authored safId/safIdx on any item
   intended to merge; unannotated items are treated as scheme-specific.
2. Leaf property merge is first-schema-wins, while structural content
   (conditionals, multi-value groups) is merged programmatically.
   1. When two schemas describe the same node (same safId):
      1. Leaf props (label, required, etc.): first schema wins. If the first is
         missing a prop and a later schema has it, keep the later value.
      2. prefillSource: merge like a leaf, then normalize by replacing the
         source namespace with "saf" while keeping the field path.
      3. Structured content (member[], conditional result[], MULTI_VALUE):
         merge/combine across schemas

### Risks and Mitigation

1. Performance issue for large bundled schemes
   1. Implementation of caching services

## Design Details

Merging rules
1. Rule #1: 
- Rule: The shared fields will be marked. Individual scheme-only field will not
   have extra label/config.
- Rationale: to keep things simple, don't need a question bank for individual
      scheme fields. We can just manage and maintain shared fields.

2. Rule #2: 
- Rule: Shared fields will be marked with safId and safIdx. safId is used to
   determine if 2 fields are the same, while safIdx is used to determine the
   order of the field in the combined SAF form.
- Rationale: Have separate safId and safIdx to keep logic more straightforward.

3. Rule #3: 
- Rule: Every node has a __usedIn list (which schemes use it). For each list of 
   children (section.member, conditional result[].member, and group.member 
   inside component like MULTI_VALUE), compare each item to the one just before 
   it. If the set of schemes is different, insert:
  - a HEADER: For <schemeCode> (skip this on the first change at top of a section)
  - an INFO_BLOCK listing those schemes.
- Rationale: This clearly marks where the form switches from one scheme’s fields 
   to another, so users can see which scheme(s) the next fields belong to, 
   while keeping internal markers hidden.

4. Rule #4: 
- Rule: For leaf properties (label, required), first-schema-wins. Inputs are
   pre-sorted by schemeCode to make this deterministic.
- Rationale: Avoid complex policy at this stage. If needed later, we can
      introduce a per-property merge policy table.

5. Rule #5: 
- Rule: Shared fields sorted by safIdx ascending. Scheme specific fields appear
   after, sorted alphabetically by scheme code.
- Rationale: Users see common questions first; scheme-specific fields follow
      after.

6. Rule #6: 
- Rule: If the base (first schema after sort) has no prefillSource but any other
   contributing schema does, adopt that prefillSource (then rewrite to saf.* in
   the post-merge prefill pass). Leaf props still follow first-schema-wins. When
   adopting a prefillSource, if the base doesn’t define editable, inherit the
   donor’s editable.
- Rationale: Keep leaf precedence simple, but don’t lose useful prefill.
      Inheriting editable with the adopted prefill preserves intended UX.

Merging examples
1. Injecting header and infoblock decorators
```
Item 1: __usedIn = ["smta,"scfa"] -> insert infoblock
Item 2: __usedIn = ["smta"] -> insert header and infoblock  
Item 3: __usedIn = ["scfa"] -> insert header and infoblock 
``` 

2. Single-value (leaf) merge, with prefillSource in scheme B
```
{ "safId": "income", "label": "Monthly Household Income", "required": true } // scheme A
{ "safId": "income", "label": "Household Income (per month)", "required": true, "prefillSource": "smta.income", "editable": true } // scheme B
```

Merged result

Before prefill rewrite
```
{
  "type": "FIELD",
  "safId": "income",
  "label": "Monthly Household Income",   // from A (first wins)
  "required": true,
  "prefillSource": "smta.income",        // from B (A had none)
  "editable": true
}
```

After prefill rewrite
```
{
  "type": "FIELD",
  "safId": "income",
  "label": "Monthly Household Income",   // from A (first wins)
  "required": true,
  "prefillSource": "saf.income",         // from B (A had none)
  "editable": true
}
```

3. Conditional merge
```
{
  "type": "SECTION_CONDITIONAL",
  "safId": "employmentStatus",
  "result": [
    { "choice": ["ES1"], "member": [ { "type": "FIELD", "id": "cpfContribution" } ] },
    { "choice": ["ES3"], "member": [ { "type": "FIELD", "safId": "situation", "safIdx": "5" } ] }
  ]
}

{
  "type": "SECTION_CONDITIONAL",
  "safId": "employmentStatus",
  "result": [
    { "choice": ["ES1"], "member": [ { "type": "FIELD", "id": "grossIncome" } ] },
    { "choice": ["ES2"], "member": [ { "type": "FIELD", "id": "proofOfJobSearch" } ] }
    { "choice": ["ES3"], "member": [ { "type": "FIELD", "safId": "situation", "safIdx": "5" } ] }
  ]
}
```

Merged result
```
{
  "type": "SECTION_CONDITIONAL",
  "safId": "employmentStatus",
  "result": [
    {
      "choice": ["ES1"],
      "member": [
        { "type": "FIELD", "id": "cpfContribution" },    
        { "type": "FIELD", "id": "grossIncome" }  
      ]
    },
    {
      "choice": ["ES2"],
      "member": [ { "type": "FIELD", "id": "proofOfJobSearch" } ]
    },
    {
      "choice": ["ES3"],
      "member": [ { "type": "FIELD", "safId": "situation", "safIdx": "5" } ]
    }
  ]
}
```

## Implementation History

## Alternatives

1) Full-Manual (Manually construct a combined schema for every possible
   permutation)
   1) Pros:
      1) Fast runtime as schema construction is done beforehand.
   2) Cons:
      1) Not scalable as changes in a single scheme will require manual changes
         across multiple schemas
2) Full-Auto (Dynamically compare fields and construct schema on-demand)
   1) Pros:
      1) Merging logic handles the combination for the different scheme
         permutations
      2) Litte or no human intervention required
   2) Cons:
      1) Computational cost and the complexity of developing robust, dynamic
         sequencing logic are significant
3) Hybrid (Chosen)
   1) Pros:
      1) Merging logic is simpler and more systematic compared to Full-Auto
      2) More scalable than Full-Manual as the merging logic handles the
         combination. Effort required to add the ids and indexes for the merging
         rules and ordering to work.
   2) Cons:
      1) Better scalability than Full-Manual while being less computationally
         demanding and complex to implement than Full-Auto

## Infrastructure Needed (Optional)

# EP-A1: Caifan scheme selection

- [Summary](#summary)
- [Motivation](#motivation)
  - [Goals](#goals)
  - [Non-Goals](#non-goals)
- [Proposal](#proposal)
  - [FE](#fe)
  - [BE](#be)
  - [Acceptance criteria (Optional)](#acceptance-criteria-optional)
  - [Notes/Constraints/Caveats (Optional)](#notesconstraintscaveats-optional)
  - [Risks and Mitigation](#risks-and-mitigation)
- [Design Details](#design-details)
  - [FE url](#fe-url)
  - [Api specs](#api-specs)
- [Implementation History](#implementation-history)
- [Alternatives](#alternatives)
  - [FE url](#fe-url-1)
  - [Option 2](#option-2)

## Summary

To enable user with the ability to freely select schemes that are relevant to
them to be applied as a single application form (SAF), minimising the
duplicative effort in applying multiple times.

## Motivation

This allows users to cut down application time when applying for multiple
schemes, enabling them to find the support they need quicker. The current
process only allows users to apply and check eligibility for one scheme at a
time, and this feature would allow the user to do so for multiple schemes.

### Goals

1. Display all `SAF` schemes in SAF selection page that user is eligible for
2. Check eligibilty for all `SAF` schemes
3. Redirect user to relevant application pages (SAF overview / single scheme
   dashboard)
4. Handle failure cases where one / more agencies eligibility check is
   unavailable
5. Updating route from individual scheme dashboard page to lead to SAF
   selection page
6. Display confirmation modal to ensure user selected the correct schemes
7. Use flagship DS to create listing & eligibility criteria components

### Non-Goals

1. Check or delete drafts for existing SAF applications (to be handled in
   another ticket)

## Proposal

### FE

1. Install flagship DS and create composite components to display listings
   1. Listing component
2. Call new BE endpoint to retrieve eligible schemes
3. Map eligible scheme schemeCodes to CMS friendlyId
4. Call CMS support API
5. Show modal when user clicks "Apply now" button
6. Handle routing to relevant pages based on schemes selected
   1. If only 1 scheme is selected -> go back to scheme application dashboard
   2. If >1 scheme is selected -> go to SAF dashboard of relevant schemes
7. Display loading state while info is loading

### BE

1. Create an API enpoint that returns the user's eligibility for SAF schemes
   apply for SAF. The endpoint should:
   - retrieve all SAF schemes
   - filter out any scheme that user has an ongoing application for (via
     checking agency status)
   - scheme display logic to follow individual scheme (display if it would
     be shown if applying on it's own)
   - check user's eligibility for all filtered schemes
   - get support listings and return to FE

### Acceptance criteria (Optional)

refer to [ticket](https://sgtechstack.atlassian.net/browse/DCUBESDQLR-1684)

### Notes/Constraints/Caveats (Optional)

- What should be the behaviour if the user has an existing draft and visits
  the SAF selection page directly? Should they be directed to the draft
  dashboard or to another page? -> Handled in draft/ dashboard ticket

### Risks and Mitigation

- When users directly access the SAF page, they might need to be redirected to
  a different page if draft is not checked. Otherwise their existing draft may
  be overwritten.
- Consider if a malicious user spams the page and sends multiple api calls.
  Since calls are made to agency, it would send multiple requests in a short
  period of time.
  - Rate limiting is in place for SGW apis

## Design Details

FE components should use base components from the Flagship design system with
the SupportGoWhere theme. Composite components will be created from these
according to figma screens provided.

For api design, the FE will make a 1 GET request (BE) & 1 GET Request (CMS)
to retrieve all the scheme to be shown to the user. In the BE system, it will
check for eligibility as well as format the data to be sent back to frontend.
The two requests should occur simultaneously.

After selecting the schemes and clicking "Apply now" user should be
redirected to the relevant dashboard page with schemes selected in the url
query params.

```mermaid
sequenceDiagram
  participant FE as Frontend (FE)
  participant BE as Backend (BE)
  participant CMS as CMS

  par FE to BE
  FE->>BE: (GET) Request eligible schemeCodes
  BE->>BE: Get schemeCodes of eligible schemes
  BE->>BE: Get user's eligibility for all SAF schemes
  BE-->>FE: Return eligible schemes
  and FE to CMS
  FE->>CMS: (POST) Call CMS with all scheme friendlyIds
  CMS-->>FE: Return scheme details (cached response)
  end

  FE->>FE: Map schemeCode to friendlyId
  FE->>FE: filter by schemeCodes from BE
  FE->>FE: Display eligible schemes
```

### FE url

- /grants/saf/selection

### Api specs

**HTTP Request**

- Method: GET
- Endpoint: /sgw/saf/app-state
- Auth Required: Yes

**Query Parameters**

- None

| Key                | Type     | Description                                                            |
| ------------------ | -------- | ---------------------------------------------------------------------- |
| `schemeCode`       | `string` | Unique code of the scheme (e.g., `"smta"`, `"scfa"`).                  |
| `applicationState` | `string` | Current state of the application. See **ApplicationState** enum below. |

```
export type ApplicationState =
  | "allow"
  | "disallow"
  | "maintenance"
  | "ineligible"
  | "unavailable"
  | "timeout";
```

**example**

```
[
    {
        "schemeCode": "smta",
        "applicationState": "allow"
    },
    {
        "schemeCode": "scfa",
        "applicationState": "allow"
    }
    ...
]
```

**[EXISTING] GET /v1/sr/schemes?serviceBundle=SGW&friendlyIds=\<friendlyIds>&lang=en**

- Opted to use v1 api for the more detailed response
- documentation can be found on [local swagger docs](http://localhost:8081/v1/sr/docs/#/Schemes/SchemesController_getSchemes)
- use swagger to get example, use "COMCARE-SMTA,SCFA" in friendlyIds query

## Implementation History

- 2025-08-12: Initial proposal created

## Alternatives

### FE url

/grants/saf

- viable alternate option that is shorter than proposed url
- use of page is less explicitly defined
- opted for `/grants/saf/selection` as the intention is more clear (to dev)

### Option 2

- Rejected as it makes the BE very heavy (taking a lot of responsibilities)
- not as scalable as making independant calls
- Any FE change would mean having to update the api response as well

```mermaid
sequenceDiagram
  participant FE as Frontend (FE)
  participant BE as Backend (BE)
  participant CMS as CMS

  FE->>BE: Request eligible schemeCodes

  BE->>BE: Get schemeCodes of eligible schemes
  BE->>BE: Get user's eligibility for all SAF schemes
  BE->>BE: Map schemeCode to friendlyId
  BE->>CMS: Call CMS with friendlyId
  CMS-->>BE: Return scheme details

  BE-->>FE: Return eligible schemes

  FE->>FE: Display eligible schemes
```

api example

```
[
  {
    "title": "Student Care Fee Assistance (SCFA)",
    "friendlyId": "SCFA",
    "schemeCode": "scfa",
    "keywords": [
        "Students",
        "Subsidies"
    ],
    "description": "Provides temporary financial assistance for lower-income individuals or families who are temporarily unable to work, looking for a job or earning a low income and require assistance."
  },
{
    "title": "ComCare Short-to-Medium-Term Assistance (SMTA)",
    "friendlyId": "COMCARE-SMTA",
    "schemeCode": "smta",
    "keywords": [
        "Cash assistance",
        "Lower-income",
        "Short-to-medium term"
    ],
    "description": "Provides temporary financial assistance for lower-income individuals or families who are temporarily unable to work, looking for a job or earning a low income and require assistance."
  },
]
```

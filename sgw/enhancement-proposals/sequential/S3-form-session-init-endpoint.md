# EP-S3: Form Session Initialization Endpoint

<!--
This is the title of your EP. Keep it short, simple, and descriptive. A good
title can help communicate what the EP is and should be considered as part of
any review.
-->

<!--
A table of contents is helpful for quickly jumping to sections of a EP and for
highlighting any additional information provided beyond the standard EP
template.

To generate TOC and update it easily, install markdown-all-in-one extension
https://marketplace.visualstudio.com/items?itemName=yzhang.markdown-all-in-one.

- As a rule of thumb, set the TOC levels to 2..4 for the extension setting
-->

- [Summary](#summary)
- [Motivation](#motivation)
  - [Goals](#goals)
  - [Non-Goals](#non-goals)
- [Proposal](#proposal)
  - [Acceptance criteria (Optional)](#acceptance-criteria-optional)
    - [AC 1](#ac-1)
    - [AC 2](#ac-2)
    - [AC 3](#ac-3)
  - [Notes/Constraints/Caveats (Optional)](#notesconstraintscaveats-optional)
  - [Risks and Mitigation](#risks-and-mitigation)
- [Design Details](#design-details)
  - [Modular and Extensible Design:](#modular-and-extensible-design)
  - [Flow Overview:](#flow-overview)
  - [Referenced Files:](#referenced-files)
    - [File: domains/sequential/routes.ts:](#file-domainssequentialroutests)
    - [File: domains/sequential/controllers.ts](#file-domainssequentialcontrollersts)
    - [File: domains/sequential/service.ts](#file-domainssequentialservicets)
    - [Example response from service:](#example-response-from-service)
    - [Example error response from controller:](#example-error-response-from-controller)
- [Alternatives](#alternatives)
- [Infrastructure Needed (Optional)](#infrastructure-needed-optional)

## Summary

<!--
In this section, you will briefly explain what the proposal is about, without
going into too much technical details. A reader of this section should be able
to go "Oh, I know **what** you are trying to achieve, though i do not know
exactly **how** you are going to do that, or **how** does this impact the rest
of the system. But hey, at least i know where you're going on this so that i
can make sense of the rest of the EP"

Both in this section and below, follow the guidelines of the documentation
style guide
https://github.com/kubernetes/community/blob/master/contributors/guide/style-guide.md
where it make sense for us. In particular, wrap lines to a reasonable length
(80 characters) using a repository co-pilot prompt: `/80-char-markdown-formatter`
-->

This proposal introduces an API endpoint for Sequential forms to retrieve
initial data required to start a session. It enables integration between
Sequential and SGW, providing a foundation for future extensibility.

## Motivation

<!--
This section is for explicitly listing the motivation. Describe why the change
is important/necessary and the benefits to users. Usually this is for you to
list down more context of what is existing behavior and what's the need for
the change
-->

The endpoint is essential for initializing Sequential forms with all necessary
data. It supports features like pre-filling, eligibility checks, and dynamic
content based on requirements. The logic can fetch scheme-specific data, allowing
tailored responses for each scheme. This connection streamlines the user experience
and supports future extensibility as new requirements emerge.

### Goals

<!--
List the specific goals of the EP. What is it trying to achieve? How will we
know that this has succeeded?
-->

- Create an endpoint that retrieves user data and supports scheme-specific logic
  for Sequential forms, with easy extension for future requirements.
- Provide a placeholder response for initial implementation, with structure for
  future enhancements.

### Non-Goals

<!--
What is out of scope for this EP? Listing non-goals helps to focus discussion
and make progress.
-->

- Only the entry route is implemented. Other routes (refresh, submit) are out
  of scope for this proposal.

## Proposal

<!--
This is where we get down to the specifics of what the proposal actually is.
This should have enough detail that reviewers can understand exactly what
you're proposing, but should not include things like API designs or
implementation. What is the desired outcome and how do we measure success?.
The "Design Details" section below is for the real nitty-gritty.
-->

This proposal introduces a modular API endpoint in SGW for Sequential form
integration. The route `/sequential/form/:schemeCode/init` is called when a
user is redirected to a Sequential form. At this point, a custom action in
Sequential triggers a webhook to SGW, which processes the request and returns
the required data for the form session. Once the data is received, the
Sequential UI proceeds, allowing the user to continue with their form.

The controller validates the `schemeCode` against the SGW database to ensure
there is an active Sequential service id. If valid, the service fetches and
returns the required data. The response is structured for easy consumption
by Sequential forms. The controller, routes, and service are modular for
future extension. Special logic for specific schemes can be added in the
service layer.

### Acceptance criteria (Optional)

<!--
Detail the things that people will be able to do if this EP is implemented.
Include as much detail as possible so that people can understand the "how" of
the system. The goal here is to make this feel real for users without getting
bogged down.
-->

#### AC 1

A new endpoint is created and hosted on the SGW backend to serve as the Access
Webhook.

#### AC 2

The endpoint's logic uses a schemeCode (or similar identifier passed in the
request's extraData) to dynamically load the appropriate data-fetching logic
for the given scheme.

#### AC 3

For the initial implementation, the endpoint will contain placeholder
logic for a single, hardcoded test scheme.

Refer to [ticket](https://sgtechstack.atlassian.net/browse/DCUBESDQLR-1940)

### Notes/Constraints/Caveats (Optional)

<!--
What are the caveats to the proposal?
What are some important details that didn't come across above?
Go in to as much detail as necessary here.
-->

- The implementation serves as a base. Additional features will be added as
  future requirements arise.
- The controller checks the SGW database to verify if the scheme is supported
  for Sequential forms.
- The service returns a structured object for easy consumption by Sequential.

### Risks and Mitigation

<!--
What are the risks of this proposal, and how do we mitigate? Think broadly.
For example, consider security, UX and performance risk.
-->

- Security: Validate if `schemeCode` has an active scheme in the SGW database
  before returning data.
- Authentication: JWT token verification is enforced for inbound API calls
  from Sequential. The token is checked for validity and session existence.
- IP Whitelisting: Only allow inbound traffic from trusted Sequential IPs.
  - **Note**: CloudFront can restrict IPs globally, but cannot filter by route.
- UX: Error responses are clear and actionable for Sequential users.
- Performance: Data fetch logic is optimized and can be extended as requirements
  grow.

## Design Details

<!--
This section should contain enough information that the specifics of your
change are understandable. This may include API specs (though not always
required) or even code snippets. If there's any ambiguity about HOW your
proposal will be implemented, this is the place to discuss them.
-->

**Note**: Only the session initialization flow is covered in this
enhancement proposal. Submission and refresh flows are out of scope and
not described here.

### Modular and Extensible Design:

The design is modular, allowing future extension and scheme-specific logic.
The service function `fetchUserData` retrieves and compiles user, application,
and scheme-specific data. If new requirements arise, such as additional MyInfo
prefill fields or eligibility checks, developers can simply add new keys or
logic to the service response. This avoids major refactoring and supports rapid
iteration. Each scheme can have its own conditional logic, making the service
layer flexible and maintainable. Consider enhancing the scheme database in the
future by adding a list of service names to enable dynamic service processing
without hardcoded scheme-to-service mappings.

### Flow Overview:

- The Sequential session initialization flow starts with a POST request to
  `/sequential/form/:schemeCode/init`.
- The controller validates the scheme code and manages error handling.
- Business logic and data processing are delegated to the service layer,
  which returns a structured response for Sequential forms.

### Referenced Files:

#### File: domains/sequential/routes.ts:

- Registers the route prefix `/sequential` for Sequential integration.
- Defines POST `/form/:schemeCode/init` mapped to `getUserData` function.

#### File: domains/sequential/controllers.ts

- `getUserData(ctx)`: Validates the scheme code, handles errors, and
  calls fetchUserData in the service layer.

#### File: domains/sequential/service.ts

- `fetchUserData(ctx, schemeCode)`: Fetches user details and returns a
  structured response object. Easily extensible for new data requirements.

#### Example response from service:

```json
{
  "applications": [{ "status": "pending", "refId": "ABC-12345" }],
  "prefills": {
    "myInfo": { "nric": "*********", "name": "John Doe" }
  }
}
```

#### Example error response from controller:

```json
{
  "errorDetails": {
    "type": "warning",
    "title": "Unable to start session",
    "description": ["We are unable to proceed with your session"],
    "actionType": "logout",
    "actionLabel": "Logout now",
    "redirectUrl": "https://supportgowhere.life.gov.sg/"
  }
}
```

## Alternatives

<!--
What other approaches did you consider, and why did you rule them out? These
do not need to be as detailed as the proposal, but should include enough
information to express the idea and why it was not acceptable.
-->

Passing `schemeCode` in `extraData` was considered, but this is only set when
Sequential is created from a Requestor. With SSO, `sessionId` is added as
additional data, and `schemeCode` could technically be included as well.
However, `schemeCode` is a static value and may not be suitable to be grouped with
data intended to be dynamic. Placing `schemeCode` in the URL is more practical,
improves tracking, and aligns better with the integration flow.

## Infrastructure Needed (Optional)

<!--
Use this section if you need things from the project. Example you need an S3
bucket, you need SQS.
-->

- Add `/sequential/*` to CloudFront and application load balancer routing
  rules to support Sequential integration.

# EP-2: Delegated SSO with session creation

- [EP-2: Delegated SSO with session creation](#ep-2-delegated-sso-with-session-creation)
  - [Summary](#summary)
  - [Motivation](#motivation)
    - [Goals](#goals)
    - [Non-Goals](#non-goals)
  - [Proposal](#proposal)
    - [Notes/Constraints/Caveats (Optional)](#notesconstraintscaveats-optional)
    - [Risks and Mitigation](#risks-and-mitigation)
  - [Design Details](#design-details)
  - [Operational Checkpoints](#operational-checkpoints)
  - [Implementation History](#implementation-history)
  - [Alternatives](#alternatives)

## Summary

This proposal aims to enable delegated Single Sign-On (SSO) with session creation for the Sequential application. The goal is to allow users who are already authenticated on our site to access Sequential without having to log in again, providing a seamless and faster user experience and a better integration between the 2 system.

## Motivation

Currently, Support Go Where (SGW) and Sequential are distinct applications that require separate user authentication. This results in users having to log in twice when moving from SGW to Sequential, creating unnecessary friction and disrupting the user journey.

This initiative proposes implementing a Delegated Single Sign-On (SSO) flow to extend an authenticated SGW session to the Sequential application. By delegating session creation, <PERSON><PERSON> can trust the authenticated session from SGW and establish its own session without prompting the user to log in again.

The primary goal is to eliminate the need for users to log in twice, thereby providing a smoother, more integrated experience between SGW and Sequential.

### Goals

- Ensure users authenticated on SGW can access Sequential directly without re-authenticating via delegated SSO.
- Ensure delegated SSO works consistently across all deployed environments (dev, tst, stg, prod).
- Enable SSO session delegation to work for future schemes as needed not just IHL scheme.

### Non-Goals

- Ensuring webhooks from Sequential are able to be sent to the AppGen backend successfully.

## Proposal

- Generate and manage separate sets of ES256 JWK for non-production and production environments.
- Expose a standard JWKS endpoint for Sequential to fetch SGW's public keys.
- Build a secure endpoint on SGW to generate delegation token (JWT) for authenticated users.
- Extend the AppGen database to support scheme-level configuration.
- Update the SGW frontend to use this delegation flow when navigating to Sequential.

### Notes/Constraints/Caveats (Optional)

- Must work across all deployed environments consistently.
- Ensure logging is in place for operations.
- Rotate keys periodically as part of operational security.

### Risks and Mitigation

- Leaked Keys
  - Maintain separate keys for non-prod and prod.
  - Store private keys in only secure environment variables.
- Token Theft
  - Token has a short expiry of 3mins defined and enforced through validation on Sequential's end.
- DDoS attacks for token generation endpoint
  - Sits behind SessionGuard, which only allows authenticated users to reach this endpoint.
  - Rate Limiting (30,000 requests per 5 minutes).
  - Scheme code validation.

## Design Details

1. Expose JWKS
   - Use existing endpoint: `https://supportgowhere.life.gov.sg/assets/jwks.json`
     - This endpoint will serve the public JWK Set (JWKS), and allows the Sequential system to fetch SGW’s public keys to validate the delegation token we send to them.
2. Create a secure token generation endpoint

   - This endpoint is used to enable seamless SSO delegation integration between SGW and Sequential. It allows authenticated SGW users to obtain a JWT delegation token and the mapped Sequential service ID (`sqServiceId`) for a given scheme.

     - SGW frontend will require the mapped `sqServiceId` to redirect to sequential's form.
     - Generated signed JWT delegation token will be according to [specs](https://www.dev.lifesg.io/icecap/docs#section/Guides/How-to-use-Delegated-SSO-with-session-creation) provided by Sequential.
       - `kid` = `sgw_appgen_sig_01`
       - `iss` = `sgw-schemes`

   - **API Specification:**

     - **Endpoint:** `/sgw/:schemeCode/sso/auth`
     - **Method:** `GET`
     - **Authentication:**
       - Requires valid SGW session (SessionGuard middleware).
       - Only authenticated users can access.
     - **Path Parameter:**
       - `schemeCode` (string, required): The scheme code to map to `sqServiceId`.
     - **Response:**

       - `200 OK`

         ```json
         {
           "token": "<JWT delegation token>",
           "sqServiceId": "<UUID>"
         }
         ```

     - **HTTP Errors:**
       - `400 Bad Request`: Missing or invalid schemeCode.
       - `401 Unauthorized`: User not authenticated.
       - `403 Forbidden`: User not authorized for the requested scheme.
       - `429 Too Many Requests`: Rate limit exceeded.
       - `500 Internal Server Error`: Unexpected error during token generation.

3. Extend AppGen DB
   - Add a new optional column `sqServiceId` (UUID) to the Tagging table.
   - Add a new column `sqServiceId` (optional) to `schemeDetails` table in AppGen DB.
     - This is needed for mapping of sgw's `schemeId` and `sqServiceId` in the token generation endpoint.
     - Run seeding script to populate column once Sequential service ID is confirmed.
       - This will be done for each environment.
     - Leave this column empty for existing AppGen schemes.

## Operational Checkpoints

- Share the JWKS endpoint (`https://supportgowhere.life.gov.sg/assets/jwks.json`) with the Sequential team for their integration.
- Whitelist Sequential's server IPs for our non-production environments.

## Implementation History

| Date (latest first) | Description | MR  | Changes made    |
| ------------------- | ----------- | --- | --------------- |
| 2025-07-10          | First draft | NA  | Initial version |

## Alternatives

\-

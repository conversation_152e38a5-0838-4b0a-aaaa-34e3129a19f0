import { loadEnvVars } from "@sgw/common";

class Config {
  private _config = {
    aws: {
      s3: {
        AWS_S3_RETRY_BUCKET: "",
        AWS_S3_ATTACHMENT_BUCKET: "",
      },
      rds: {
        AWS_RDS_HOST: "",
        AWS_RDS_PORT: 0,
        AWS_RDS_DBNAME: "",
        AWS_RDS_USER: "",
        AWS_RDS_PASSWORD: "",
      },
    },
    notification: {
      email: {
        EMAIL_FROM: "",
        EMAIL_HOST: "",
        EMAIL_PORT: 0,
        EMAIL_SECURE: false,
        EMAIL_REJECT_UNAUTHORIZED: false,
      },
    },
    ssnet: {
      api: {
        SSNET_CALL_TIMEOUT_MS: 5000,
        SSNET_BASE_URL: "",
        SSNET_ENC_KEY: "",
        SSNET_CLIENT_ID: "",
        SSNET_CLIENT_SECRET: "",
        SSNET_PROVISION_KEY: "",
        SSNET_USER_ID: "",
      },
    },
    appgen: {
      APPGEN_ENC_PRIVATE_KEY: "",
      APPGEN_SIG_PRIVATE_KEY: "",
      APPGEN_SIG_KID: "",
      COMLINK_ACCESS_KEY_ID: "",
      COMLINK_ACCESS_KEY_SECRET: "",
      DRAFT_ENC_PRIVATE_KEY: "",
      DRAFT_ENC_JWKS: "",
      CONSENT_ENC_PRIVATE_KEY: "",
      CONSENT_ENC_JWKS: "",
    },
    msfp3: {
      MSFP3_ACCESS_TOKEN_URL: "",
      MSFP3_ACCESS_PRIVATE_KEY: "",
      MSFP3_ACCESS_ISSUER_ID: "",
      MSFP3_ACCESS_SUBJECT: "",
      MSFP3_ACCESS_AUDIENCE: "",
      PWDR_ELIGIBILITY_URL: "",
    },
    sequential: {
      RSH_SI_ATTACHMENT_URL: "",

      SEQUENTIAL_ISSUER: "",
      SEQUENTIAL_JWKS_URL: "",
    },
  };

  public get aws() {
    return this._config.aws;
  }

  public get notification() {
    return this._config.notification;
  }

  public get ssnet() {
    return this._config.ssnet;
  }

  public get appgen() {
    return this._config.appgen;
  }

  public get msfp3() {
    return this._config.msfp3;
  }

  public get sequential() {
    return this._config.sequential;
  }

  public init(): void {
    for (const property in this._config) {
      loadEnvVars(this._config[property]);
    }
  }
}

export const serviceConfig = new Config();

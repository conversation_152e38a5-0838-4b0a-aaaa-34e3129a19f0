import { <PERSON><PERSON> } from "knex";
import { dbClient } from "./dbclient";
import { schemeDao } from "./schemedao";

export interface Consent {
  consenterUuid: string;
  refId: string;
  userUuid: string;
  consenterDetails?: string;
  isProvided?: boolean;
  providedDateTime?: Date;
}

interface UserConsentStatus {
  refId: string;
  schemeCode: string;
  schemeName: string;
  appliedDateTime: Date;
  isProvided: boolean;
}

class ConsentDao {
  private TABLE_NAME = "consent";

  public async insertTrx(consents: Consent[], trx: Knex.Transaction) {
    return await dbClient.knex<Consent>(this.TABLE_NAME).insert(consents).transacting(trx);
  }

  public async get(consenterUuid: string, refId: string) {
    const result = await dbClient.knex<Consent>(this.TABLE_NAME).select("*").where({ consenterUuid, refId });

    return result.length > 0 ? result[0] : undefined;
  }

  public async getPendingSgwConsent(consenterUuid: string, refId: string): Promise<Consent | undefined> {
    const result = await this.getUserAllSgwPendingConsentQuery(consenterUuid)
      .select("consent.refId", "consent.isProvided", "consent.userUuid")
      .andWhere("application.refId", "=", refId);

    return result.length > 0 ? result[0] : undefined;
  }

  public async getPendingSgwConsents(consenterUuid: string): Promise<UserConsentStatus[]> {
    const result = await this.getUserAllSgwPendingConsentQuery(consenterUuid).select(
      "scheme.code as schemeCode",
      "scheme.name as schemeName",
      "application.refId",
      "application.appliedDateTime",
      "consent.isProvided",
    );

    return result;
  }

  public async getByRefId(refId: string) {
    return await dbClient.knex<Consent>(this.TABLE_NAME).select("*").where({ refId });
  }

  public async getConsenterDetails(refId: string): Promise<Record<string, any>[]> {
    return await dbClient.knex<Consent>(this.TABLE_NAME).select("consenterDetails").where({ refId });
  }

  public async update(consenterUuid: string, refId: string, userUuid: string) {
    return await dbClient
      .knex<Consent>(this.TABLE_NAME)
      .where({ consenterUuid, refId, userUuid })
      .update({ isProvided: true, providedDateTime: new Date() });
  }

  private getUserAllSgwPendingConsentQuery(consenterUuid: string) {
    return dbClient
      .knex(this.TABLE_NAME)
      .innerJoin("application", function () {
        this.on("application.refId", "=", "consent.refId").andOn("application.userUuid", "=", "consent.userUuid");
      })
      .innerJoin("schemeDetails", "application.schemeCode", "=", "schemeDetails.schemeCode")
      .innerJoin("scheme", "application.schemeCode", "=", "scheme.code")
      .whereIn("application.statusCode", ["0", "1", "2", "20", "21"])
      .andWhere("consent.consenterUuid", "=", consenterUuid)
      .andWhereRaw(schemeDao.deprecated_activeSchemeDetailsQuery())
      .orderBy([
        { column: "consent.isProvided", order: "asc" },
        { column: "application.appliedDateTime", order: "asc" },
      ]);
  }
}

export const consentDao = new ConsentDao();

import * as jose from "jose";
import { Knex } from "knex";

import { logger } from "@sgw/common";

import { dbClient } from "./dbclient";

export interface Scheme {
  code: string;
  name: string;
  key?: jose.JSONWebKeySet;
  jwksUrl?: string;
  appStatusUrl: string;
  appSubmissionUrl: string;
}

export interface SchemeDetails {
  id?: string;
  schemeCode: string;
  schema: string;
  sqServiceId?: string;
  startDateTime: Date;
  endDateTime?: Date;
}

class SchemeDao {
  private TABLE_NAME = "scheme";

  public async getSchema(schemeCode: string) {
    const query = `select schema from sgw."schemeDetails" where "schemeCode" = '${schemeCode}' and ${this.deprecated_activeSchemeDetailsQuery()}`;
    const result = await dbClient.knex.raw(query);

    return result.rows.length > 0 ? result.rows[0] : undefined;
  }

  public async getSchemas(schemeCodes: string[]) {
    if (schemeCodes.length === 0) return [];

    const query = dbClient
      .knex<SchemeDetails>("schemeDetails")
      .withSchema("sgw")
      .select("schema")
      .whereIn("schemeCode", schemeCodes);

    return await this.appendActiveQuery(query);
  }

  public async getActiveSafSchemes(safSchemeCodes: string[]) {
    if (safSchemeCodes.length === 0) return [];

    const query = dbClient
      .knex<SchemeDetails>("schemeDetails")
      .withSchema("sgw")
      .select("schemeCode")
      .whereIn("schemeCode", safSchemeCodes)
      .andWhereRaw(`"schema" @> '{"bundleSchemeCode": "saf"}'`);

    return await this.appendActiveQuery(query);
  }

  public async getScheme(schemeCode: string) {
    const result = await dbClient.knex<Scheme>(this.TABLE_NAME).select("*").where({ code: schemeCode });

    return result.length > 0 ? result[0] : undefined;
  }

  public async getActiveScheme(schemeCode: string) {
    const query = `select * from sgw."scheme" where "code" = (select "schemeCode" from sgw."schemeDetails" where "schemeCode" = '${schemeCode}' and ${this.deprecated_activeSchemeDetailsQuery()})`;
    const result = await dbClient.knex.raw(query);

    return result.rows.length > 0 ? result.rows[0] : undefined;
  }

  public activeSchemeDetailsIdQuery(schemeCode: string) {
    return `select id from sgw."schemeDetails" where "schemeCode" = '${schemeCode}' and ${this.deprecated_activeSchemeDetailsQuery()}`;
  }

  public async getActiveSchemaId(schemeCode: string) {
    const result = await dbClient.knex.raw(this.activeSchemeDetailsIdQuery(schemeCode));

    return result.rows.length > 0 ? result.rows[0] : undefined;
  }

  public async upsertSchema(scheme: Scheme, schemeDetails: SchemeDetails) {
    try {
      const latestId = await this.getActiveSchemaId(scheme.code);
      if (latestId) {
        logger.debug(`updating ${scheme.code} schema with id ${latestId.id}`);
        await dbClient
          .knex<SchemeDetails>("schemeDetails")
          .update({ schema: schemeDetails.schema })
          .where({ id: latestId.id });
      } else {
        logger.debug(`inserting new scheme ${scheme.code}`);
        await dbClient.knex.transaction(async (trx: Knex.Transaction) => {
          await dbClient.knex<Scheme>(this.TABLE_NAME).insert(scheme).transacting(trx);
          await dbClient.knex<SchemeDetails>("schemeDetails").insert(schemeDetails).transacting(trx);
        });
      }
    } catch (error) {
      logger.error("error upserting application schema, rolling back", error);
      throw error;
    }
  }

  // deprecated as it's using raw query. Developer should use knex query or `appendActiveQuery` method below instead
  // this function is kept for backward compatibility
  public deprecated_activeSchemeDetailsQuery() {
    const now = new Date().toISOString();
    return `"startDateTime" <= '${now}' and ("endDateTime" is null or "endDateTime" > '${now}')`;
  }

  // helper function to append active scheme details query to existing knex query
  private appendActiveQuery<TResult>(query: Knex.QueryBuilder<SchemeDetails, TResult>) {
    const now = new Date().toISOString();
    return query.where("startDateTime", "<=", now).andWhere(function () {
      this.whereNull("endDateTime").orWhere("endDateTime", ">", now);
    });
  }

  public async getActiveSqServiceId(schemeCode: string) {
    const query = dbClient
      .knex<SchemeDetails>("schemeDetails")
      .withSchema("sgw")
      .select("sqServiceId")
      .where("schemeCode", schemeCode);

    const result = await this.appendActiveQuery(query);
    return result.length > 0 ? result[0] : undefined;
  }
}

export const schemeDao = new SchemeDao();

import { dbClient } from "./dbclient";
import { schemeDao } from "./schemedao";

export interface Draft {
  userUuid: string;
  schemeDetailsId: number;
  content: string;
  updatedDatetime: Date;
}

export interface UserDraft {
  schemeCode: string;
  schemeName: string;
  updatedDatetime: Date;
}

class DraftDao {
  private TABLE_NAME = "draft";

  public async getDraft(userUuid: string, schemeCode: string) {
    const query = `select content, "updatedDatetime" from sgw.draft where "userUuid" = ? and "schemeDetailsId" = (${schemeDao.activeSchemeDetailsIdQuery(
      schemeCode,
    )})`;

    const result = await dbClient.knex.raw(query, userUuid);

    return result.rows.length > 0 ? result.rows[0] : undefined;
  }

  public async getUserDraft(userUuid: string) {
    const result = await dbClient
      .knex(this.TABLE_NAME)
      .join("schemeDetails", "draft.schemeDetailsId", "=", "schemeDetails.id")
      .join("scheme", "scheme.code", "=", "schemeDetails.schemeCode")
      .select("schemeDetails.schemeCode", "scheme.name as schemeName", "draft.updatedDatetime")
      .where("draft.userUuid", "=", userUuid)
      .andWhereRaw(schemeDao.deprecated_activeSchemeDetailsQuery())
      .orderBy("draft.updatedDatetime", "asc");

    return result as UserDraft[];
  }

  public async getExpiredDraft(expireDate: Date) {
    return await dbClient
      .knex<Draft>(this.TABLE_NAME)
      .join("schemeDetails", "draft.schemeDetailsId", "=", "schemeDetails.id")
      .select("draft.userUuid", "draft.updatedDatetime", "schemeDetails.schemeCode")
      .where("draft.updatedDatetime", "<", expireDate);
  }

  public async isDraftExist(userUuid: string, schemeCode: string) {
    const draft = await dbClient
      .knex<Draft>(this.TABLE_NAME)
      .join("schemeDetails", "draft.schemeDetailsId", "=", "schemeDetails.id")
      .join("scheme", "schemeDetails.schemeCode", "=", "scheme.code")
      .select("draft.userUuid", "scheme.code")
      .where("draft.userUuid", "=", userUuid)
      .andWhere("scheme.code", "=", schemeCode);

    return draft.length > 0;
  }

  public async deleteExpiredDraft(expireDate: Date) {
    return await dbClient.knex<Draft>(this.TABLE_NAME).where("updatedDatetime", "<", expireDate).del();
  }

  public async upsert(userUuid: string, schemeCode: string, content: string, updatedDatetime: Date) {
    const query = `insert into sgw.draft("userUuid", "schemeDetailsId", content, "updatedDatetime") 
                    values(?, (${schemeDao.activeSchemeDetailsIdQuery(schemeCode)}), ?, ?)
                    on conflict on constraint draft_pkey
                      do update set content = EXCLUDED.content, "updatedDatetime" = EXCLUDED."updatedDatetime"`;

    await dbClient.knex.raw(query, [userUuid, content, updatedDatetime]);
  }

  public async delete(userUuid: string, schemeCode: string) {
    const query = `delete from sgw.draft where "userUuid" = ? and "schemeDetailsId" = (${schemeDao.activeSchemeDetailsIdQuery(
      schemeCode,
    )})`;

    await dbClient.knex.raw(query, userUuid);
  }
}

export const draftDao = new DraftDao();

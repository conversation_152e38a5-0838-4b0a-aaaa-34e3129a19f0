import DayJS from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import z from "zod";

import { fileValidator } from "../customgroup";
import { ERROR_CODE, Options, stringValidator, getValidValues } from "./helper";

export { CustomFieldType } from "./helper";

DayJS.extend(isSameOrAfter);

const DEFAULT_MIN_DATE = new Date("1900-01-01");
const DEFAULT_MAX_DATE = new Date("2099-12-31");

export const inputTextValidator = (maxLength = 10, isOptional = false) => {
  return stringValidator(maxLength, isOptional).refine(validateAlphanumeric, {
    message: ERROR_CODE.ONLY_ALPHANUMERIC,
  });
};

export const customTextValidator = (maxLength?: number, isOptional?: boolean) => {
  const validator = inputTextValidator(maxLength, isOptional);

  return isOptional ? validator.optional() : validator;
};

const validateAlphanumeric = (value: string) => {
  return /^[A-Za-z0-9]*$/.test(value);
};

export const freeTextValidator = (maxLength = 256, isOptional = false) => {
  return stringValidator(maxLength, isOptional).refine(validateFreeText, {
    message: ERROR_CODE.INVALID_FREE_TEXT,
  });
};

export const customFreeTextValidator = (maxLength?: number, isOptional?: boolean) => {
  const validator = freeTextValidator(maxLength, isOptional);

  return isOptional ? validator.optional() : validator;
};

const validateFreeText = (value: string) => {
  return /^[A-Za-z0-9 .,'():;\-/?@]*$/.test(value);
};

export const multilineTextValidator = (maxLength = 300, isOptional = false) => {
  const validator = stringValidator(maxLength, isOptional).refine(validateMultilineText, {
    message: ERROR_CODE.INVALID_FREE_TEXT,
  });

  return isOptional ? validator.optional() : validator;
};

const validateMultilineText = (value: string) => {
  return /^[A-Za-z0-9 .,'():;\-/?@\n]*$/.test(value);
};

export const textNumberValidator = (maxLength = 5, isOptional = false) => {
  return stringValidator(256, isOptional)
    .max(maxLength, { message: `${ERROR_CODE.EXCEED_MAX_DIGIT}${maxLength}` })
    .refine(validateNumber, {
      message: ERROR_CODE.ONLY_NUMERIC,
    });
};

export const customTextNumberValidator = (maxLength?: number, isOptional?: boolean) => {
  const validator = textNumberValidator(maxLength, isOptional);

  return isOptional ? validator.optional() : validator;
};

const validateNumber = (value: string) => {
  return /^\d*$/.test(value);
};

export const radioValidator = (options: Options, isOptional = false) => {
  const validValues = getValidValues(options);

  return stringValidator(256, isOptional).refine((val) => (val ? validValues.includes(val) : true), {
    message: ERROR_CODE.INVALID_OPTION,
  });
};

export const customRadioValidator = (options: Options, isOptional = false) => {
  const validator = radioValidator(options, isOptional);

  return isOptional ? validator.optional() : validator;
};

export const dropdownValidator = (options: Options, isOptional = false) => {
  const validValues = getValidValues(options);

  return stringValidator(256, isOptional).refine((val) => (val ? validValues.includes(val) : true), {
    message: ERROR_CODE.INVALID_OPTION,
  });
};

export const customDropdownValidator = (options: Options, isOptional = false) => {
  const validator = dropdownValidator(options, isOptional);

  return isOptional ? validator.optional() : validator;
};

export const checkboxValidator = (isOptional = false) => {
  return z
    .boolean({ required_error: ERROR_CODE.REQUIRED_ACKNOWLEDGEMENT })
    .refine((arg) => (isOptional ? true : arg === true), {
      message: ERROR_CODE.REQUIRED_ACKNOWLEDGEMENT,
    });
};

export const customSingleCheckValidator = (isOptional?: boolean) => {
  const validator = checkboxValidator(isOptional);

  return isOptional ? validator.optional() : validator;
};

export const checkBoxValidator = (options: Options, isOptional = false) => {
  const stringArrayValidator = z.array(z.string(), {
    required_error: ERROR_CODE.REQUIRED,
  });

  const validator = isOptional
    ? stringArrayValidator.optional()
    : stringArrayValidator.nonempty({
        message: ERROR_CODE.REQUIRED,
      });

  const validValues = getValidValues(options);

  return validator.refine(
    (vals) => {
      return vals !== undefined ? vals.every((val) => validValues.includes(val)) : true;
    },
    {
      message: ERROR_CODE.INVALID_OPTION,
    },
  );
};

const dateStringValidator = (isOptional = false, maxDate?: string) => {
  return stringValidator(256, isOptional).refine((val) => (val ? validateDateString(val, maxDate) : true), {
    message: ERROR_CODE.INVALID_OPTION,
  });
};

export const customTextDateValidator = (isOptional?: boolean) => {
  const validator = dateStringValidator(isOptional);

  return isOptional ? validator.optional() : validator;
};

export const customDateRangeValidator = (isOptional?: boolean) => {
  const validator = z
    .array(z.string(), {
      message: ERROR_CODE.REQUIRED,
    })
    .length(2)
    .refine(
      // when only end date is not specified, provide a more descriptive error message
      ([startDate, endDate]) => isOptional || !startDate || endDate,
      { message: ERROR_CODE.REQUIRED_END_DATE },
    )
    .refine(
      // required validation
      ([startDate, endDate]) => {
        const dateValidation = customTextDateValidator(isOptional);
        return dateValidation.safeParse(startDate).success && dateValidation.safeParse(endDate).success;
      },
      { message: ERROR_CODE.REQUIRED },
    )
    .refine(
      // start date cannot be later than end date validation
      ([startDate, endDate]) => {
        if (!startDate || !endDate) {
          return true;
        }
        return DayJS(endDate).isSameOrAfter(DayJS(startDate));
      },
    );

  return isOptional ? validator.optional() : validator;
};

export const validateDateString = (value: string, maxDate?: string) => {
  const validator = z
    .date()
    .min(DEFAULT_MIN_DATE)
    .max(maxDate ? new Date(maxDate) : DEFAULT_MAX_DATE);

  return validator.safeParse(new Date(value)).success;
};

export const moneyValidator = (isOptional = false, allowNegative = false) => {
  const validator = stringValidator(256, isOptional)
    .max(10, `${ERROR_CODE.EXCEED_MAX_DIGIT}9`) // Negative value can only have max 8 digits (two character count are reserved for decimal point and negative sign) but it doesn't have a separate error message. This is because negative value's error message is rare to occur.
    .refine((val) => validateAllowNegative(val, allowNegative), {
      message: ERROR_CODE.ONLY_NUMERIC, // this error code is used as catch-all error
    })
    .refine((val) => (val ? validateTwoDecimal(val) : true), {
      message: ERROR_CODE.ONLY_NUMERIC, // this error code is used as catch-all error
    });

  return isOptional ? validator.optional() : validator;
};

const validateAllowNegative = (val: string, allowNegative: boolean) => {
  if (val.startsWith("-") && !allowNegative) {
    return false;
  }

  return true;
};

export const validateTwoDecimal = (value: string): boolean => /^-?(0|([1-9][0-9]*))\.[0-9]{2}$/.test(value);

const getMinMonth = (range: number) => {
  return DayJS().startOf("month").subtract(range, "month").toDate();
};
const getMaxMonth = (range: number) => {
  return DayJS().endOf("month").add(range, "month").toDate();
};

export const monthStringValidator = (isOptional = false, minMonthRange?: number, maxMonthRange?: number) => {
  return stringValidator(256, isOptional).refine(
    (val) => (val ? validateMonthString(val, minMonthRange, maxMonthRange) : true),
    { message: ERROR_CODE.INVALID_OPTION },
  );
};

export const customTextMonthValidator = (isOptional?: boolean, minMonthRange?: number, maxMonthRange?: number) => {
  const validator = monthStringValidator(isOptional, minMonthRange, maxMonthRange);

  return isOptional ? validator.optional() : validator;
};

export const validateMonthString = (value: string, minMonthRange?: number, maxMonthRange?: number) => {
  const minMonth = typeof minMonthRange === "number" ? getMinMonth(minMonthRange) : DEFAULT_MIN_DATE;
  const maxMonth = typeof maxMonthRange === "number" ? getMaxMonth(maxMonthRange) : DEFAULT_MAX_DATE;

  const validator = z.date().min(minMonth).max(maxMonth);

  return validator.safeParse(new Date(value)).success;
};

export const customFileUploadValidator = (isOptional = false, maxFile = 10) => {
  const fileValidator = z.object({
    fileName: z
      .string()
      .min(1, {
        message: "File name cannot be empty.",
      })
      .max(250, {
        message: "File name exceeded 250 characters. Reduce the file name length and try again.",
      })
      .regex(/^[a-zA-Z0-9 _.=+-@]*$/, {
        message: "File name should only contain letters, numbers, spaces and the following characters: + - = . _ @.",
      }),
    attachmentType: z.string(), // not used by appgen but it's here to reflect the value inserted into form
    fileSize: z.number(), // used by draft
    // fileId not included here as it is only used for external file upload system and should not be sent to agency
  });

  const fileArrayValidator = z
    .array(fileValidator, {
      required_error: "At least 1 document is required.",
    })
    .max(maxFile, {
      message: `Reached limit of ${maxFile} files in this section. Try uploading the file in another section or reducing the number of files.`,
    });

  const validator = isOptional
    ? fileArrayValidator.optional()
    : fileArrayValidator.nonempty({
        message: "At least 1 document is required.",
      });

  return validator;
};

export const hiddenValidator = (maxLength = 36, optional = false) => {
  const validator = stringValidator(maxLength, optional).refine(
    (value) => {
      return /^[A-Za-z0-9-_.]*$/.test(value);
    },
    {
      message: ERROR_CODE.REQUIRED,
    },
  );
  return optional ? validator.optional() : validator;
};

export const customSignatureValidator = (isOptional = false) => {
  const SIGNATURE_REQUIRED_ERROR = "Signature is required.";
  const signatureValidator = z
    .object(
      {},
      {
        invalid_type_error: SIGNATURE_REQUIRED_ERROR, // error for default null value
        required_error: SIGNATURE_REQUIRED_ERROR, // error for undefined value
      },
    )
    .passthrough()
    .refine((val) => fileValidator.safeParse(val).success, {
      message: SIGNATURE_REQUIRED_ERROR,
    });
  return isOptional ? signatureValidator.nullable().optional() : signatureValidator;
};

export const customTncValidator = () => {
  const validator = checkboxValidator(false);

  return validator;
};

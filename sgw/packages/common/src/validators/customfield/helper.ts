import z from "zod";

export const CustomFieldType = z.enum([
  "TEXT",
  "FREE_TEXT",
  "TEXT_NUMBER",
  "MULTILINE_TEXT",
  "DROPDOWN",
  "RADIO",
  "CHECKBOX",
  "<PERSON>AT<PERSON>",
  "<PERSON><PERSON><PERSON>_MONTH",
  "DATE_RANGE",
  "SINGLE_CHECK",
  "MONE<PERSON>",
  "DY<PERSON>MIC_DROPDOWN",
  "FILE_UPLOAD",
  "HIDDEN",
  "SIGNATURE",
  "TNC",
]);

export type CustomField = z.infer<typeof CustomFieldType>;

export const ERROR_CODE = {
  REQUIRED: "REQUIRED",
  ONLY_NUMERIC: "ONLY_NUMERIC",
  ONLY_ALPHANUMERIC: "ONLY_ALPHANUMERIC",
  INVALID_FREE_TEXT: "INVALID_FREE_TEXT",
  EXCEED_MAX_CHARACTER: "EXCEED_MAX_CHARACTER-", // to be appended with max length e.g. EXCEED_MAX_CHARACTER-256
  EXCEED_MAX_DIGIT: "EXCEED_MAX_DIGIT-", // to be appended with max length e.g. EXCEED_MAX_DIGIT-9
  ENTER_EXACT_DIGIT: "ENTER_EXACT_DIGIT-", // to be appended with exact length e.g. ENTER_EXACT_DIGIT-6
  REQUIRED_ACKNOWLEDGEMENT: "REQUIRED_ACKNOWLEDGEMENT",
  REQUIRED_END_DATE: "REQUIRED_END_DATE",
  INVALID_OPTION: "INVALID_OPTION",
  INVALID_NRIC: "INVALID_NRIC",
  INVALID_MOBILE_NUMBER: "INVALID_MOBILE_NUMBER",
  INVALID_HOME_NUMBER: "INVALID_HOME_NUMBER",
  INVALID_EMAIL: "INVALID_EMAIL",
  INVALID_NAME: "INVALID_NAME",
  INVALID_BANK_ACCOUNT_LENGTH: "INVALID_BANK_ACCOUNT_LENGTH-", // to be appended with exact length e.g. INVALID_BANK_ACCOUNT_LENGTH-9
};

export const stringValidator = (maxLength: number, isOptional: boolean) => {
  const validator = z
    .string({
      required_error: ERROR_CODE.REQUIRED,
    })
    .trim()
    .max(maxLength, {
      message: `${ERROR_CODE.EXCEED_MAX_CHARACTER}${maxLength}`,
    });

  return isOptional ? validator : noEmptyString(validator);
};

const noEmptyString = (validator: z.ZodString) => {
  return validator.min(1, {
    message: ERROR_CODE.REQUIRED,
  });
};

type CustomOptions = (string | Record<string, string>)[];
export type PresetOptions = {
  [key: string]: string; // format is value: label in ddl
};
export type Options = CustomOptions | PresetOptions;

// used to convert array of records to object for field validation
//TODO: to move into dropdown/radio/checkbox validator once all enums are switched to Record<string, string>[]
export const recordArrToObj = (options: Record<string, string>[]): PresetOptions => {
  return options.reduce((acc, curr) => ({ ...acc, ...curr }), {});
};

// handles array based options
//TODO: to refactor when all enums are switched to Record<string, string>[]
export const generateCustomOptions = (options: CustomOptions) => {
  return options.map((option, index) => {
    if (typeof option === "string") {
      return { value: "" + index, label: option };
    } else {
      const [value, label] = Object.entries(option)[0];
      return { value, label };
    }
  });
};

/**
 * Returns an array of valid option values for selection fields (dropdown, radio, checkbox).
 * Supports array of strings, array of single-key objects, and object formats.
 * Used by validators to determine which values are allowed for a given field.
 */
export const getValidValues = (options: Options): string[] => {
  if (Array.isArray(options)) {
    return options.map((option, index) => {
      if (typeof option === "string") {
        // For array of strings, use index as value
        return String(index);
      }
      // For array of single-key objects, use the key as value
      return Object.keys(option)[0];
    });
  } else if (typeof options === "object" && options !== null) {
    // For object format, use keys as valid values
    return Object.keys(options);
  }
  return [];
};

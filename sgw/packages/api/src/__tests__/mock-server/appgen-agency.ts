import Router from "@koa/router";
import * as jose from "jose";
import { Context } from "koa";
import { setTimeout } from "node:timers/promises";

import { jwtService, logger } from "@sgw/common";

import { appConfig } from "../../config/app";
import { getQueryValueAsString } from "../../util";

const SGW_JWKS = {
  keys: [
    {
      kty: "EC",
      kid: "sgw_appgen_sig_01",
      use: "sig",
      alg: "ES256",
      crv: "P-256",
      x: "Zl7t3etCymo1A0Ek9XRC0HAxPorxvcoAgxP4v9ztPHE",
      y: "iTNvM7EMxnGR4gB_fH024jbXP1Si2febNgBEvrenxdM",
    },
    {
      kty: "EC",
      kid: "sgw_appgen_enc_01",
      use: "enc",
      alg: "ECDH-ES+A256KW",
      crv: "P-256",
      x: "WGaikAqhkUKAAzAee7iNCKjRiMDCYU0gQe_2B-ZGx0E",
      y: "rM0oRXORsgdXQn3w_HWL29ABCwuLhwNN609CjGbbN5w",
    },
  ],
};

export const AGENCY_JWKS = {
  keys: [
    {
      kty: "EC",
      kid: "agency_appgen_sig_01",
      use: "sig",
      alg: "ES256",
      crv: "P-256",
      x: "bo6n6DCyQE3fEVwX9Y3E_4HBVoV4lWoD0Ow7tbrt02o",
      y: "Tu1yrjCynfb99EGTEJzYJK0M8FaeCmiphjkTYBoPLRc",
    },
    {
      kty: "EC",
      kid: "agency_appgen_enc_01",
      use: "enc",
      alg: "ECDH-ES+A256KW",
      crv: "P-256",
      x: "bsDSqEE6rAR2a1zsBXRnt4j1LFZv8c7EXFiQCMk65Z8",
      y: "b9L9kMF98rtDMyNtdELNd0yyCFCmTghgflF3AZqHtNg",
    },
  ],
};

const postSubmission = async (ctx: Context) => {
  const scheme = ctx.params.scheme;
  logger.debug(`received ${scheme} submission request`);
  const decryptedRequest = await decryptRequest(ctx.request.body.data, scheme);
  if (!decryptedRequest) {
    ctx.throw(500);
  }

  logger.debug(`successfully decrypted ${scheme} submission request`);
  ctx.status = 200;
};

const getApplication = async (ctx: Context) => {
  const scheme = ctx.params.scheme;
  let encryptedResponse = "";

  const encryptedRequest = getQueryValueAsString(ctx.query, "query");
  let nric = "";
  const decryptedNric = await decryptRequest(encryptedRequest, scheme);
  if (decryptedNric) {
    nric = JSON.parse(decryptedNric).nric;
    logger.debug(`decrypted nric ${nric}`);
  }

  // test when agency throws error
  if (nric === "*********") {
    ctx.throw(500);
  }

  if (scheme === "mda") {
    // eligible and non-terminal status
    if (nric === "*********") {
      const agencyStatus = [
        {
          refId: "MDA-1OP53CSR6G",
          status: "1",
          appliedDateTime: "2023-01-17T03:24:00.123Z",
          updatedDateTime: "2023-03-01T03:24:00.000Z",
          detail: [
            {
              title: "Sharon Rebecca Tay Kaling",
              category: "2",
              status: "5",
              remarks:
                "Ref no.: ABC-1MC3C25C11\nSCC: YMCA Student Care Centre @ St Anthony’s - St Anthony's Primary School",
            },
          ],
        },
      ];

      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: agencyStatus,
            eligible: true,
            systemData: {
              maintenanceStartDate: "2024-10-18T01:24:00.123Z",
              maintenanceEndDate: "2024-10-18T09:24:00.123Z",
            },
          }),
          scheme,
        )) || "";
    }
    // not eligible and terminal status
    else if (nric === "*********") {
      const agencyStatus = [
        {
          refId: "MDA-1ONDSK5Q2Y",
          status: "3",
          appliedDateTime: "2023-01-17T03:24:00.123Z",
          updatedDateTime: "2023-03-01T03:24:00.000Z",
          detail: [
            {
              title: "Sharon Rebecca Tay Kaling",
              category: "2",
              status: "5",
              remarks:
                "Ref no.: ABC-1MC3C25C11\nSCC: YMCA Student Care Centre @ St Anthony’s - St Anthony's Primary School",
            },
          ],
        },
      ];
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: agencyStatus,
            eligible: false,
            systemData: {
              maintenanceStartDate: "2024-10-18T01:24:00.123Z",
              maintenanceEndDate: "2024-10-19T05:24:00.123Z",
            },
          }),
          scheme,
        )) || "";
    }
    // eligible
    else if (nric === "*********") {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: true,
            systemData: { maintenanceStartDate: "2024-10-18T01:24:00.123Z" },
          }),
          scheme,
        )) || "";
    }
    // not eligible
    else if (nric === "*********") {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: false,
          }),
          scheme,
        )) || "";
    }
    // many statuses
    else if (nric === "*********") {
      const agencyStatus = [
        {
          refId: "MDA-1NFHXFKU7R",
          status: "2",
          appliedDateTime: "2023-01-17T03:24:00.123Z",
          updatedDateTime: "2023-03-01T03:24:00.000Z",
          remarks: "Beneficiaries registered for MDA:\nSharon Rebecca Tay\nKenneth Benjamin Tay Kai Kang",
          detail: [
            {
              title: "Sharon Rebecca Tay Kaling",
              category: "2",
              status: "5",
              remarks:
                "Ref no.: ABC-1MC3C25C11\nSCC: YMCA Student Care Centre @ St Anthony’s - St Anthony's Primary School",
            },
            {
              title: "Kenneth Benjamin Tay Kai Kang",
              category: "2",
              status: "1",
              description: "Monthly grant: $200\nCo-payment: $200\nStart-up grant: $100",
              period: "Feb 2023 - Jun 2023",
              remarks:
                "Ref no.: ABC-1MC3C25C91\nSCC: YMCA Student Care Centre @ St Anthony’s - St Anthony's Primary School\nRefer to your SCFA SMS/email for more details.",
            },
            {
              title: "Jayden Aloysius Tay Kun Xing",
              assistance: "$9999.99",
              category: "2",
              status: "4",
              description: "Monthly grant: $200\nCo-payment: $200\nStart-up grant: $100",
              period: "Feb 2023 - Jun 2023",
              paymentMode: "GIRO",
              disbursement:
                "Upon application for assistance, this assistance will be provided to you through the school. Please check with them on the Grant Start Month and benefits provided.",
              remarks:
                "Ref no.: ABC-1MC3C25Z20\nSCC: YMCA Student Care Centre @ St Anthony’s - St Anthony's Primary School\nRefer to your SCFA SMS/email for more details.",
            },
          ],
        },
        {
          refId: "MDA-1NGQARR3UL",
          status: "6",
          appliedDateTime: "2022-01-17T03:24:00.000Z",
          updatedDateTime: "2022-03-22T03:24:00.000Z",
        },
        {
          refId: "MDA-1NG5OV8WYD",
          status: "4",
          appliedDateTime: "2022-04-17T03:24:00.000Z",
          updatedDateTime: "2022-12-18T03:24:00.000Z",
        },
      ];
      encryptedResponse = (await encryptResponse(JSON.stringify({ application: agencyStatus }), scheme)) || "";
    } else if (nric === "*********") {
      // multiple ongoing application
      throw new Error();
    }
  } else if (scheme === "clpp1") {
    if (nric === "*********") {
      // valid ineligible reason code
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: false,
            ineligibleReason: "NO_CDA",
          }),
          scheme,
        )) || "";
    } else if (nric === "*********") {
      // invalid ineligible reason code
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: false,
            ineligibleReason: "INVALID",
          }),
          scheme,
        )) || "";
    } else {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: true,
            userData: {
              child: [
                {
                  clppUuid: "81c4bc65-a3dc-4f7e-b44e-9c099e50383d",
                  name: "Child 1 Name",
                  id: "*********",
                  residentialStatus: "C",
                  dob: "2020-04-01",
                  paymentMode: "0",
                },
                {
                  clppUuid: "ea7c5149-10bc-46b3-919b-7a2a07da9565",
                  name: "Child 2 Name (Example)",
                  id: "*********",
                  residentialStatus: "C",
                  dob: "2021-01-22",
                  paymentMode: "1",
                  bankDetails: {
                    bankCode: "7339", // OCBC
                    bankBranch: "501",
                    bankAccountNumber: "1234567",
                    isJointAllAccount: "1",
                  },
                  trusteeDetails: {
                    name: "Trustee Name",
                    nric: "*********",
                    relationship: "Mother",
                  },
                  // ownself's mock cda jwt - should be sufficient for testing at local
                  cdaJwt:
                    "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************.56DnmvWBtEs9fyiGfecxV1O3vMScortGBeFw7QXhzfoOO3lF_Q9Jmtng635lALGr963E0bRi3gtDSa76TxTx3g",
                },
                {
                  clppUuid: "cf159ecf-97df-4ccd-9b2a-85539c16d180",
                  name: "Child 3 Name",
                  id: "*********",
                  residentialStatus: "C",
                  dob: "2019-04-01",
                  paymentMode: "0",
                },
              ],
            },
          }),
          scheme,
        )) || "";
    }
  } else if (scheme === "clpp2" || scheme === "clpp4") {
    encryptedResponse =
      (await encryptResponse(
        JSON.stringify({
          application: [],
          eligible: true,
        }),
        scheme,
      )) || "";
  } else if (scheme === "clpp3") {
    if (nric === "*********") {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: false,
            userData: {
              clppUuid: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
            },
          }),
          scheme,
        )) || "";
    } else if (nric === "*********") {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [
              {
                refId: "CLPP3-1MC3C25M41",
                status: "2",
                appliedDateTime: "2025-01-17T03:24:00.123Z",
                updatedDateTime: "2025-03-01T03:24:00.000Z",
              },
            ],
            eligible: true,
            userData: {
              clppUuid: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
            },
          }),
          scheme,
        )) || "";
    } else if (nric === "*********") {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [
              {
                refId: "CLPP3-1MC3C25M41",
                status: "8",
                appliedDateTime: "2025-09-17T03:24:00.123Z",
                updatedDateTime: "2025-10-01T03:24:00.000Z",
                detail: [
                  {
                    title: "Singtel",
                    category: "0",
                    status: "11",
                    paymentMode: "Refund by CCS from creditors",
                    assistance: "$200",
                    remarks: "Date of refund: 15 Aug 2025",
                  },
                  {
                    title: "Singtel",
                    category: "0",
                    status: "4",
                    paymentMode: "Payment by CCS to creditors. ",
                    assistance: "$200",
                    remarks: "Your payment date to creditor: 2 Jul 2025\nCCS' payment date to creditor: 15 Aug 2025",
                  },
                ],
              },
            ],
            eligible: true,
            userData: {
              clppUuid: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
            },
          }),
          scheme,
        )) || "";
    } else if (nric === "*********") {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [
              {
                refId: "CLPP3-1MC3C25M41",
                status: "7",
                appliedDateTime: "2025-01-17T03:24:00.123Z",
                updatedDateTime: "2025-03-01T03:24:00.000Z",
              },
            ],
            eligible: true,
            userData: {
              clppUuid: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
            },
          }),
          scheme,
        )) || "";
    } else {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: true,
            userData: {
              clppUuid: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
            },
          }),
          scheme,
        )) || "";
    }
  } else if (scheme === "smta") {
    if (nric === "*********") {
      // valid data 1
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: true,
            userData: {
              highestEducation: "1",
              bankOwnerType: "0",
              bankDetails: {
                bankCode: "7171",
                bankBranch: "001",
                bankAccountNumber: "**********",
                isJointAllAccount: "0",
                jointAllName: "Joint name",
                jointAllNric: "*********",
              },
              contactType: "0",
              otherContact: "1",
              employmentStatus: "ES1",
              jobDetails: [
                {
                  employmentType: "0",
                  companyName: "Macdonalds",
                  occupation: "5",
                },
                {
                  employmentType: "1",
                  companyName: "Watson Pte Ltd",
                  occupation: "4",
                },
                {
                  employmentType: "1",
                  companyName: "Burger King",
                  occupation: "5",
                },
              ],
              grossMonthlyIncome: "1000.00",
              cohabitationStatus: "0",
              familyMemberDetails: [
                {
                  name: "Jackie Han",
                  nric: "*********",
                  email: "<EMAIL>",
                  employmentStatus: "ES1",
                  grossMonthlyIncome: "2000.00",
                  relationshipType: "RT6",
                },
                {
                  name: "Mackie Han",
                  nric: "*********",
                  email: "",
                  employmentStatus: "ES3",
                  situationType: ["UR4", "UR6"],
                  othersReason: "Internship",
                  relationshipType: "RT2",
                },
              ],
            },
          }),
          scheme,
        )) || "";
    } else if (nric === "*********") {
      // valid data 2
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: true,
            userData: {
              highestEducation: "0",
              bankOwnerType: "2",
              bankOwnerName: "Mr O",
              bankOwnerNric: "*********",
              bankOwnerMobile: "********",
              bankOwnerRelationshipType: "RT32",
              bankDetails: {
                bankCode: "7171",
                bankBranch: "001",
                bankAccountNumber: "**********",
                isJointAllAccount: "1",
              },
              contactType: "1",
              proxyName: "Proxy",
              proxyMobileNumber: "********",
              proxyHomeNumber: "",
              proxyEmail: "",
              proxyRelationshipType: "RT6",
              otherContact: "0",
              otherName: "Mr 0",
              otherMobileNumber: "********",
              otherHomeNumber: "********",
              otherEmail: "",
              otherRelationshipType: "RT32",
              employmentStatus: "ES3",
              situationType: ["UR1", "UR6"],
              othersReason: "Doing freelance work",
              cohabitationStatus: "1",
            },
          }),
          scheme,
        )) || "";
    } else if (nric === "*********") {
      // valid data with empty bank details and employment status
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: true,
            userData: {
              highestEducation: "1",
              bankOwnerType: "0",
              bankDetails: {
                bankCode: "",
                bankBranch: "",
                bankAccountNumber: "",
                isJointAllAccount: "",
                jointAllName: "",
                jointAllNric: "",
              },
              contactType: "0",
              otherContact: "1",
              employmentStatus: "",
              cohabitationStatus: "0",
              familyMemberDetails: [
                {
                  name: "Jackie Han",
                  nric: "*********",
                  email: "<EMAIL>",
                  employmentStatus: "",
                  grossMonthlyIncome: "",
                  relationshipType: "RT6",
                },
                {
                  name: "Mackie Han",
                  nric: "*********",
                  email: "<EMAIL>",
                  employmentStatus: "ES3",
                  situationType: ["UR4", "UR6"],
                  othersReason: "Internship",
                  relationshipType: "RT2",
                },
              ],
            },
          }),
          scheme,
        )) || "";
    } else if (nric === "*********") {
      // invalid data
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: true,
            userData: {
              highestEducation: "invalid",
              bankOwnerType: "2",
              bankOwnerName: "Mr O",
              bankOwnerNric: "*********",
              bankOwnerMobile: "********",
              bankOwnerRelationshipType: "invalid",
              bankDetails: {
                bankCode: "7171",
                bankBranch: "001",
                bankAccountNumber: "**********",
                isJointAllAccount: "invalid",
              },
              contactType: "0",
              otherContact: "1",
              employmentStatus: "ES2",
              cohabitationStatus: "1",
            },
          }),
          scheme,
        )) || "";
    } else if (nric === "*********") {
      // additional valid data for saf smta
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: true,
            userData: {
              highestEducation: "1",
              bankOwnerType: "0",
              bankDetails: {
                bankCode: "7171",
                bankBranch: "001",
                bankAccountNumber: "**********",
                isJointAllAccount: "0",
                jointAllName: "Joint name",
                jointAllNric: "*********",
              },
              homeNumber: "********",
              cohabitationStatus: "0",
              familyMemberDetails: [
                {
                  name: "Jackie Han",
                  nric: "*********",
                  email: "<EMAIL>",
                  relationshipType: "REL402",
                },
                {
                  name: "Mackie Han",
                  nric: "*********",
                  email: "",
                },
              ],
            },
          }),
          scheme,
        )) || "";
    } else if (nric === "*********") {
      // multiple ongoing application
      const agencyStatus = [
        {
          refId: "SMTA-1PFXRXQ111",
          status: "20",
          appliedDateTime: "2024-12-12T02:50:00.708Z",
          updatedDateTime: "2024-12-12T02:50:00.708Z",
          outstandingItems: {
            id: "hhMemberconsent",
            deadline: "2024-12-31T02:48:00.708Z",
          },
        },
        {
          refId: "SMTA-1PFXRXQ8AA",
          status: "2",
          appliedDateTime: "2024-12-08T02:48:50.708Z",
          updatedDateTime: "2024-12-08T02:48:50.708Z",
        },
        {
          refId: "SMTA-1PFXRXQ80L",
          status: "3",
          appliedDateTime: "2024-12-05T02:48:10.708Z",
          updatedDateTime: "2024-12-05T02:48:10.708Z",
          detail: [
            {
              title: "Medical fees at Public Healthcare Institutions",
              category: "3",
              status: "4",
              description: "This assistance is provided to you through your Public Healthcare Institution(s).",
              remarks: "Please check with them on the amount and payment mode.",
            },
          ],
        },
        {
          refId: "SMTA-1PFXRX881",
          status: "5",
          appliedDateTime: "2024-12-01T02:50:00.708Z",
          updatedDateTime: "2024-12-01T02:50:00.708Z",
          remarks: "Your application was unsuccessful. We will mail you a letter with the reasons for rejection.",
        },
        {
          refId: "SMTA-1PFXRXQ8XX",
          status: "3",
          appliedDateTime: "2024-11-05T02:48:10.708Z",
          updatedDateTime: "2024-11-05T02:48:10.708Z",
          detail: [
            {
              title: "Cash",
              category: "0",
              assistance: "$300",
              description: "$300 for December will be credited into DBS by 2024-12-15.",
              paymentMode: "Bank deposit.",
            },
          ],
        },
        {
          refId: "SMTA-1PFXRXQ999",
          status: "20",
          appliedDateTime: "2024-08-06T02:50:00.708Z",
          updatedDateTime: "2024-08-06T02:50:00.708Z",
          outstandingItems: {
            id: "hhMemberconsent",
            deadline: "2024-08-30T02:48:00.708Z",
          },
        },
        {
          refId: "SMTA-1PFXRXQ123",
          status: "2",
          appliedDateTime: "2024-07-07T02:48:50.708Z",
          updatedDateTime: "2024-07-07T02:48:50.708Z",
        },
        {
          refId: "SMTA-1PFXRXQ222",
          status: "20",
          appliedDateTime: "2024-04-06T02:50:00.708Z",
          updatedDateTime: "2024-04-06T02:50:00.708Z",
          outstandingItems: {
            id: "hhMemberconsent",
            deadline: "2024-05-01T02:48:00.708Z",
          },
        },
        {
          refId: "SMTA-1PFXRXQ333",
          status: "5",
          appliedDateTime: "2024-02-06T02:50:00.708Z",
          updatedDateTime: "2024-02-06T02:50:00.708Z",
          remarks: "Your application was unsuccessful. We will mail you a letter with the reasons for rejection.",
        },
      ];

      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: agencyStatus,
            eligible: true,
            userData: {
              highestEducation: "1",
              bankOwnerType: "0",
              bankDetails: {
                bankCode: "7171",
                bankBranch: "001",
                bankAccountNumber: "**********",
                isJointAllAccount: "0",
                jointAllName: "Joint name",
                jointAllNric: "*********",
              },
              contactType: "0",
              otherContact: "1",
              employmentStatus: "ES1",
              jobDetails: [
                {
                  employmentType: "0",
                  companyName: "Macdonalds",
                  occupation: "5",
                },
                {
                  employmentType: "1",
                  companyName: "Watson Pte Ltd",
                  occupation: "4",
                },
                {
                  employmentType: "1",
                  companyName: "Burger King",
                  occupation: "5",
                },
              ],
              grossMonthlyIncome: "1000.00",
              cohabitationStatus: "0",
              familyMemberDetails: [
                {
                  name: "Jackie Han",
                  nric: "*********",
                  email: "<EMAIL>",
                  employmentStatus: "ES1",
                  grossMonthlyIncome: "2000.00",
                  relationshipType: "RT6",
                },
                {
                  name: "Mackie Han",
                  nric: "*********",
                  email: "",
                  employmentStatus: "ES3",
                  situationType: ["UR4", "UR6"],
                  othersReason: "Internship",
                  relationshipType: "RT2",
                },
              ],
            },
          }),
          scheme,
        )) || "";
    } else if (nric === "*********") {
      // invalid systemData and one invalid application record missing outstanding items
      const agencyStatus = [
        {
          refId: "SMTA-V7RX2Y8TBN",
          status: "2",
          appliedDateTime: "2024-12-03T03:48:50.708Z",
          updatedDateTime: "2024-12-03T03:48:50.708Z",
        },
        {
          refId: "SMTA-8KJ4PZ1QW9",
          status: "20",
          appliedDateTime: "2024-12-12T02:50:00.708Z",
          updatedDateTime: "2024-12-12T02:50:00.708Z",
        },
        {
          refId: "SMTA-3MZD6H5LKP",
          status: "3",
          appliedDateTime: "2024-12-04T03:48:10.708Z",
          updatedDateTime: "2024-12-04T03:48:10.708Z",
          detail: [
            {
              title: "Medical fees at Public Healthcare Institutions",
              category: "3",
              status: "4",
              description: "This assistance is provided to you through your Public Healthcare Institution(s).",
              remarks: "Please check with them on the amount and payment mode.",
            },
          ],
        },
      ];

      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: agencyStatus,
            eligible: true,
            systemData: {
              maintenanceStartDate: "invalid",
            },
            userData: {
              highestEducation: "0",
              bankOwnerType: "2",
              bankOwnerName: "Mr O",
              bankOwnerNric: "*********",
              bankOwnerMobile: "********",
              bankOwnerRelationshipType: "RT32",
              bankDetails: {
                bankCode: "7171",
                bankBranch: "001",
                bankAccountNumber: "**********",
                isJointAllAccount: "1",
              },
              contactType: "1",
              proxyName: "Proxy",
              proxyMobileNumber: "********",
              proxyHomeNumber: "",
              proxyEmail: "",
              proxyRelationshipType: "RT6",
              otherContact: "0",
              otherName: "Mr 0",
              otherMobileNumber: "********",
              otherHomeNumber: "********",
              otherEmail: "",
              otherRelationshipType: "RT32",
              employmentStatus: "ES3",
              situationType: ["UR1", "UR6"],
              othersReason: "Doing freelance work",
              cohabitationStatus: "1",
            },
          }),
          scheme,
        )) || "";
    }
  } else if (scheme === "ksa") {
    // pending outstanding doc
    if (nric === "*********" || nric === "*********" || nric === "*********" || nric === "*********") {
      const agencyStatus = [
        {
          refId: "KSA-1ONDSK5Q2Y",
          status: "20",
          appliedDateTime: "2023-01-17T03:24:00.123Z",
          updatedDateTime: "2023-03-01T03:24:00.000Z",
          outstandingItems: {
            id: "SD4,SD6",
            deadline: "31 Jul 2024",
          },
        },
      ];
      encryptedResponse =
        (await encryptResponse(JSON.stringify({ application: agencyStatus, eligible: false }), scheme)) || "";
    }
  } else if (scheme === "scfa") {
    if (nric === "*********") {
      // multiple ongoing application
      const agencyStatus = [
        {
          refId: "SCFA-1PHLHFE9KK",
          status: "20",
          appliedDateTime: "2024-12-21T02:48:10.708Z",
          updatedDateTime: "2024-12-21T02:48:10.708Z",
          outstandingItems: {
            id: "SD23",
            deadline: "2024-12-31T02:48:00.708Z",
          },
          appliedForNames: ["Jamus Lim Ah Gok", "Alice Chan Wai Jin", "Mabel Tan Yongqing"],
        },
        {
          refId: "SCFA-1PFXRXQ80L",
          status: "3",
          appliedDateTime: "2024-12-12T02:48:10.708Z",
          updatedDateTime: "2024-12-12T02:48:10.708Z",
          remarks: "Beneficiaries registered for SCFA:\nSharon Rebecca Tay\nKenneth Benjamin Tay Kai Kang",
          detail: [
            {
              title: "Sharon Rebecca Tay Kaling",
              category: "2",
              status: "3",
              description: "Raffles SCC LLP Fernvale",
              remarks:
                "Ref no.: ABC-1MC3C25C11\nSCC: YMCA Student Care Centre @ St Anthony’s - St Anthony's Primary School",
            },
          ],
        },
        {
          refId: "SCFA-1PFXRXO000",
          status: "5",
          appliedDateTime: "2024-12-08T02:48:10.708Z",
          updatedDateTime: "2024-12-08T02:48:10.708Z",
          remarks: "Your application was unsuccessful. We will mail you a letter with the reasons for rejection.",
        },
        {
          refId: "SCFA-1PFXRXQ111",
          status: "20",
          appliedDateTime: "2024-12-04T02:48:00.708Z",
          updatedDateTime: "2024-12-04T02:48:00.708Z",
          outstandingItems: {
            id: "SD23",
            deadline: "2024-12-31T02:48:00.708Z",
          },
        },
        {
          refId: "SCFA-1PFXRXQ8AA",
          status: "2",
          appliedDateTime: "2024-12-05T02:48:50.708Z",
          updatedDateTime: "2024-12-05T02:48:50.708Z",
        },
        {
          refId: "SCFA-1PFXRXQ8XX",
          status: "3",
          appliedDateTime: "2024-11-05T02:48:10.708Z",
          updatedDateTime: "2024-11-05T02:48:10.708Z",
          remarks: "Beneficiaries registered for SCFA:\nSharon Rebecca Tay\nKenneth Benjamin Tay Kai Kang",
          detail: [
            {
              title: "Sharon Rebecca Tay Kaling",
              category: "2",
              status: "3",
              description: "Raffles SCC LLP Fernvale",
              remarks:
                "Ref no.: ABC-1MC3C25C11\nSCC: YMCA Student Care Centre @ St Anthony’s - St Anthony's Primary School",
            },
          ],
        },
        {
          refId: "SCFA-1PFXRXW777",
          status: "5",
          appliedDateTime: "2024-08-22T02:48:10.708Z",
          updatedDateTime: "2024-08-22T02:48:10.708Z",
          remarks: "Your application was unsuccessful. We will mail you a letter with the reasons for rejection.",
          detail: [
            {
              title: "Sharon Rebecca Tay Kaling",
              category: "2",
              status: "5",
              description: "Raffles SCC LLP Fernvale",
              remarks:
                "Ref no.: ABC-1MC3C25C11\nSCC: YMCA Student Care Centre @ St Anthony’s - St Anthony's Primary School",
            },
          ],
        },
        {
          refId: "SCFA-1PFXRXH567",
          status: "20",
          appliedDateTime: "2024-05-27T02:48:00.708Z",
          updatedDateTime: "2024-05-27T02:48:00.708Z",
          outstandingItems: {
            id: "SD23",
            deadline: "2024-06-31T02:48:00.708Z",
          },
          appliedForNames: ["Jeremy Tan", "Liu Ling Ling"],
        },
      ];

      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: agencyStatus,
            eligible: true,
          }),
          scheme,
        )) || "";
      // newly submitted application
    } else if (nric === "*********") {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [
              {
                refId: `${scheme.toUpperCase()}-5PHLHGE9QL`,
                status: "0",
                appliedDateTime: "2024-12-21T02:48:10.708Z",
                updatedDateTime: "2024-12-21T02:48:10.708Z",
              },
            ],
            eligible: true,
          }),
          scheme,
        )) || "";
    }
  } else if (["pwdr", "emps", "h2w", "ihl"].includes(scheme)) {
    // profile for user who has previous application
    if (nric === "*********") {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [
              {
                refId: `${scheme.toUpperCase()}-5PHLHGE9QL`,
                status: "5",
                appliedDateTime: "2024-12-21T02:48:10.708Z",
                updatedDateTime: "2024-12-21T02:48:10.708Z",
              },
            ],
            eligible: true,
          }),
          scheme,
        )) || "";
      // profile for user who has recently submitted application
    } else if (nric === "*********") {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [
              {
                refId: `${scheme.toUpperCase()}-5PHLHGE9QL`,
                status: "0",
                appliedDateTime: "2024-12-21T02:48:10.708Z",
                updatedDateTime: "2024-12-21T02:48:10.708Z",
              },
            ],
            eligible: true,
          }),
          scheme,
        )) || "";
    } else {
      encryptedResponse =
        (await encryptResponse(
          JSON.stringify({
            application: [],
            eligible: true,
          }),
          scheme,
        )) || "";
    }
  }

  if (!encryptedResponse) {
    encryptedResponse = (await encryptResponse(JSON.stringify({ application: [] }), scheme)) || "";
  }
  ctx.body = { data: encryptedResponse };
};

const getDropdownOptions = async (ctx: Context) => {
  logger.debug("received get dropdown options request");

  const { scheme } = ctx.params;
  let data;
  if (scheme === "scfa") {
    const { query } = ctx.request.query;

    if (query === "school") {
      data = {
        sch0: "Commit Learning Schoolhouse @ Fairfield Methodist School (Primary)",
        sch1: "Commit Learning SchoolHouse @ Methodist Girls'' School",
        sch2: "Commit Learning SchoolHouse @ Paya Lebar Methodist Girls'' School (Primary)",
        sch3: "Nascans Lambda Pte Ltd",
      };
    } else if (query === "community") {
      data = {
        comm0: "Ascenda Educare Centre (Tampines) (272 Tampines Street 21)",
        comm1: "Children Primaryland Pte Ltd (497H Tampines Street 45)",
        comm2: "Kampung Senang Student Care Centre (840 Tampines Street 82)",
        comm3: "Kidz Inc Educare Learning Centre Pte Ltd (433 Tampines Street 43)",
        comm4: "After School Adventure Club (481 Tampines Street 44)",
        comm5: "Pro-Teach Before & After School Care (Tampines) (264 Tampines Street 21)",
        comm6: "Pap Community Foundation Tampines East 3-In-1 Family Centre (Student Care) (203 Tampines Street 21)",
      };
    }
  } else {
    data = { code1: "Option 1", code2: "Option 2" };
  }

  ctx.body = { data: await encryptResponse(JSON.stringify(data), scheme) };
  ctx.status = 200;
};

const getJwks = async (ctx: Context) => {
  logger.debug("received get jwks request");
  ctx.body = AGENCY_JWKS;
};

const decryptRequest = async (jwt: string, scheme: string) => {
  try {
    const agencyPrivateKey = await jose.importPKCS8(appConfig.mock.MOCK_AGENCY_ENC_PRIVATE_KEY, "ES256");
    const jws = await jwtService.decryptJwe(jwt, agencyPrivateKey, "sgw", scheme);
    if (!jws) {
      throw new Error("jwt no sub");
    }
    return await jwtService.verifyJws(jws, SGW_JWKS, "sgw", scheme);
  } catch (err) {
    logger.error("failed to decrypt: ", err);
  }

  return undefined;
};

const encryptResponse = async (content: string, scheme: string) => {
  try {
    const agencyPrivateKey = await jose.importPKCS8(appConfig.mock.MOCK_AGENCY_SIG_PRIVATE_KEY, "ES256");
    const jws = await jwtService.createJws(
      content,
      agencyPrivateKey,
      appConfig.mock.MOCK_AGENCY_SIG_KID,
      scheme,
      "sgw",
    );

    const encKey = SGW_JWKS.keys.find((key) => key.use === "enc");
    if (!encKey) {
      throw new Error("no enc key found in jwks");
    }
    const jwk = await jose.importJWK(encKey, "ES256");
    return await jwtService.createJwe(jws, jwk, scheme, "sgw", appConfig.mock.MOCK_AGENCY_SIG_KID);
  } catch (err) {
    logger.error("failed to encrypt: ", err);
  }

  return undefined;
};

const getAgencyToken = async (ctx: Context) => {
  ctx.body = {
    access_token:
      "00D850000015EPx!AQEAQETP3JOIL4xrez0MKg8nO4nHVECPci9OQ012nkuLjJB1G3IQ5VOC_EtlcAFdCIvCFQ0TDqXh_5yMtaJjM8Z0uAa_U9wd",
    scope: "sfap_api api full",
    instance_url: "https://casecentral--devorg.sandbox.my.salesforce.com",
    id: "https://test.salesforce.com/id/00D850000015EPxEAM/00585000002M1sRAAS",
    token_type: "Bearer",
    api_instance_url: "https://api.salesforce.com",
  };
  ctx.status = 200;
};

const agencyCheckEligibility = async (ctx: Context) => {
  const scheme = ctx.params.scheme;
  let encryptedResponse = "";

  const decryptedRequest = await decryptRequest(ctx.request.body.data, scheme);
  if (!decryptedRequest) {
    ctx.throw(500);
  }
  const subjectNric = JSON.parse(decryptedRequest).criteria.nric;

  if (subjectNric === "*********") {
    encryptedResponse =
      (await encryptResponse(
        JSON.stringify({
          eligible: false,
          checkedDateTime: new Date(),
        }),
        scheme,
      )) || "";
  }

  if (subjectNric === "*********") {
    encryptedResponse =
      (await encryptResponse(
        JSON.stringify({
          eligible: false,
          checkedDateTime: new Date(),
        }),
        scheme,
      )) || "";
  }

  if (subjectNric === "*********") {
    // simulate agency eligibility throwing error
    ctx.throw(500);
  }

  if (subjectNric === "*********") {
    // simulate agency eligibility check taking > 5s (timeout)
    await setTimeout(10000);
    encryptedResponse =
      (await encryptResponse(
        JSON.stringify({
          eligible: false,
          checkedDateTime: new Date(),
        }),
        scheme,
      )) || "";
  }

  if (!encryptedResponse) {
    encryptedResponse =
      (await encryptResponse(JSON.stringify({ eligible: true, checkedDateTime: new Date() }), scheme)) || "";
  }
  ctx.body = { data: encryptedResponse };
  ctx.status = 200;
};

const appgenAgencyRoute = new Router();
appgenAgencyRoute.post("/agency-submission/:scheme*", postSubmission);
appgenAgencyRoute.get("/agency-status/:scheme*", getApplication);
appgenAgencyRoute.get("/agency-dropdown-options/:scheme*", getDropdownOptions);
appgenAgencyRoute.get("/agency-jwks", getJwks);
appgenAgencyRoute.post("/agency-token", getAgencyToken);
appgenAgencyRoute.post("/agency-check-eligibility/:scheme*", agencyCheckEligibility);

export const appgenAgencyRouter = new Router();
appgenAgencyRouter.use(appgenAgencyRoute.routes(), appgenAgencyRoute.allowedMethods());

import Koa from "koa";
import { ApplicationSchema, logger } from "@sgw/common";
import { Application, applicationDao, Consent, schemeDao, sgwService, User } from "@sgw/services";

import { checkPwdEligibility, updateSubmittedSchemeCount } from "../sgw/service";
import { generateRefID, redactEmail } from "../../util";

/// Retrieves and compiles user, application, and scheme-specific data.
// Can be extended to support additional data requirements defined by the scheme.
export const fetchUserData = async (ctx: Koa.Context, schemeCode: string) => {
  const { myInfoCommon } = ctx.session;
  const name = myInfoCommon?.name?.value;
  const uinfin = myInfoCommon?.uinfin?.value || "";

  let applications;
  let myInfoData;
  let applyInfo;

  try {
    // Mocked response for demonstration
    if (schemeCode) {
      const scheme = await schemeDao.getActiveScheme(schemeCode);

      applications = [{ status: "pending", refId: "ABC-12345" }];
      myInfoData = { nric: uinfin, name };

      // Applicant eligibility
      applyInfo.isApplicantEligible = await checkPwdEligibility(uinfin, uinfin, scheme);
    }
    logger.info(`${schemeCode}: user data compiled`, { applications, myInfoData });

    return {
      applications,
      prefills: { myInfo: myInfoData },
      applyInfo,
    };
  } catch (error) {
    logger.error(`${schemeCode}: failed to compile user data`, error);
    return null;
  }
};

interface UserData {
  nric: string;
  userId: string;
}

interface SubmissionResponse {
  email: string;
  refId: string;
  nextSteps: string;
  name: string;
}

export const processSubmission = async (
  schemeCode: string,
  { nric, userId }: UserData,
  appSchema: ApplicationSchema,
  submissionData,
): Promise<SubmissionResponse> => {
  // TODO: Process MSF refund fields. Push to hashed file names?

  // TODO: Process MSF Letter of Undertaking fields. Push to hashed file names and email attachments?

  // TODO: Get consenter details from submission data?

  // Generate reference ID
  const refId = generateRefID(schemeCode.toUpperCase());
  logger.info(`generated ref ID for submission ${refId}`);

  // Process SGW application
  const email = await processSgwApplication(appSchema, { nric, userId }, refId, submissionData);

  // Return value to acknowledgement page
  return {
    email: email ? redactEmail(email) : "",
    refId,
    nextSteps: appSchema.nextSteps,
    name: appSchema.schemeName,
  };
};

export const processSgwApplication = async (
  schema: ApplicationSchema,
  { nric, userId }: UserData,
  refId: string,
  submissionData,
) => {
  const submittedDate = new Date();
  const schemeCode = schema.schemeCode;

  const scheme = await schemeDao.getScheme(schemeCode);
  if (!scheme) {
    throw new Error(`no scheme found at db for ${schemeCode}`);
  }

  logger.info("uploading application");
  await sgwService.uploadApplication(refId, nric, submittedDate, submissionData, scheme);

  // TODO Sevin: Sending attachments hashed filenames to queue
  // if (hashedFileNames.length > 0) {
  //     logger.info("sending attachment hashed filenames to queue");
  // await sendAttachmentMessage(schemeCode, refId, userId, "application", hashedFileNames, scheme.appSubmissionUrl);
  //   }

  const application: Application = {
    refId,
    userUuid: userId,
    schemeCode,
    appliedDateTime: submittedDate,
    updatedDateTime: submittedDate,
    statusCode: "0",
  };

  const consents: Consent[] = [];
  // TODO: encrypt and map consent details data?

  logger.info("saving application record to database", application);

  // TODO: Get applicant's name and email from submission data
  const name = "Mr SG Father with only normal children";
  const email = "<EMAIL>";
  const user: User = { uuid: userId, name, email };
  await applicationDao.saveSgwApplication(application, user, consents);

  // TODO: Delete draft?

  await updateSubmittedSchemeCount(schemeCode);

  // TODO: Send success email

  return email;
};

import Koa from "koa";

import { logger } from "@sgw/common";

import { redactNric, extractPropertiesByPrefix } from "../../util";
import { retrieveSchema, getSqServiceIdFromSchemeCode } from "../sgw/service";
import { processSubmission, fetchUserData } from "./service";

// Initializes the session and sets the response or error details
export const getUserData = async (ctx: Koa.Context) => {
  const { schemeCode } = ctx.params;
  logger.info(`${schemeCode}: begin user data retrieval`);

  const sqServiceId = await getSqServiceIdFromSchemeCode(schemeCode);
  if (!sqServiceId) {
    logger.error(`${schemeCode}: sequential service id not found, scheme code not supported`);
    ctx.throw(400);
  }

  try {
    const data = await fetchUserData(ctx, schemeCode);

    if (!data) {
      throw new Error("No data found");
    }

    logger.info(`${schemeCode}: user data successfully retrieved`, data);
    ctx.body = data;
  } catch (err) {
    logger.error(`${schemeCode}: failed to retrieve user data`, err);
    ctx.status = 422;
    // TODO: sample of an error response
    ctx.body = {
      errorDetails: {
        type: "warning",
        title: "Unable to start session",
        description: ["We are unable to proceed with your session"],
        actionType: "logout",
        actionLabel: "Logout now",
        redirectUrl: "https://supportgowhere.life.gov.sg/",
      },
    };
  }
};

export const submitApplication = async (ctx: Koa.Context): Promise<void> => {
  const requestBody = ctx.request.body;
  const { schemeCode } = ctx.params;
  const { nric, userId } = ctx.session;

  const sqServiceId = await getSqServiceIdFromSchemeCode(schemeCode);
  if (!sqServiceId) {
    logger.error(`${schemeCode}: sequential service id not found, scheme code not supported`);
    ctx.throw(400);
  }

  logger.info(`${schemeCode}: received application from ${redactNric(nric)}`, requestBody);

  const appSchema = await retrieveSchema(schemeCode);
  // TODO: retrieve scheme schema?
  if (!appSchema) {
    logger.error(`${schemeCode}: no active schema found`);
    ctx.throw(400);
  }

  // TODO: validation for bank details?

  // TODO: validate submission data
  const submissionData = extractPropertiesByPrefix("ui-", requestBody?.sessionData);

  // TODO Sevin: check files exist and save the hashed ids

  // TODO: check signatures exist?

  // TODO: check duplicate nrics?

  logger.info("submission data to be processed", submissionData);

  try {
    const data = await processSubmission(schemeCode, { nric, userId }, appSchema, submissionData);
    ctx.body = { data };
  } catch (error) {
    logger.error("unable to process submit application", error);
    ctx.throw(500);
  }
};

export const refreshSession = async (ctx: Koa.Context) => {
  logger.info("request to refresh session");
  ctx.status = 200;
};

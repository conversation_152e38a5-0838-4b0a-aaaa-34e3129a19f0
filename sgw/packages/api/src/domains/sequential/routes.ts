import Router from "@koa/router";
import { DefaultState, Context } from "koa";

import { refreshSession, submitApplication, getUserData } from "./controllers";

// System-to-system route: the routes will be verified by a JWT token send from Sequential
const sequentialRoutes = new Router();
sequentialRoutes.post("/form/:schemeCode/init", getUserData);
sequentialRoutes.post("/form/:schemeCode/submit", submitApplication);
sequentialRoutes.post("/session/refresh", refreshSession);

const router = new Router<DefaultState, Context>({ prefix: "/sequential" });
router.use(sequentialRoutes.routes(), sequentialRoutes.allowedMethods());

export { router };

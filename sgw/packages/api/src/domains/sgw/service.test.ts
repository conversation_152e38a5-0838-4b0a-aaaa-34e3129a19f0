import { ApplySchema } from "@sgw/common";
import {
  consentDao,
  dynamicOptionsDao,
  schemeDao,
  sgwApiClient,
  sgwService,
  userDao,
  applicationDao,
  Application,
} from "@sgw/services";

import { enquiryApiClient } from "../enquiry/apiclient";
import { sgwRedisService } from "./redis";
import * as serviceSgw from "./service";
import {
  getAgencyStatus,
  getDuplicateNricSections,
  getConsenterDetails,
  getMembers,
  getOptions,
  getApplicantDetailsForPendingConsent,
  getPrefillData,
  processOutstandingDocuments,
  evaluateConsentCondition,
  constructApplicationList,
  updateApplicationDatabase,
  SgwApplication,
  mergeUserData,
  ResidentialStatusCode,
  ChildrenBirthRecords,
} from "./service";
import { ApplicationState, dbToApiAppStatus } from "./util";
import DayJS from "dayjs";
import { smtaSchema, scfaSchema } from "./schema-mock";
import { get } from "lodash";
import * as util from "../../util/index";
import { SgwSession } from "../../server/session/interfaces";
import { appConfig } from "../../config/app";
import { decodeJwt } from "jose";

describe("getMembers", () => {
  it("could get members correctly", () => {
    const BASIC_SCHEMA = {
      section: [
        {
          members: [
            {
              type: "CUSTOM_FIELD",
              subType: "DYNAMIC_DROPDOWN",
              title: "A custom dropdown",
            },
          ],
        },
      ],
    } as const;

    const result = getMembers(BASIC_SCHEMA, "CUSTOM_FIELD", "DYNAMIC_DROPDOWN");
    expect(result.length).toEqual(1);
    expect(result[0]).toBe(BASIC_SCHEMA.section[0].members[0]);
  });

  it("could get members in conditional schema correctly", () => {
    const CONDITIONAL_SCHEMA = {
      section: [
        {
          members: [
            {
              type: "GROUP_CONDITIONAL",
              selector: {
                type: "CUSTOM_FIELD",
                subType: "DROPDOWN",
              },
              result: [
                { choice: ["0"], member: [{}] },
                {
                  choice: ["1"],
                  member: [
                    {
                      type: "CUSTOM_FIELD",
                      subType: "DYNAMIC_DROPDOWN",
                      title: "A group conditional nested dropdown",
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    } as const;

    const result = getMembers(CONDITIONAL_SCHEMA, "CUSTOM_FIELD", "DYNAMIC_DROPDOWN");
    expect(result.length).toEqual(1);
    expect(result[0]).toBe(CONDITIONAL_SCHEMA.section[0].members[0].result[1].member[0]);
  });
});

describe("getApplicantDetailsForPendingConsent", () => {
  it("should return applicant details if consent has NOT been given", async () => {
    const consentData = {
      consenterUuid: "consenterUuid",
      refId: "refId",
      userUuid: "userUuid",
      isProvided: false,
    };
    jest.spyOn(consentDao, "getPendingSgwConsent").mockResolvedValueOnce(consentData);
    jest.spyOn(userDao, "getByRefIdAndUserUuid").mockResolvedValueOnce({
      uuid: "userUuid",
      name: "applicant name",
    });

    const consent = await getApplicantDetailsForPendingConsent("consenterUuid", consentData.refId);

    expect(consent).toEqual({
      uuid: "userUuid",
      name: "applicant name",
    });
  });

  it("should return undefined if consent has been given", async () => {
    const consentData = {
      consenterUuid: "consenterUuid",
      refId: "refId",
      userUuid: "userUuid",
      isProvided: true,
    };
    jest.spyOn(consentDao, "getPendingSgwConsent").mockResolvedValueOnce(consentData);

    const consent = await getApplicantDetailsForPendingConsent("consenterUuid", "refId");

    expect(consent).toEqual(undefined);
  });

  it("should return undefined if no consent is required", async () => {
    jest.spyOn(consentDao, "getPendingSgwConsent").mockResolvedValueOnce(undefined);

    const consent = await getApplicantDetailsForPendingConsent("consenterUuid", "refId");

    expect(consent).toEqual(undefined);
  });
});

describe("getOptions", () => {
  const DUMMY_URL = "http://fake-url.com";
  const SCHEME_CODE = "fake-scheme-code";
  const REDIS_OPTIONS = { redisOption: "Redis Option" };
  const API_OPTIONS = { op1: "Option 1" };
  const DB_OPTIONS = { dbOption: "DB Option" };

  jest.spyOn(sgwRedisService, "setDynamicOptions").mockResolvedValue();
  jest.spyOn(schemeDao, "getActiveScheme").mockResolvedValue({});
  jest.spyOn(sgwService, "decryptJwt").mockResolvedValue(JSON.stringify(API_OPTIONS));
  jest.spyOn(dynamicOptionsDao, "upsert").mockResolvedValue();

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should not call url if options are in redis", async () => {
    jest.spyOn(sgwRedisService, "getDynamicOptions").mockResolvedValue(REDIS_OPTIONS);
    const sgwApiFn = jest.spyOn(sgwApiClient, "getDynamicCodeTable").mockResolvedValue("");
    const enquiryApiFn = jest.spyOn(enquiryApiClient, "getDynamicCodeTable").mockResolvedValue(API_OPTIONS);

    const options = await getOptions(DUMMY_URL, SCHEME_CODE);

    expect(sgwApiFn).not.toHaveBeenCalled();
    expect(enquiryApiFn).not.toHaveBeenCalled();
    expect(options).toEqual({
      options: REDIS_OPTIONS,
      url: DUMMY_URL,
    });
  });

  it("should retrieve options from sgwApiClient when schemeCode is provided", async () => {
    jest.spyOn(sgwRedisService, "getDynamicOptions").mockResolvedValue(null);
    const sgwApiFn = jest.spyOn(sgwApiClient, "getDynamicCodeTable").mockResolvedValue("");
    const enquiryApiFn = jest.spyOn(enquiryApiClient, "getDynamicCodeTable").mockResolvedValue({});
    const dbRetrievalFn = jest.spyOn(dynamicOptionsDao, "get").mockResolvedValue(DB_OPTIONS);

    const options = await getOptions(DUMMY_URL, SCHEME_CODE);

    expect(sgwApiFn).toHaveBeenCalled();
    expect(enquiryApiFn).not.toHaveBeenCalled();
    expect(dbRetrievalFn).not.toHaveBeenCalled();
    expect(options).toEqual({
      options: API_OPTIONS,
      url: DUMMY_URL,
    });
  });

  it("should retrieve options from db when redis and url retrieval is invalid", async () => {
    jest.spyOn(sgwRedisService, "getDynamicOptions").mockRejectedValue(null);
    jest.spyOn(sgwApiClient, "getDynamicCodeTable").mockRejectedValue(null);
    const dbRetrievalFn = jest.spyOn(dynamicOptionsDao, "get").mockResolvedValue(DB_OPTIONS);

    const options = await getOptions(DUMMY_URL, SCHEME_CODE);

    expect(dbRetrievalFn).toHaveBeenCalled();
    expect(options).toEqual({
      options: DB_OPTIONS,
      url: DUMMY_URL,
    });
  });
});

describe("getPrefillData", () => {
  const schema: ApplySchema = {
    id: "main",
    section: [
      {
        id: "section1",
        title: "Section Title",
        member: [
          {
            type: "PRESET_FIELD",
            subType: "name",
            id: "name",
            prefillSource: "agency.name",
          },
          {
            type: "PRESET_FIELD",
            subType: "name",
            id: "name2",
            prefillSource: "myInfo.name",
          },
          {
            type: "MULTI_VALUE",
            id: "child",
            prefillSource: "agency.child",
            title: "Please fill in your child details",
            header: "Child details",
            maxGroup: 5,
            group: {
              type: "CUSTOM_GROUP",
              subType: "BLANK",
              id: "childDetails",
              title: "Child details",
              member: [
                {
                  type: "PRESET_FIELD",
                  subType: "name",
                  id: "name",
                  prefillSource: "name",
                },
                {
                  type: "PRESET_FIELD",
                  subType: "dob",
                  id: "dob",
                  prefillSource: "dob",
                },
                {
                  id: "employment",
                  type: "GROUP_CONDITIONAL",
                  selector: {
                    id: "employmentStatus",
                    prefillSource: "employmentStatus",
                    editable: true,
                    type: "CUSTOM_FIELD",
                    title: "Employment status",
                    subType: "DROPDOWN",
                    options: {
                      ES1: "Working",
                      ES2: "Looking for work",
                      ES3: "Not working",
                    },
                  },
                  result: [
                    {
                      choice: ["ES1"],
                      member: [
                        {
                          type: "CUSTOM_FIELD",
                          subType: "MONEY",
                          id: "grossMonthlyIncome",
                          prefillSource: "grossMonthlyIncome",
                          editable: true,
                          title: "Gross monthly income",
                        },
                      ],
                    },
                    {
                      choice: ["ES3"],
                      member: [
                        {
                          id: "situation",
                          type: "GROUP_CONDITIONAL",
                          selector: {
                            id: "situationType",
                            prefillSource: "situationType",
                            editable: true,
                            type: "CUSTOM_FIELD",
                            title: "Describe household member’s current situation",
                            description: ["You may select more than one option."],
                            subType: "CHECKBOX",
                            options: {
                              UR1: "Retiree",
                              UR2: "Homemaker",
                              UR3: "Full-time caregiver",
                              UR4: "Student",
                              UR5: "Medically unfit for work",
                              UR6: "Others",
                            },
                          },
                          result: [
                            {
                              choice: ["UR6"],
                              member: [
                                {
                                  type: "CUSTOM_FIELD",
                                  subType: "MULTILINE_TEXT",
                                  id: "others",
                                  prefillSource: "othersReason",
                                  editable: true,
                                  title: "Tell us his/her situation.",
                                  maxLength: 300,
                                },
                              ],
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    ],
  };

  const userData: any = {
    agency: {
      name: "Kevin Tan",
      child: [
        {
          name: "Abbey Tan",
          employmentStatus: "ES1",
          grossMonthlyIncome: "2000.00",
        },
        {
          name: "Joelle Tan",
          dob: "2020-01-01",
          employmentStatus: "ES3",
          situationType: ["UR6"],
          othersReason: "Internship",
        },
      ],
    },
  };

  it("should get non-myinfo prefill data correctly even if not all user data have value and for nested conditional groups", () => {
    const result = getPrefillData(schema, userData);
    expect(result).toEqual({
      "section1.name": "Kevin Tan",

      // multi value
      "section1.child.childDetailsCount": "2",
      "section1.child.childDetails.0.name": "Abbey Tan", // no dob for Abbey as user data does not have this value
      "section1.child.childDetails.1.name": "Joelle Tan",
      "section1.child.childDetails.1.dob": "2020-01-01",

      // nested group conditional in multi value
      "section1.child.childDetails.0.employment.employmentStatus": "ES1",
      "section1.child.childDetails.0.employment.grossMonthlyIncome": "2000.00",
      "section1.child.childDetails.1.employment.employmentStatus": "ES3",
      "section1.child.childDetails.1.employment.situation.situationType": ["UR6"],
      "section1.child.childDetails.1.employment.situation.others": "Internship",
    });
  });

  it("should return undefined when schema does not require any prefill", () => {
    const result = getPrefillData(
      {
        section: [
          {
            id: "section1",
            title: "Section Title",
            member: [
              {
                type: "PRESET_FIELD",
                subType: "name",
                id: "name",
              },
            ],
          },
        ],
      },
      userData,
    );
    expect(result).toBe(undefined);
  });

  it("should return undefined when there's no user data", () => {
    const result = getPrefillData(schema, undefined);
    expect(result).toBe(undefined);
  });
});

describe("processOutstandingDocuments", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  const mockSession: any = { nric: "*********", userId: "1234-1234" };
  const mockScheme: any = { code: "XYZ", appSubmissionUrl: "sample.com" };
  const mockSubmissionData = {};
  const mockHashedFileNames = ["filename"];
  const mockEmail = "<EMAIL>";

  it("should submit attachments only if there is only file upload field", async () => {
    const mockSchema: any = {
      section: [
        {
          member: [
            {
              type: "CUSTOM_GROUP",
              subType: "FILE_UPLOAD",
            },
            {
              type: "CUSTOM_FIELD",
              subType: "FILE_UPLOAD",
            },
          ],
        },
      ],
    };
    const mockAppStatus: any = { statusCode: "20" };

    const mockUploadApplication = jest.spyOn(sgwService, "uploadApplication").mockResolvedValue();
    const mockSendAttachmentMessage = jest.spyOn(serviceSgw, "sendAttachmentMessage").mockResolvedValue();
    const mockUpsert = jest.spyOn(applicationDao, "upsert").mockResolvedValue();
    const mockSendOutstandingDocumentsSuccessEmail = jest
      .spyOn(serviceSgw, "sendOutstandingDocumentsSuccessEmail")
      .mockResolvedValue(mockEmail);

    const result = await processOutstandingDocuments(
      mockSession,
      mockScheme,
      mockSchema,
      mockSchema,
      mockAppStatus,
      mockSubmissionData,
      mockHashedFileNames,
    );

    expect(mockUploadApplication).not.toHaveBeenCalled();
    expect(mockSendAttachmentMessage).toHaveBeenCalled();
    expect(mockUpsert).toHaveBeenCalled();
    expect(mockSendOutstandingDocumentsSuccessEmail).toHaveBeenCalled();
    expect(result).toBe(mockEmail);
  });

  it("should submit application data and attachments if there is file upload and other input fields", async () => {
    const mockSchema: any = {
      section: [
        {
          member: [
            {
              type: "CUSTOM_GROUP",
              subType: "FILE_UPLOAD",
            },
            {
              type: "CUSTOM_FIELD",
              subType: "FREE_TEXT",
            },
            {
              type: "CUSTOM_FIELD",
              subType: "MULTILINE_TEXT",
            },
          ],
        },
      ],
    };
    const mockAppStatus: any = { statusCode: "20" };

    const mockUploadApplication = jest.spyOn(sgwService, "uploadApplication").mockResolvedValue();
    const mockSendAttachmentMessage = jest.spyOn(serviceSgw, "sendAttachmentMessage").mockResolvedValue();
    const mockUpsert = jest.spyOn(applicationDao, "upsert").mockResolvedValue();
    const mockSendOutstandingDocumentsSuccessEmail = jest
      .spyOn(serviceSgw, "sendOutstandingDocumentsSuccessEmail")
      .mockResolvedValue(mockEmail);

    const result = await processOutstandingDocuments(
      mockSession,
      mockScheme,
      mockSchema,
      mockSchema,
      mockAppStatus,
      mockSubmissionData,
      mockHashedFileNames,
    );

    expect(mockUploadApplication).toHaveBeenCalled();
    expect(mockSendAttachmentMessage).toHaveBeenCalled();
    expect(mockUpsert).toHaveBeenCalled();
    expect(mockSendOutstandingDocumentsSuccessEmail).toHaveBeenCalled();
    expect(result).toBe(mockEmail);
  });

  it("should handle submitted status when submitting consent documents", async () => {
    const mockSchema: any = {
      section: [
        {
          member: [
            {
              type: "CUSTOM_GROUP",
              subType: "FILE_UPLOAD",
            },
          ],
        },
      ],
    };
    const mockAppStatus: any = { statusCode: "0" };

    const mockUploadApplication = jest.spyOn(sgwService, "uploadApplication").mockResolvedValue();
    const mockSendAttachmentMessage = jest.spyOn(serviceSgw, "sendAttachmentMessage").mockResolvedValue();
    const mockUpsert = jest.spyOn(applicationDao, "upsert").mockResolvedValue();
    const mockSendOutstandingDocumentsSuccessEmail = jest
      .spyOn(serviceSgw, "sendOutstandingDocumentsSuccessEmail")
      .mockResolvedValue(mockEmail);

    const result = await processOutstandingDocuments(
      mockSession,
      mockScheme,
      mockSchema,
      mockSchema,
      mockAppStatus,
      mockSubmissionData,
      mockHashedFileNames,
    );

    expect(mockUploadApplication).not.toHaveBeenCalled();
    expect(mockSendAttachmentMessage).toHaveBeenCalled();
    expect(mockUpsert).not.toHaveBeenCalled();
    expect(mockSendOutstandingDocumentsSuccessEmail).toHaveBeenCalled();
    expect(result).toBe(mockEmail);
  });
});

describe("getConsenterDetails", () => {
  const mockSchema: ApplySchema = {
    section: [
      {
        id: "maritalStatus",
        title: "Marital status",
        member: [
          {
            id: "spouseName",
            type: "PRESET_FIELD",
            subType: "name",
          },
          {
            id: "spouseIdNumber",
            type: "PRESET_FIELD",
            consent: true,
            subType: "nric",
            consenterInfo: {
              name: "spouseName",
            },
          },
        ],
        subtitle: "Tell us about your marital status",
      },
      {
        id: "family",
        title: "Family",
        member: [
          {
            id: "member",
            type: "PRESET_FIELD",
            subType: "family",
            consent: true,
          },
        ],
        subtitle: "Tell us about your family members",
      },
    ],
  };

  const mockMultiValueSchema: ApplySchema = {
    section: [
      {
        id: "family",
        title: "Family",
        member: [
          {
            id: "familyMember",
            type: "MULTI_VALUE",
            group: {
              id: "familyMemberDetails",
              type: "CUSTOM_GROUP",
              title: "Family member",
              member: [
                {
                  id: "spouseName",
                  type: "PRESET_FIELD",
                  subType: "name",
                },
                {
                  id: "spouseIdNumber",
                  type: "PRESET_FIELD",
                  consent: true,
                  subType: "nric",
                  consenterInfo: {
                    name: "spouseName",
                  },
                },
                {
                  id: "member",
                  type: "PRESET_FIELD",
                  subType: "family",
                  consent: true,
                },
              ],
              subType: "BLANK",
            },
            title: "How many of them are living with you?",
            header: "Family member details",
            maxGroup: 12,
          },
        ],
        subtitle: "Tell us about your family members",
      },
    ],
  };

  const mockPwdSchema: ApplySchema = {
    section: [
      {
        id: "caregiverGuardian",
        title: "Guardian / Caregiver",
        member: [
          {
            id: "caregiverGuardian",
            type: "MULTI_VALUE",
            group: {
              id: "caregiverGuardianRecords",
              type: "CUSTOM_GROUP",
              title: "Guardian / Caregiver",
              member: [
                {
                  id: "idName",
                  type: "PRESET_FIELD",
                  subType: "name",
                },
                {
                  id: "idNo",
                  type: "PRESET_FIELD",
                  consent: {
                    field: "role",
                    value: "2",
                    operator: "notEqual",
                  },
                  subType: "nric",
                  consenterInfo: {
                    name: "idName",
                  },
                },
                {
                  id: "role",
                  type: "CUSTOM_FIELD",
                  subType: "DROPDOWN",
                  title: "Role to PWD",
                  options: ["Parent / Legal guardian", "Donee / Deputy", "Caregiver / Non-legal guardian"],
                },
              ],
              subType: "BLANK",
            },
            title: "How many other legal guardians / caregivers does the PWD have, excluding yourself?",
            header: "Guardian / Caregiver details",
            maxGroup: 11,
          },
        ],
      },
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should handle empty data", () => {
    const mockData = {};
    const result = getConsenterDetails(mockSchema, mockData);

    expect(result).toEqual([]);
  });

  it("should return an array of consenter objects with NRIC and name", () => {
    const mockData = {
      maritalStatus: {
        spouseName: "Johnny",
        spouseIdNumber: "*********",
      },
      family: {
        member: {
          memberIdNumber: "*********",
          memberName: "Tan Ke Yuan",
          memberDob: "2022-10-04",
          memberSex: "F",
          memberRecord: "mock-record",
          optionType: "myinfo",
        },
      },
    };
    const result = getConsenterDetails(mockSchema, mockData);

    expect(result).toEqual([
      { name: "JOHNNY", nric: "*********" },
      { name: "TAN KE YUAN", nric: "*********" },
    ]);
  });

  it("should return an array of consenter objects with NRIC and name for multi-valued field", () => {
    const mockData = {
      family: {
        familyMember: {
          familyMemberDetails: [
            {
              spouseName: "Johnny",
              spouseIdNumber: "*********",
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Ke Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
            {
              spouseName: "Jimmy",
              spouseIdNumber: "*********",
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Ai Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
          ],
        },
      },
    };
    const result = getConsenterDetails(mockMultiValueSchema, mockData);

    expect(result).toEqual([
      { name: "JOHNNY", nric: "*********" },
      { name: "JIMMY", nric: "*********" },
      { name: "TAN KE YUAN", nric: "*********" },
      { name: "TAN AI YUAN", nric: "*********" },
    ]);
  });

  it("should return an array of consenter objects with NRIC and name where role meets the consent condition for multi-valued field", () => {
    const mockData = {
      caregiverGuardian: {
        caregiverGuardian: {
          caregiverGuardianRecords: [
            {
              idName: "John",
              idNo: "*********",
              role: "0",
            },
            {
              idName: "May",
              idNo: "*********",
              role: "1",
            },
            {
              idName: "James",
              idNo: "*********",
              role: "2",
            },
            {
              idName: "Mark",
              idNo: "*********",
              role: "1",
            },
          ],
        },
      },
    };
    const result = getConsenterDetails(mockPwdSchema, mockData);

    expect(result).toEqual([
      { name: "JOHN", nric: "*********" },
      { name: "MAY", nric: "*********" },
      { name: "MARK", nric: "*********" },
    ]);
  });

  it("should throw error when consenter NRIC are not able to get name id", () => {
    const mockSchema: ApplySchema = {
      section: [
        {
          id: "maritalStatus",
          title: "Marital status",
          member: [
            {
              id: "spouseIdNumber",
              type: "PRESET_FIELD",
              consent: true,
              subType: "nric",
            },
          ],
          subtitle: "Tell us about your marital status",
        },
      ],
    };

    const mockData = {
      maritalStatus: {
        spouseIdNumber: "*********",
      },
    };

    expect(() => {
      getConsenterDetails(mockSchema, mockData);
    }).toThrow("unable to proceed without consenter full name id");
  });

  it("should throw error when consenter NRIC are not able to get name value", () => {
    const mockSchema: ApplySchema = {
      section: [
        {
          id: "maritalStatus",
          title: "Marital status",
          member: [
            {
              id: "spouseName",
              type: "PRESET_FIELD",
              subType: "name",
            },
            {
              id: "spouseIdNumber",
              type: "PRESET_FIELD",
              consent: true,
              subType: "nric",
              consenterInfo: {
                name: "spouseName",
              },
            },
          ],
          subtitle: "Tell us about your marital status",
        },
      ],
    };

    const mockData = {
      maritalStatus: {
        spouseIdNumber: "*********",
      },
    };

    expect(() => {
      getConsenterDetails(mockSchema, mockData);
    }).toThrow("unable to proceed as consenter need to have both NRIC and name");
  });

  it("should throw error when consenter NRIC are not able to get name id for multi-valued field", () => {
    const mockMultiValueSchema: ApplySchema = {
      section: [
        {
          id: "family",
          title: "Family",
          member: [
            {
              id: "familyMember",
              type: "MULTI_VALUE",
              group: {
                id: "familyMemberDetails",
                type: "CUSTOM_GROUP",
                title: "Family member",
                member: [
                  {
                    id: "spouseIdNumber",
                    type: "PRESET_FIELD",
                    consent: true,
                    subType: "nric",
                  },
                ],
                subType: "BLANK",
              },
              title: "How many of them are living with you?",
              header: "Family member details",
              maxGroup: 12,
            },
          ],
          subtitle: "Tell us about your family members",
        },
      ],
    };

    const mockData = {
      family: {
        familyMember: {
          familyMemberDetails: [
            {
              spouseIdNumber: "*********",
            },
          ],
        },
      },
    };

    expect(() => {
      getConsenterDetails(mockMultiValueSchema, mockData);
    }).toThrow("unable to proceed without consenter full name id");
  });

  it("should throw error when consenter NRIC are not able to get name value for multi-valued field", () => {
    const mockMultiValueSchema: ApplySchema = {
      section: [
        {
          id: "family",
          title: "Family",
          member: [
            {
              id: "familyMember",
              type: "MULTI_VALUE",
              group: {
                id: "familyMemberDetails",
                type: "CUSTOM_GROUP",
                title: "Family member",
                member: [
                  {
                    id: "spouseName",
                    type: "PRESET_FIELD",
                    subType: "name",
                  },
                  {
                    id: "spouseIdNumber",
                    type: "PRESET_FIELD",
                    consent: true,
                    subType: "nric",
                    consenterInfo: {
                      name: "spouseName",
                    },
                  },
                ],
                subType: "BLANK",
              },
              title: "How many of them are living with you?",
              header: "Family member details",
              maxGroup: 12,
            },
          ],
          subtitle: "Tell us about your family members",
        },
      ],
    };

    const mockData = {
      family: {
        familyMember: {
          familyMemberDetails: [
            {
              spouseIdNumber: "*********",
            },
          ],
        },
      },
    };

    expect(() => {
      getConsenterDetails(mockMultiValueSchema, mockData);
    }).toThrow("unable to proceed as consenter need to have both NRIC and name");
  });
});

describe("getDuplicatedNricSections", () => {
  const mockSchema: ApplySchema = {
    section: [
      {
        id: "profile",
        title: "Profile",
        member: [
          {
            id: "personalDetails",
            type: "CUSTOM_GROUP",
            title: "Personal details",
            member: [
              {
                id: "nric",
                type: "PRESET_FIELD",
                subType: "nric",
                prefillSource: "myInfo.nric",
              },
            ],
            subType: "BLANK",
          },
        ],
        subtitle: "Tell us about yourself",
      },
      {
        id: "disability",
        title: "Disability",
        member: [
          {
            id: "pwdPersonalDetails",
            type: "CUSTOM_GROUP",
            title: "PWD’s personal details",
            member: [
              {
                id: "idNo",
                type: "PRESET_FIELD",
                subType: "nric",
              },
            ],
            subType: "BLANK",
          },
        ],
        subtitle: "Tell us about the PWD’s details and their disability",
      },
      {
        id: "beneficiary",
        title: "Beneficiary",
        member: [
          {
            id: "child",
            type: "MULTI_VALUE",
            group: {
              id: "childDetails",
              type: "CUSTOM_GROUP",
              title: "Child",
              member: [
                {
                  id: "child",
                  type: "PRESET_FIELD",
                  subType: "child",
                },
                {
                  id: "payeeNric",
                  type: "PRESET_FIELD",
                  subType: "nric",
                  skipDuplicateCheck: true,
                },
              ],
              subType: "BLANK",
            },
            title: "Number of beneficiaries you are applying for",
            header: "Beneficiary details",
            maxGroup: 6,
          },
          {
            id: "relationship",
            type: "SECTION_CONDITIONAL",
            result: [
              {
                choice: ["REL002", "REL205", "REL206"],
                member: [
                  {
                    type: "DECORATOR",
                    title: "Additional documents to upload",
                    subType: "HEADER",
                  },
                  {
                    id: "SD24",
                    type: "CUSTOM_GROUP",
                    title: "Entry/Re-entry Permit",
                    subType: "FILE_UPLOAD",
                    optional: true,
                    documents: [
                      {
                        paragraph: "**Entry/Re-entry Permit** if your child is a Permanent Resident",
                      },
                    ],
                  },
                ],
              },
              {
                choice: ["REL203", "REL204"],
                member: [
                  {
                    type: "DECORATOR",
                    title: "Additional documents to upload",
                    subType: "HEADER",
                  },
                  {
                    id: "SD4",
                    type: "CUSTOM_GROUP",
                    title: "Adoption paper",
                    subType: "FILE_UPLOAD",
                    documents: [
                      {
                        paragraph: "**Adoption paper/proof** that you are taking care of the child",
                      },
                    ],
                  },
                  {
                    id: "SD24",
                    type: "CUSTOM_GROUP",
                    title: "Entry/Re-entry Permit",
                    subType: "FILE_UPLOAD",
                    optional: true,
                    documents: [
                      {
                        paragraph: "**Entry/Re-entry Permit** if your child is a Permanent Resident",
                      },
                    ],
                  },
                ],
              },
              {
                choice: ["REL009"],
                member: [
                  {
                    type: "DECORATOR",
                    title: "Additional documents to upload",
                    subType: "HEADER",
                  },
                  {
                    id: "SD5",
                    type: "CUSTOM_GROUP",
                    title: "Guardianship documents",
                    subType: "FILE_UPLOAD",
                    documents: [
                      {
                        indent: [
                          {
                            paragraph: "Guardianship paper",
                          },
                        ],
                        paragraph: "**Legal guardian**:",
                      },
                      {
                        indent: [
                          {
                            indent: [
                              "Parent(s)’ death certificate",
                              "Police report",
                              "Prison letter",
                              "[Annex 6](https://www.msf.gov.sg/docs/default-source/student-care/annex-6-self-declaration-form.pdf): SCFA Self Declaration Form",
                              "Proof that non-legal guardian is also applicant of approved MOE-FAS application for child",
                            ],
                            paragraph:
                              "Any of the following documents explaining the need to be the non-legal guardian of child such as:",
                          },
                        ],
                        paragraph: "**Non-legal guardian**:",
                      },
                      {
                        indent: [
                          {
                            paragraph: "Letter of recommendation from foster care agencies",
                          },
                        ],
                        paragraph: "**Foster parent**:",
                      },
                    ],
                  },
                  {
                    id: "SD24",
                    type: "CUSTOM_GROUP",
                    title: "Entry/Re-entry Permit",
                    subType: "FILE_UPLOAD",
                    optional: true,
                    documents: [
                      {
                        paragraph: "**Entry/Re-entry Permit** if your child is a Permanent Resident",
                      },
                    ],
                  },
                ],
              },
            ],
            selector: {
              id: "childRelationshipType",
              type: "PRESET_FIELD",
              title: "Relationship to beneficiary",
              subType: "relationshipType",
              allowedOptions: ["REL002", "REL205", "REL206", "REL203", "REL204", "REL009"],
            },
          },
        ],
        subtitle: "Tell us about the child you are applying for",
      },
      {
        id: "maritalStatus",
        title: "Marital status",
        member: [
          {
            id: "spouseIdNumber",
            type: "PRESET_FIELD",
            consent: true,
            subType: "nric",
          },
        ],
        subtitle: "Tell us about your marital status",
      },
      {
        id: "family",
        title: "Family",
        member: [
          {
            id: "familyMember",
            type: "MULTI_VALUE",
            group: {
              id: "familyMemberDetails",
              type: "CUSTOM_GROUP",
              title: "Family member",
              member: [
                {
                  id: "member",
                  type: "PRESET_FIELD",
                  subType: "family",
                  consent: true,
                },
              ],
              subType: "BLANK",
            },
            title: "How many of them are living with you?",
            header: "Family member details",
            maxGroup: 12,
          },
        ],
        subtitle: "Tell us about your family members",
      },
    ],
  };
  const mockDuplicatedSections = ["Profile", "Disability", "Beneficiary", "Marital status", "Family"];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should handle empty data", () => {
    const mockData = {};
    const result = getDuplicateNricSections(mockSchema, mockData);

    expect(result).toEqual([]);
  });

  it("should handle no duplicated nric", () => {
    const mockData = {
      profile: {
        personalDetails: {
          nric: "*********",
        },
      },
      beneficiary: {
        child: {
          childDetails: [
            {
              child: {
                childDob: "2022-10-04",
                childSex: "F",
                childName: "Tan Chiu",
                optionType: "myinfo",
                childRecord: "mock-record",
                childIdNumber: "*********",
              },
            },
          ],
          childDetailsCount: "1",
        },
        relationship: {
          childRelationshipType: "REL002",
        },
      },
      maritalStatus: {
        spouseIdNumber: "*********",
      },
      family: {
        familyMember: {
          familyMemberDetails: [
            {
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Ke Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
            {
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Ai Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
          ],
        },
      },
    };
    const result = getDuplicateNricSections(mockSchema, mockData);

    expect(result).toEqual([]);
  });

  it("should handle intra-section duplication", () => {
    const mockData = {
      profile: {
        personalDetails: {
          nric: "*********",
        },
      },
      family: {
        familyMember: {
          familyMemberDetails: [
            {
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Ke Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
            {
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Ai Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
            {
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Go Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
            {
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Peh Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
          ],
        },
      },
    };
    const result = getDuplicateNricSections(mockSchema, mockData);

    expect(result).toEqual(mockDuplicatedSections);
  });

  it("should handle inter-section duplication with same duplicated nric", () => {
    const mockData = {
      profile: {
        personalDetails: {
          nric: "*********",
        },
      },
      maritalStatus: {
        spouseIdNumber: "*********",
      },
      family: {
        familyMember: {
          familyMemberDetails: [
            {
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Ke Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
            {
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Ai Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
          ],
        },
      },
    };
    const result = getDuplicateNricSections(mockSchema, mockData);

    expect(result).toEqual(mockDuplicatedSections);
  });

  it("should handle inter-section duplication with same duplicated nric for non-prefilled nric", () => {
    const mockData = {
      profile: {
        personalDetails: {
          nric: "*********",
        },
      },
      disability: {
        pwdPersonalDetails: {
          idNo: "*********",
        },
      },
      maritalStatus: {
        spouseIdNumber: "*********",
      },
    };
    const result = getDuplicateNricSections(mockSchema, mockData);

    expect(result).toEqual(mockDuplicatedSections);
  });

  it("should handle inter-section duplication with two or more different duplicates", () => {
    const mockData = {
      profile: {
        personalDetails: {
          nric: "*********",
        },
      },
      beneficiary: {
        child: {
          childDetails: [
            {
              child: {
                childDob: "2022-10-04",
                childSex: "F",
                childName: "Tan Chiu",
                optionType: "myinfo",
                childRecord: "mock-record",
                childIdNumber: "*********",
              },
            },
          ],
          childDetailsCount: "1",
        },
        relationship: {
          childRelationshipType: "REL002",
        },
      },
      maritalStatus: {
        spouseIdNumber: "*********",
      },
      family: {
        familyMember: {
          familyMemberDetails: [
            {
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Ke Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
            {
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Ai Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
            {
              member: {
                memberIdNumber: "*********",
                memberName: "Tan Ba Yuan",
                memberDob: "2022-10-04",
                memberSex: "F",
                memberRecord: "mock-record",
                optionType: "myinfo",
              },
            },
          ],
        },
      },
    };
    const result = getDuplicateNricSections(mockSchema, mockData);

    expect(result).toEqual(mockDuplicatedSections);
  });

  it("should skip duplicate check for payee nric with skipDuplicateCheck key set to true", () => {
    const mockData = {
      profile: {
        personalDetails: {
          nric: "*********",
        },
      },
      beneficiary: {
        child: {
          childDetails: [
            {
              child: {
                childDob: "2022-10-04",
                childSex: "F",
                childName: "Tan Chiu",
                optionType: "myinfo",
                childRecord: "mock-record",
                childIdNumber: "*********",
              },
              payeeNric: "*********",
            },
            {
              child: {
                childDob: "2021-09-04",
                childSex: "M",
                childName: "Tan Kee",
                optionType: "myinfo",
                childRecord: "mock-record",
                childIdNumber: "*********",
              },
              payeeNric: "*********",
            },
          ],
          childDetailsCount: "2",
        },
        relationship: {
          childRelationshipType: "REL002",
        },
      },
    };
    const result = getDuplicateNricSections(mockSchema, mockData);

    expect(result).toEqual([]);
  });
});

jest.mock("@sgw/services");

describe("getAgencyStatus", () => {
  const mockSgwSession = {
    nric: "*********",
    userId: "mockUserId",
    sessionId: "mockSessionId",
  };

  const mockScheme = {
    code: "fake-scheme-code",
    name: "Fake Scheme",
    appSubmissionUrl: "https://submission.api",
    appStatusUrl: "https://test.api",
  };

  const mockSchemeCode = "fake-scheme-code";

  const agencyStatus = [
    {
      refId: "MDA-1OP53CSR6G",
      status: "1",
      appliedDateTime: "2023-01-17T03:24:00.123Z",
      updatedDateTime: "2023-03-01T03:24:00.000Z",
      detail: [
        {
          title: "Sharon Rebecca Tay Kaling",
          category: "2",
          status: "5",
          remarks:
            "Ref no.: ABC-1MC3C25C11\nSCC: YMCA Student Care Centre @ St Anthony’s - St Anthony's Primary School",
        },
      ],
    },
  ];

  const maintenanceStartDate = "2024-10-17T06:00:00Z";
  const maintenanceEndDate = "2024-10-17T10:30:00Z";
  const expectedStartDate = DayJS(maintenanceStartDate).format("D MMM YYYY");
  const expectedStartTime = DayJS(maintenanceStartDate).format("h:mm a");
  const expectedEndTimeSameDay = DayJS(maintenanceEndDate).format("h:mm a");

  const encryptedResponse = JSON.stringify({
    application: agencyStatus,
    eligible: true,
    systemData: {
      maintenanceStartDate,
      maintenanceEndDate,
    },
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should successfully retrieve and decrypt agency status", async () => {
    const mockJwt = "mockJwt";
    const mockStatusResponse = "mockStatusResponse";

    (sgwService.createJwt as jest.Mock).mockResolvedValue(mockJwt);
    (sgwApiClient.getStatus as jest.Mock).mockResolvedValue(mockStatusResponse);
    (sgwService.decryptJwt as jest.Mock).mockResolvedValue(encryptedResponse);

    const result = await getAgencyStatus(mockSgwSession, mockScheme, mockSchemeCode);

    expect(sgwService.createJwt).toHaveBeenCalledWith(JSON.stringify({ nric: mockSgwSession.nric }), mockScheme);
    expect(sgwApiClient.getStatus).toHaveBeenCalledWith(`${mockScheme.appStatusUrl}?query=${mockJwt}`, mockSchemeCode);
    expect(sgwService.decryptJwt).toHaveBeenCalledWith(mockStatusResponse, mockScheme);

    // Validate the result returned by getAgencyStatus
    expect(result).toEqual({
      appStatus: [
        {
          refId: "MDA-1OP53CSR6G",
          status: "1",
          appliedDateTime: new Date("2023-01-17T03:24:00.123Z"),
          updatedDateTime: new Date("2023-03-01T03:24:00.000Z"),
          detail: [
            {
              title: "Sharon Rebecca Tay Kaling",
              category: "2",
              status: "5",
              remarks:
                "Ref no.: ABC-1MC3C25C11\nSCC: YMCA Student Care Centre @ St Anthony’s - St Anthony's Primary School",
            },
          ],
        },
      ],
      eligible: true,
      userData: undefined,
      agencyUnavailable: false,
      maintenanceMsg: `This scheme provider will be undergoing a scheduled maintenance on **${expectedStartDate}** from **${expectedStartTime}** to **${expectedEndTimeSameDay}**.`,
    });
  });

  it("should throw an error if the agency service is unavailable", async () => {
    (sgwService.createJwt as jest.Mock).mockResolvedValue("mockJwt");
    (sgwApiClient.getStatus as jest.Mock).mockRejectedValue(new Error("Agency error"));

    await expect(getAgencyStatus(mockSgwSession, mockScheme, mockSchemeCode)).rejects.toThrow("Agency error");

    expect(sgwService.createJwt).toHaveBeenCalledWith(JSON.stringify({ nric: mockSgwSession.nric }), mockScheme);
    expect(sgwApiClient.getStatus).toHaveBeenCalledWith(`${mockScheme.appStatusUrl}?query=mockJwt`, mockSchemeCode);
  });

  it("should throw a timeout error if the agency takes too long to respond", async () => {
    (sgwService.createJwt as jest.Mock).mockResolvedValue("mockJwt");
    (sgwApiClient.getStatus as jest.Mock).mockImplementation(() => {
      return new Promise((_, reject) => {
        setTimeout(() => {
          // mock shape of AxiosError
          reject({
            name: "AxiosError",
            message: "timeout of 5000ms exceeded",
            code: "ECONNABORTED",
            config: {},
            isAxiosError: true,
            toJSON: () => ({}),
          });
        }, 1000);
      });
    });

    await expect(getAgencyStatus(mockSgwSession, mockScheme, mockSchemeCode)).rejects.toMatchObject({
      code: "ECONNABORTED",
      message: expect.stringContaining("timeout"),
    });

    expect(sgwService.createJwt).toHaveBeenCalledWith(JSON.stringify({ nric: mockSgwSession.nric }), mockScheme);
    expect(sgwApiClient.getStatus).toHaveBeenCalledWith(`${mockScheme.appStatusUrl}?query=mockJwt`, mockSchemeCode);
  });
});

describe("evaluateConsentCondition", () => {
  it("should return true if consentCondition is not provided", () => {
    const item = {};
    const consentCondition = null;

    const result = evaluateConsentCondition(item, consentCondition);

    expect(result).toBe(true);
  });

  test.each([
    // Test cases for "equal" operator
    [{ field: "field", operator: "equal", value: "value1" }, { field: "value1" }, true],
    [{ field: "field", operator: "equal", value: "value1" }, { field: "value2" }, false],

    // Test cases for "notEqual" operator
    [{ field: "field", operator: "notEqual", value: "value1" }, { field: "value2" }, true],
    [{ field: "field", operator: "notEqual", value: "value1" }, { field: "value1" }, false],
  ])("should evaluate consent condition correctly for %p with item %p", (consentCondition, item, expectedResult) => {
    const result = evaluateConsentCondition(item, consentCondition);

    expect(result).toBe(expectedResult);
  });
});

describe("constructApplicationList", () => {
  const schemeCode = "smta";
  const userUuid = "abc-xyz";

  const sampleAppStatus: SgwApplication = {
    refId: "2",
    status: "0",
    appliedDateTime: new Date("2024-07-02"),
    updatedDateTime: new Date("2024-07-02"),
  };
  const sampleDbStatus = {
    userUuid,
    schemeCode,
    refId: "2",
    statusCode: "0",
    updatedDateTime: new Date("2024-07-02"),
    appliedDateTime: new Date("2024-07-02"),
  };

  it("should return an empty array when both agency and database have no data", () => {
    const appStatus: SgwApplication[] = [];
    const dbStatus: Application[] = [];

    const result = constructApplicationList(appStatus, dbStatus);

    expect(result).toEqual([]);
  });

  it("should return the latest database status when agency has no data", () => {
    const appStatus: SgwApplication[] = [];
    const dbStatus: Application[] = [sampleDbStatus];

    const result = constructApplicationList(appStatus, dbStatus);

    expect(result).toEqual([dbToApiAppStatus(dbStatus[0])]);
  });

  it("should return the latest agency status when database has no data", () => {
    const appStatus: SgwApplication[] = [sampleAppStatus];
    const dbStatus: Application[] = [];

    const result = constructApplicationList(appStatus, dbStatus);

    expect(result).toEqual([appStatus[0]]);
  });

  it("should return the latest database status when agency and database have matching statuses, with database being latest", () => {
    const appStatus: SgwApplication[] = [sampleAppStatus];
    const dbStatus: Application[] = [
      {
        userUuid,
        schemeCode,
        refId: "2",
        statusCode: "2",
        updatedDateTime: new Date("2024-07-10"),
        appliedDateTime: new Date("2024-07-02"),
      },
    ];

    const result = constructApplicationList(appStatus, dbStatus);

    expect(result).toEqual([dbToApiAppStatus(dbStatus[0])]);
  });

  it("should return the latest agency status when agency and database have matching statuses, with agency being latest", () => {
    const appStatus: SgwApplication[] = [
      {
        refId: "2",
        status: "2",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-10"),
      },
    ];
    const dbStatus: Application[] = [sampleDbStatus];

    const result = constructApplicationList(appStatus, dbStatus);

    expect(result).toEqual([appStatus[0]]);
  });

  it("should return the agency status when agency and database have matching statuses that are equal", () => {
    const appStatus: SgwApplication[] = [sampleAppStatus];
    const dbStatus: Application[] = [sampleDbStatus];

    const result = constructApplicationList(appStatus, dbStatus);

    expect(result).toEqual([appStatus[0]]);
  });
  it("should return all agency statuses when some are not present in the database", () => {
    const appStatus: SgwApplication[] = [
      { refId: "10", status: "0", appliedDateTime: new Date("2024-07-10"), updatedDateTime: new Date("2024-07-10") },
      sampleAppStatus,
    ];
    const dbStatus: Application[] = [sampleDbStatus];

    const result = constructApplicationList(appStatus, dbStatus);

    expect(result).toEqual(appStatus);
  });

  it("should return both database statuses and agency statuses when some database statuses do not have matches in the agency", () => {
    const appStatus: SgwApplication[] = [sampleAppStatus];
    const dbStatus: Application[] = [
      {
        userUuid,
        schemeCode,
        refId: "10",
        statusCode: "0",
        appliedDateTime: new Date("2024-07-10"),
        updatedDateTime: new Date("2024-07-10"),
      },
      sampleDbStatus,
    ];

    const result = constructApplicationList(appStatus, dbStatus);

    expect(result).toEqual([dbToApiAppStatus(dbStatus[0]), appStatus[0]]);
  });
});

describe("updateApplicationDatabase", () => {
  const schemeCode = "smta";
  const userUuid = "abc-xyz";
  const sampleAppStatus: SgwApplication = {
    refId: "2",
    status: "0",
    appliedDateTime: new Date("2024-07-02"),
    updatedDateTime: new Date("2024-07-02"),
  };
  const sampleDbStatus = {
    userUuid,
    schemeCode,
    refId: "2",
    statusCode: "0",
    updatedDateTime: new Date("2024-07-02"),
    appliedDateTime: new Date("2024-07-02"),
  };

  let updateApplicationSpy;
  let deleteApplicationSpy;
  let getAppFromDbSpy;

  beforeEach(() => {
    updateApplicationSpy = jest.spyOn(serviceSgw, "updateApplication").mockResolvedValue(undefined);
    deleteApplicationSpy = jest.spyOn(serviceSgw, "deleteApplication").mockResolvedValue(undefined);
    getAppFromDbSpy = jest.spyOn(serviceSgw, "getAppFromDb").mockResolvedValue([]);

    jest.clearAllMocks();
  });

  afterEach(() => {
    updateApplicationSpy.mockRestore();
    deleteApplicationSpy.mockRestore();
    getAppFromDbSpy.mockRestore();
  });

  it("should not perform any action when both agency and database are empty", async () => {
    const appStatus: SgwApplication[] = [];
    const dbStatus: Application[] = [];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).not.toHaveBeenCalled();
    expect(deleteApplicationSpy).not.toHaveBeenCalled();
    expect(getAppFromDbSpy).not.toHaveBeenCalled();
  });

  it("should update the database when agency has a newer application status", async () => {
    const appStatus: SgwApplication[] = [
      {
        refId: "2",
        status: "1",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-10"),
      },
    ];
    const dbStatus: Application[] = [sampleDbStatus];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).toHaveBeenCalledWith(appStatus[0], userUuid, schemeCode);
    expect(deleteApplicationSpy).not.toHaveBeenCalled();
    expect(getAppFromDbSpy).toHaveBeenCalled();
  });

  it("should not update when the database has a newer application status", async () => {
    const appStatus: SgwApplication[] = [sampleAppStatus];
    const dbStatus: Application[] = [
      {
        userUuid,
        schemeCode,
        refId: "2",
        statusCode: "1",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-10"),
      },
    ];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).not.toHaveBeenCalled();
    expect(deleteApplicationSpy).not.toHaveBeenCalled();
    expect(getAppFromDbSpy).not.toHaveBeenCalled();
  });

  it("should not update when agency and database statuses are identical", async () => {
    const appStatus: SgwApplication[] = [sampleAppStatus];
    const dbStatus: Application[] = [sampleDbStatus];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).not.toHaveBeenCalled();
    expect(deleteApplicationSpy).not.toHaveBeenCalled();
    expect(getAppFromDbSpy).not.toHaveBeenCalled();
  });

  it("should delete the outdated database entry when the agency has the latest status, and the database entry is no longer the latest and is terminal", async () => {
    const appStatus: SgwApplication[] = [
      {
        refId: "10",
        status: "0",
        appliedDateTime: new Date("2024-07-10"),
        updatedDateTime: new Date("2024-07-10"),
      },
    ];
    const dbStatus: Application[] = [
      {
        userUuid,
        schemeCode,
        refId: "2",
        statusCode: "3",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-02"),
      },
    ];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).toHaveBeenCalledWith(appStatus[0], userUuid, schemeCode);
    expect(deleteApplicationSpy).toHaveBeenCalledWith(userUuid, dbStatus[0].refId);
    expect(getAppFromDbSpy).toHaveBeenCalled();
  });

  it("should update the database with agency statuses; if the next status is ongoing, it should be created", async () => {
    const appStatus: SgwApplication[] = [
      {
        refId: "10",
        status: "3",
        appliedDateTime: new Date("2024-07-10"),
        updatedDateTime: new Date("2024-07-10"),
      },
      sampleAppStatus,
    ];
    const dbStatus: Application[] = [];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).toHaveBeenCalledWith(appStatus[0], userUuid, schemeCode);
    expect(updateApplicationSpy).toHaveBeenCalledWith(appStatus[1], userUuid, schemeCode);
    expect(deleteApplicationSpy).not.toHaveBeenCalled();
    expect(getAppFromDbSpy).toHaveBeenCalled();
  });

  it("should update the database with agency statuses; if the next status is terminal, it should not be updated", async () => {
    const appStatus: SgwApplication[] = [
      {
        refId: "10",
        status: "3",
        appliedDateTime: new Date("2024-07-10"),
        updatedDateTime: new Date("2024-07-10"),
      },
      {
        refId: "2",
        status: "3",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-02"),
      },
    ];
    const dbStatus: Application[] = [];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).toHaveBeenCalledWith(appStatus[0], userUuid, schemeCode);
    expect(updateApplicationSpy).not.toHaveBeenCalledWith(appStatus[1], userUuid, schemeCode);
    expect(deleteApplicationSpy).not.toHaveBeenCalled();
    expect(getAppFromDbSpy).toHaveBeenCalled();
  });

  it("should update the database when agency has the latest status, and the agency's status matches the non-latest status in the database and is ongoing", async () => {
    const appStatus: SgwApplication[] = [
      {
        refId: "2",
        status: "1",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-10"),
      },
    ];
    const dbStatus: Application[] = [
      {
        userUuid,
        schemeCode,
        refId: "10",
        statusCode: "0",
        appliedDateTime: new Date("2024-07-10"),
        updatedDateTime: new Date("2024-07-10"),
      },
      sampleDbStatus,
    ];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).toHaveBeenCalledWith(appStatus[0], userUuid, schemeCode);
    expect(deleteApplicationSpy).not.toHaveBeenCalled();
    expect(getAppFromDbSpy).toHaveBeenCalled();
  });

  it("should not update when the database has the latest status, and the agency's status matches the non-latest status in the database but is not newer", async () => {
    const appStatus: SgwApplication[] = [
      {
        refId: "2",
        status: "3",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-02"),
      },
    ];
    const dbStatus: Application[] = [
      {
        userUuid,
        schemeCode,
        refId: "10",
        statusCode: "0",
        appliedDateTime: new Date("2024-07-10"),
        updatedDateTime: new Date("2024-07-10"),
      },
      {
        userUuid,
        schemeCode,
        refId: "2",
        statusCode: "0",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-10"),
      },
    ];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).not.toHaveBeenCalled();
    expect(deleteApplicationSpy).not.toHaveBeenCalled();
    expect(getAppFromDbSpy).not.toHaveBeenCalled();
  });

  it("should not update when the database has the latest status, and the agency's status matches the non-latest status in the database but is the same", async () => {
    const appStatus: SgwApplication[] = [sampleAppStatus];
    const dbStatus: Application[] = [
      {
        userUuid,
        schemeCode,
        refId: "10",
        statusCode: "3",
        appliedDateTime: new Date("2024-07-10"),
        updatedDateTime: new Date("2024-07-10"),
      },
      sampleDbStatus,
    ];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).not.toHaveBeenCalled();
    expect(deleteApplicationSpy).not.toHaveBeenCalled();
    expect(getAppFromDbSpy).not.toHaveBeenCalled();
  });

  it("should update the latest agency status while deleting the matching terminal status from the database", async () => {
    const appStatus: SgwApplication[] = [
      {
        refId: "10",
        status: "0",
        appliedDateTime: new Date("2024-07-10"),
        updatedDateTime: new Date("2024-07-10"),
      },
      {
        refId: "2",
        status: "3",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-10"),
      },
    ];
    const dbStatus: Application[] = [sampleDbStatus];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).toHaveBeenCalledWith(appStatus[0], userUuid, schemeCode);
    expect(deleteApplicationSpy).toHaveBeenCalledWith(userUuid, dbStatus[0].refId);
    expect(getAppFromDbSpy).toHaveBeenCalled();
  });

  it("should update the latest agency status while deleting a non-latest terminal status from the database", async () => {
    const appStatus: SgwApplication[] = [
      {
        refId: "10",
        status: "0",
        appliedDateTime: new Date("2024-07-10"),
        updatedDateTime: new Date("2024-07-10"),
      },
      {
        refId: "2",
        status: "3",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-02"),
      },
    ];
    const dbStatus: Application[] = [
      {
        userUuid,
        schemeCode,
        refId: "2",
        statusCode: "4",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-10"),
      },
    ];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).toHaveBeenCalledWith(appStatus[0], userUuid, schemeCode);
    expect(deleteApplicationSpy).toHaveBeenCalledWith(userUuid, dbStatus[0].refId);
    expect(getAppFromDbSpy).toHaveBeenCalled();
  });

  it("should update the latest agency status while deleting a matching terminal status from the database", async () => {
    const appStatus: SgwApplication[] = [
      {
        refId: "10",
        status: "0",
        appliedDateTime: new Date("2024-07-10"),
        updatedDateTime: new Date("2024-07-10"),
      },
      {
        refId: "2",
        status: "3",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-02"),
      },
    ];
    const dbStatus: Application[] = [
      {
        userUuid,
        schemeCode,
        refId: "2",
        statusCode: "3",
        appliedDateTime: new Date("2024-07-02"),
        updatedDateTime: new Date("2024-07-02"),
      },
    ];

    await updateApplicationDatabase(schemeCode, userUuid, appStatus, dbStatus);

    expect(updateApplicationSpy).toHaveBeenCalledWith(appStatus[0], userUuid, schemeCode);
    expect(deleteApplicationSpy).toHaveBeenCalledWith(userUuid, dbStatus[0].refId);
    expect(getAppFromDbSpy).toHaveBeenCalled();
  });
});

describe("setMyInfoNric", () => {
  it("should update submission data with the provided NRIC", () => {
    const submissionData = {
      profile: {
        nric: "",
      },
    };

    const updatedData = serviceSgw.setMyInfoNric(smtaSchema, submissionData, "*********");
    // Here we expect that the NRIC is set at "profile.nric"
    expect(get(updatedData, "profile.nric")).toBe("*********");
  });
});

describe("getHashedFileNames", () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should return an empty array for smtaSchema (which has no file upload field)", () => {
    jest.spyOn(serviceSgw, "getAttachmentIds").mockReturnValue([]);

    const submissionData = {
      profile: {
        name: "John Doe",
        nric: "*********",
        highestEducation: "3",
      },
    };

    const result = serviceSgw.getHashedFileNames(smtaSchema, submissionData);
    expect(result).toEqual([]);
  });

  it("should return hashed file names for scfaSchema", () => {
    // For SCFA schema, we expect a file upload field for otherSCFASupportingDocuments.
    // Stub getAttachmentIds to return the path where file uploads are stored.
    jest.spyOn(serviceSgw, "getAttachmentIds").mockReturnValue(["profile.otherSCFASupportingDocuments"]);

    // Stub getHashedString to return a predictable hashed string.
    jest.spyOn(util, "getHashedString").mockImplementation((input: string) => `hashed(${input})`);

    // Define submissionData that includes an array of file objects at the expected path.
    const submissionData = {
      profile: {
        otherSCFASupportingDocuments: [
          { attachmentType: "profile.otherSCFASupportingDocuments", fileName: "doc1.pdf" },
          { attachmentType: "profile.otherSCFASupportingDocuments", fileName: "doc2.pdf" },
        ],
      },
    };

    const result = serviceSgw.getHashedFileNames(scfaSchema, submissionData);
    expect(result).toEqual([
      "hashed(profile.otherSCFASupportingDocuments/doc1.pdf)",
      "hashed(profile.otherSCFASupportingDocuments/doc2.pdf)",
    ]);
  });
});

describe("mergeUserData", () => {
  const schemes = ["scheme1", "scheme2", "scheme3"];

  const scheme1 = {
    personalInfo: {
      name: "John Doe",
      age: 30,
      contact: {
        phone: "************",
        email: "<EMAIL>",
      },
    },
    education: {
      highestLevel: "Bachelor",
      institutions: [
        { name: "University A", year: 2015 },
        { name: "University C", year: 2017 },
      ],
    },
    financialInfo: {
      income: 50000,
      assets: ["Savings", "Stocks"],
    },
  };

  const scheme2 = {
    personalInfo: {
      age: 31,
      contact: {
        phone: "************",
        address: "123 Main St",
      },
    },
    financialInfo: {
      income: 55000,
      liabilities: ["Mortgage"],
    },
    employmentStatus: "Employed",
  };

  const scheme3 = {
    personalInfo: {
      name: "Mary Jane",
      maritalStatus: "Married",
    },
    education: {
      institutions: [{ name: "University B", year: 2018 }],
    },
    financialInfo: {
      income: 60000,
      assets: ["Real Estate"],
    },
  };

  it("should return an empty object if userData is undefined", () => {
    const result = mergeUserData(schemes, undefined);
    expect(result).toEqual({});
  });

  it("should merge matching keys from userData into object", () => {
    const userData: any = {
      scheme1,
      scheme2,
    };

    const expectedMergedData = {
      personalInfo: {
        name: "John Doe",
        age: 31,
        contact: {
          email: "<EMAIL>",
          phone: "************",
          address: "123 Main St",
        },
      },
      education: {
        highestLevel: "Bachelor",
        institutions: [
          { name: "University A", year: 2015 },
          { name: "University C", year: 2017 },
        ],
      },
      financialInfo: {
        income: 55000,
        assets: ["Savings", "Stocks"],
        liabilities: ["Mortgage"],
      },
      employmentStatus: "Employed",
    };

    const result = mergeUserData(schemes, userData);
    expect(result).toEqual(expectedMergedData);
  });

  it("should handle array merging correctly with the customizer", () => {
    const userData: any = {
      scheme1,
      scheme3,
    };

    const expectedMergedData = {
      personalInfo: {
        name: "Mary Jane",
        age: 30,
        contact: {
          email: "<EMAIL>",
          phone: "************",
        },
        maritalStatus: "Married",
      },
      education: {
        highestLevel: "Bachelor",
        institutions: [{ name: "University B", year: 2018 }],
      },
      financialInfo: {
        income: 60000,
        assets: ["Real Estate"],
      },
    };

    const result = mergeUserData(schemes, userData);
    expect(result).toEqual(expectedMergedData);
  });

  it("should return an empty object if no matching schemes exist in userData", () => {
    const userData: any = {
      scheme99: {
        personalInfo: {
          name: "Peter Pan",
          age: 30,
        },
      },
    };

    const result = mergeUserData(schemes, userData);
    expect(result).toEqual({});
  });
});

describe("checkForeignerEligibility", () => {
  it("should return true for foreigners", () => {
    const result = serviceSgw.checkForeignerEligibility("A");
    expect(result).toBe(true);
  });

  it.each(["", "C", "P", "U", "N", undefined] as ResidentialStatusCode[])(
    "should return false for non-foreigners (%s)",
    async (resStatusCode) => {
      const result = serviceSgw.checkForeignerEligibility(resStatusCode);
      expect(result).toBe(false);
    },
  );
});

describe("checkChildrenEligibility", () => {
  let checkPwdEligibilitySpy: jest.SpyInstance;

  const mockScheme = {
    code: "pwdr",
    name: "PWD Registry",
    appStatusUrl: "http://localhost:3456/agency-status/pwdr",
    appSubmissionUrl: "http://localhost:3456/agency-submit/pwdr",
  };

  const mockChildrenBirthRecords: ChildrenBirthRecords = [
    {
      birthcertno: { value: "*********" },
      name: { value: "Child One" },
      classification: "C",
      lastupdated: "2018-06-01",
      source: "1",
    },
    {
      birthcertno: { value: "*********" },
      name: { value: "Child Two" },
      classification: "C",
      lastupdated: "2018-06-01",
      source: "1",
    },
  ];

  beforeEach(() => {
    checkPwdEligibilitySpy = jest.spyOn(serviceSgw, "checkPwdEligibility");
  });

  afterEach(() => jest.restoreAllMocks());

  it("should return an empty array if no children birth records are provided", async () => {
    const result = await serviceSgw.checkChildrenEligibility([], "*********", mockScheme);
    expect(result).toEqual([]);
  });

  it("should return an empty array if all children are eligible", async () => {
    checkPwdEligibilitySpy.mockResolvedValue(true);

    const result = await serviceSgw.checkChildrenEligibility(mockChildrenBirthRecords, "*********", mockScheme);
    expect(result).toEqual([]);
    expect(checkPwdEligibilitySpy).toHaveBeenCalledTimes(2);
  });

  it("should return an array of ineligible children", async () => {
    const checkPwdEligibilitySpy = jest
      .spyOn(serviceSgw, "checkPwdEligibility")
      .mockResolvedValueOnce(false)
      .mockResolvedValueOnce(true);

    const result = await serviceSgw.checkChildrenEligibility(mockChildrenBirthRecords, "*********", mockScheme);
    expect(result).toEqual([{ name: "Child One", nric: "*********" }]);
    expect(checkPwdEligibilitySpy).toHaveBeenCalledTimes(2);
  });
});

describe("getApplyInfo", () => {
  const mockScheme = {
    code: "pwdr",
    name: "PWD Registry",
  };

  const mockSgwSession: SgwSession = {
    nric: "*********",
    userId: "mockUserId",
    sessionId: "mockSessionId",
    myInfoCommon: {
      residentialstatus: { code: "C", classification: "C", lastupdated: "2018-06-01", source: "1" },
      childrenbirthrecords: [
        {
          birthcertno: { value: "*********" },
          name: { value: "Child One" },
          classification: "C",
          lastupdated: "2018-06-01",
          source: "1",
        },
        {
          birthcertno: { value: "*********" },
          name: { value: "Child Two" },
          classification: "C",
          lastupdated: "2018-06-01",
          source: "1",
        },
      ],
    },
  };

  let getActiveSchemeSpy: jest.SpyInstance;

  beforeEach(() => {
    getActiveSchemeSpy = jest.spyOn(schemeDao, "getActiveScheme").mockResolvedValue(mockScheme);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("scheme validation", () => {
    it("should return empty object for unsupported schemes", async () => {
      const result = await serviceSgw.getApplyInfo("smta", mockSgwSession, "allow");
      expect(result).toEqual({});
      expect(getActiveSchemeSpy).not.toHaveBeenCalled();
    });

    it.each(["disallow", "maintenance", "ineligible", "unavailable", "timeout"])(
      "should return empty object for when application state is not 'allow' (%s)",
      async (applicationState) => {
        const result = await serviceSgw.getApplyInfo("smta", mockSgwSession, applicationState as ApplicationState);
        expect(result).toEqual({});
        expect(getActiveSchemeSpy).not.toHaveBeenCalled();
      },
    );

    it("should throw error when scheme does not exist or is not active", async () => {
      getActiveSchemeSpy.mockResolvedValueOnce(undefined);

      await expect(serviceSgw.getApplyInfo("emps", mockSgwSession, "allow")).rejects.toThrow(
        "emps scheme does not exist or not active",
      );
    });
  });

  describe("evaluate applyInfo", () => {
    let checkForeignerEligibilitySpy: jest.SpyInstance;
    let checkPwdEligibilitySpy: jest.SpyInstance;
    let checkChildrenEligibilitySpy: jest.SpyInstance;

    const mockIneligibleChildren = [
      { name: "Child One", nric: "*********" },
      { name: "Child Two", nric: "*********" },
    ];

    beforeEach(() => {
      checkForeignerEligibilitySpy = jest.spyOn(serviceSgw, "checkForeignerEligibility");
      checkPwdEligibilitySpy = jest.spyOn(serviceSgw, "checkPwdEligibility");
      checkChildrenEligibilitySpy = jest.spyOn(serviceSgw, "checkChildrenEligibility");
    });

    afterEach(() => jest.restoreAllMocks());

    describe("applying to PWDR", () => {
      it("should set notEligiblePwdCitizen template for foreigners applying as a PWD", async () => {
        checkForeignerEligibilitySpy.mockReturnValue(true);
        checkPwdEligibilitySpy.mockResolvedValue(true);
        checkChildrenEligibilitySpy.mockResolvedValue([]);

        const result = await serviceSgw.getApplyInfo("pwdr", mockSgwSession, "allow");

        expect(result.pwd).toEqual({ template: "notEligiblePwdCitizen" });
        expect(checkPwdEligibilitySpy).not.toHaveBeenCalled();
      });

      it("should set notEligiblePwd template for ineligible applicant applying as a PWD", async () => {
        checkForeignerEligibilitySpy.mockReturnValue(false);
        checkPwdEligibilitySpy.mockResolvedValue(false);
        checkChildrenEligibilitySpy.mockResolvedValue([]);

        const result = await serviceSgw.getApplyInfo("pwdr", mockSgwSession, "allow");

        expect(result.pwd).toEqual({ template: "notEligiblePwd" });
      });

      it("should return no template for non-foreigner, eligible applicant applying as a PWD", async () => {
        checkForeignerEligibilitySpy.mockReturnValue(false);
        checkPwdEligibilitySpy.mockResolvedValue(true);
        checkChildrenEligibilitySpy.mockResolvedValue([]);

        const result = await serviceSgw.getApplyInfo("pwdr", mockSgwSession, "allow");

        expect(result.pwd).toBeUndefined();
      });

      it("should return no template if there is no ineligible children when applying as a parent", async () => {
        checkPwdEligibilitySpy.mockResolvedValue(true);
        checkChildrenEligibilitySpy.mockResolvedValue([]);

        const result = await serviceSgw.getApplyInfo("pwdr", mockSgwSession, "allow");

        expect(result.parent).toBeUndefined();
      });

      it("should return childrenNotEligible template if there are ineligible children when applying as a parent", async () => {
        checkChildrenEligibilitySpy.mockResolvedValue(mockIneligibleChildren);

        const result = await serviceSgw.getApplyInfo("pwdr", mockSgwSession, "allow");

        expect(result.parent).toEqual({
          template: "childrenNotEligible",
          data: {
            children: mockIneligibleChildren,
          },
        });
      });
    });

    describe("applying to EMPS/H2W", () => {
      it.each(["emps", "h2w"])(
        "should set notEligiblePwdCitizen template for foreigners applying as a PWD (%s)",
        async (schemeCode) => {
          checkForeignerEligibilitySpy.mockReturnValue(true);
          checkPwdEligibilitySpy.mockResolvedValue(true);
          checkChildrenEligibilitySpy.mockResolvedValue([]);

          const result = await serviceSgw.getApplyInfo(schemeCode, mockSgwSession, "allow");

          expect(result.pwd).toEqual({ template: "notEligiblePwdCitizen" });
          expect(checkPwdEligibilitySpy).not.toHaveBeenCalled();
        },
      );
      it.each(["emps", "h2w"])(
        "should set notVerifiedPwd template for 'pwdr eligible applicant' applying as a PWD (%s)",
        async (schemeCode) => {
          checkForeignerEligibilitySpy.mockReturnValue(false);
          checkPwdEligibilitySpy.mockResolvedValue(true);
          checkChildrenEligibilitySpy.mockResolvedValue([]);

          const result = await serviceSgw.getApplyInfo(schemeCode, mockSgwSession, "allow");

          expect(result.pwd).toEqual({ template: "notVerifiedPwd" });
        },
      );

      it.each(["emps", "h2w"])(
        "should set verifiedPwd template for 'pwdr ineligible applicant' applying as a PWD (%s)",
        async (schemeCode) => {
          checkForeignerEligibilitySpy.mockReturnValue(false);
          checkPwdEligibilitySpy.mockResolvedValue(false);
          checkChildrenEligibilitySpy.mockResolvedValue([]);

          const result = await serviceSgw.getApplyInfo(schemeCode, mockSgwSession, "allow");

          expect(result.pwd).toEqual({ template: "verifiedPwd" });
        },
      );

      it.each(["emps", "h2w"])(
        "should set noVerifiedPwdChildren template if there is no 'pwdr ineligible children' when applying as a parent (%s)",
        async (schemeCode) => {
          checkChildrenEligibilitySpy.mockResolvedValue([]);

          const result = await serviceSgw.getApplyInfo(schemeCode, mockSgwSession, "allow");

          expect(result.parent).toEqual({ template: "noVerifiedPwdChildren" });
        },
      );

      it.each(["emps", "h2w"])(
        "should set verifiedPwdChildren template if there are 'pwdr ineligible children' when applying as a parent (%s)",
        async (schemeCode) => {
          checkChildrenEligibilitySpy.mockResolvedValue(mockIneligibleChildren);

          const result = await serviceSgw.getApplyInfo(schemeCode, mockSgwSession, "allow");

          expect(result.parent).toEqual({
            template: "verifiedPwdChildren",
            data: {
              children: mockIneligibleChildren,
            },
          });
        },
      );
    });

    describe("applying to IHL", () => {
      it("should set notEligiblePwdCitizen template for foreigners applying as a PWD", async () => {
        checkForeignerEligibilitySpy.mockReturnValue(true);
        checkPwdEligibilitySpy.mockResolvedValue(true);

        const result = await serviceSgw.getApplyInfo("ihl", mockSgwSession, "allow");

        expect(result.pwd).toEqual({ template: "notEligiblePwdCitizen" });
        expect(checkPwdEligibilitySpy).not.toHaveBeenCalled();
        expect(checkChildrenEligibilitySpy).not.toHaveBeenCalled();
      });

      it("should return no template for non-foreigner, eligible applicant applying as a PWD", async () => {
        checkForeignerEligibilitySpy.mockReturnValue(false);
        checkPwdEligibilitySpy.mockResolvedValue(true);

        const result = await serviceSgw.getApplyInfo("pwdr", mockSgwSession, "allow");

        expect(result.pwd).toBeUndefined();
        expect(checkChildrenEligibilitySpy).not.toHaveBeenCalled();
      });

      it("should set notVerifiedPwd template for 'pwdr eligible applicant' applying as a PWD", async () => {
        checkForeignerEligibilitySpy.mockReturnValue(false);
        checkPwdEligibilitySpy.mockResolvedValue(true);

        const result = await serviceSgw.getApplyInfo("ihl", mockSgwSession, "allow");

        expect(result.pwd).toEqual({ template: "notVerifiedPwd" });
        expect(checkChildrenEligibilitySpy).not.toHaveBeenCalled();
      });

      it("should set verifiedPwd template for 'pwdr ineligible applicant' applying as a PWD", async () => {
        checkForeignerEligibilitySpy.mockReturnValue(false);
        checkPwdEligibilitySpy.mockResolvedValue(false);

        const result = await serviceSgw.getApplyInfo("ihl", mockSgwSession, "allow");

        expect(result.pwd).toEqual({ template: "verifiedPwd" });
        expect(checkChildrenEligibilitySpy).not.toHaveBeenCalled();
      });
    });
  });
});
describe("getSqServiceIdFromSchemeCode", () => {
  const SCHEME_CODE = "test-scheme";
  const SQ_SERVICE_ID = "service-id-123";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return sqServiceId when found", async () => {
    jest.spyOn(schemeDao, "getActiveSqServiceId").mockResolvedValueOnce({ sqServiceId: SQ_SERVICE_ID });
    const result = await serviceSgw.getSqServiceIdFromSchemeCode(SCHEME_CODE);
    expect(result).toBe(SQ_SERVICE_ID);
  });

  it("should return null when sqServiceId is not found", async () => {
    const result = await serviceSgw.getSqServiceIdFromSchemeCode("non-existent-scheme-id");
    expect(result).toBeNull();
  });
});

describe("getDelegateSsoJwtToken", () => {
  const mockSgwSession: SgwSession = {
    nric: "*********",
    userId: "mockUserId",
    sessionId: "mockSessionId",
  };
  const mockPrivateKey =
***********************************************************************************************************************************************************************************************************************************************************
  const mockAuthForwarderUrl = "https://www.test-auth-forwarder.com";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should throw error when signing fails", async () => {
    await expect(serviceSgw.getDelegateSsoJwtToken(mockSgwSession)).rejects.toThrow("Failed to generate JWT token");
  });

  it("should return a JWT token with the correct claims when signing succeeds", async () => {
    appConfig.sequential.SEQUENTIAL_SSO_SIG_PRIVATE_KEY = mockPrivateKey;
    appConfig.sequential.SEQUENTIAL_SSO_AUTH_FORWARDER_URL = mockAuthForwarderUrl;
    const result = await serviceSgw.getDelegateSsoJwtToken(mockSgwSession);
    const decoded = decodeJwt(result);

    expect(decoded.iss).toStrictEqual("sgw-schemes");
    expect(decoded.aud).toStrictEqual(mockAuthForwarderUrl);
    expect(decoded.exp! - decoded.iat!).toStrictEqual(180);
    expect(decoded.sub).toStrictEqual(mockSgwSession.userId);
  });
});

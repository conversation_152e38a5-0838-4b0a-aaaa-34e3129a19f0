import DayJS from "dayjs";
import { escape } from "lodash";

import { Application } from "@sgw/services";

import { SgwApplication } from "./service";

// "unavailable" is only used to represents SAF state when it's unable to be applied
export type ApplicationState = "allow" | "disallow" | "maintenance" | "ineligible" | "unavailable" | "timeout";

export interface ConsentRequired {
  refId: string;
  isRequired: boolean;
}

export interface SgwAppState {
  schemeCode: string;
  applicationState: ApplicationState;
}

export type SgwAppStateResponse = SgwAppState[];

export interface SgwAppStatusResponse {
  schemeCode: string;
  schemeName?: string;
  status: SgwAppStatus[];
  agencyUnavailable: boolean;
  applicationState: ApplicationState;
  consentsRequired: ConsentRequired[];
  maintenanceMsg?: string;
}

export interface SgwAppStatus {
  refId: string;
  statusCode: string;
  status: string;
  appliedDateTime?: string;
  updatedDateTime?: string;
  remarks?: string;
  detail?: SgwAppStatusDetail[];
  deadline?: string;
  documentsType?: string;
  schemeCode: string;
  schemeName?: string;
  appliedForNames?: string[];
}

export interface SgwAppStatusDetail {
  title: string;
  category: string;
  assistance?: string;
  status?: string;
  description?: string;
  period?: string;
  paymentMode?: string;
  disbursement?: string;
  remarks?: string;
}

export const SGW_STATUS = {
  "0": "Submitted",
  "1": "Received",
  "2": "Processing",
  "3": "Approved",
  "4": "Assistance Received",
  "5": "Rejected",
  "6": "Withdrawn",
  "7": "Completed",
  "8": "Ongoing",
  "9": "Suspended",
  "10": "Payout Failed",
  "11": "Assistance Refunded",
  "20": "Pending Documents",
  "21": "Documents Submitted",
} as const;
export type SGW_STATUS_VALUE = (typeof SGW_STATUS)[keyof typeof SGW_STATUS];
export const SGW_STATUS_CODE = Object.keys(SGW_STATUS) as [keyof typeof SGW_STATUS];

export const TERMINAL_STATUS: SGW_STATUS_VALUE[] = [
  "Approved",
  "Assistance Received",
  "Rejected",
  "Withdrawn",
  "Completed",
  "Ongoing",
  "Suspended",
];

const SGW_CATEGORY = {
  "0": "Financial",
  "1": "Employment",
  "2": "Beneficiary",
  "3": "Medical",
  "4": "Housing",
  "5": "Education",
} as const;
export const SGW_CATEGORY_CODE = Object.keys(SGW_CATEGORY) as [keyof typeof SGW_CATEGORY];

export const getApplicationDisplayDate = (originalDate: string | Date): string => {
  return DayJS(originalDate).isValid() ? DayJS(originalDate).format("D MMM YYYY") : "";
};

export const getApplicationDisplayDateWithTime = (originalDate: string | Date): string => {
  return DayJS(originalDate).isValid() ? DayJS(originalDate).format("D MMM YYYY, hh:mm a") : "";
};

export const apiToSgwAppStatus = (schemeCode: string, application: SgwApplication): SgwAppStatus => {
  let detail: SgwAppStatusDetail[] | undefined;
  if (application.detail) {
    detail = application.detail.map((content) => ({
      ...content,
      category: SGW_CATEGORY[content.category],
      status: content.status && SGW_STATUS[content.status],
    }));
  }
  return {
    schemeCode,
    refId: application.refId,
    statusCode: application.status,
    status: SGW_STATUS[application.status],
    appliedDateTime: getApplicationDisplayDate(application.appliedDateTime),
    updatedDateTime: getApplicationDisplayDate(application.updatedDateTime),
    remarks: application.remarks,
    detail,
    deadline:
      application.status === "20" && application.outstandingItems?.deadline
        ? getApplicationDisplayDate(application.outstandingItems.deadline)
        : undefined,
    documentsType: application.outstandingItems?.id,
    appliedForNames: application.appliedForNames,
  };
};

export const dbToApiAppStatus = (dbAppStatus: Application): SgwApplication => ({
  refId: dbAppStatus.refId,
  status: dbAppStatus.statusCode as SgwApplication["status"],
  appliedDateTime: dbAppStatus.appliedDateTime,
  updatedDateTime: dbAppStatus.updatedDateTime,
  outstandingItems:
    dbAppStatus.documentsType && dbAppStatus.documentsDeadline
      ? {
          id: dbAppStatus.documentsType,
          deadline: dbAppStatus.documentsDeadline,
        }
      : undefined,
});

// flatten nested object or array into paths e.g. { "a.b.0": "val" }
export const flattenObjectToPaths = (obj: object, namespace: string): object => {
  let result = {};
  Object.entries(obj).forEach(([key, val]) => {
    if (typeof val === "object" && val) {
      result = { ...result, ...flattenObjectToPaths(val, `${namespace}${key}.`) };
    } else {
      result[`${namespace}${key}`] = val;
    }
  });
  return result;
};

export const formatMaintenanceMessage = (
  maintenanceStartDate: string | Date,
  maintenanceEndDate?: string | Date,
): string | undefined => {
  const startDate = DayJS(maintenanceStartDate);
  const endDate = maintenanceEndDate ? DayJS(maintenanceEndDate) : undefined;

  // Check if start date is valid
  if (!startDate.isValid()) {
    return undefined;
  }

  const formattedStartDate = startDate.format("D MMM YYYY");
  const formattedStartTime = startDate.format("h:mm a");

  // Only start date is provided
  if (!endDate) {
    return `This scheme provider will be undergoing a scheduled maintenance on **${formattedStartDate}** from **${formattedStartTime}**.`;
  }

  // Both start and end dates are provided
  if (endDate.isValid()) {
    const formattedEndDate = endDate.format("D MMM YYYY");
    const formattedEndTime = endDate.format("h:mm a");

    const sameDay = startDate.isSame(endDate, "day");
    return sameDay
      ? `This scheme provider will be undergoing a scheduled maintenance on **${formattedStartDate}** from **${formattedStartTime}** to **${formattedEndTime}**.`
      : `This scheme provider will be undergoing a scheduled maintenance from **${formattedStartDate}, ${formattedStartTime}** to **${formattedEndDate}, ${formattedEndTime}**.`;
  }

  return undefined;
};

export const convertMarkdownLinksToHtml = (text: string): string => {
  if (!text) return "";
  // Regex to match markdown links: [link text](https://example.com)
  const markdownLinkRegex = /\[([^\]]+)]\((https?:\/\/[^\)]+)\)/g;
  const nbspRegex = /&nbsp;/g; // \n is not detected in the email so only the nbsp regex is used

  return text
    .replace(markdownLinkRegex, (_match, linkText, url) => {
      // Since our regex only accepts http(s), the URL should be safe.
      return `<a href="${encodeURI(url)}" target="_blank" rel="noopener noreferrer">${escape(linkText)}</a>`;
    })
    .replace(nbspRegex, "<br><br>"); // this is used to replace \n&nbsp;\n with a new line in rendered emails
};

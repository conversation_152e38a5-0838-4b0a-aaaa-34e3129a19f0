import z from "zod";

import {
  checkBoxValidator,
  customDropdownValidator,
  customRadioValidator,
  dateOfBirthValidator,
  educationValidator,
  emailValidator,
  hiddenValidator,
  homeNumberValidator,
  logger,
  mobileNumberValidator,
  nameValidator,
  nricValidator,
  occupationValidator,
  residentialStatusValidator,
} from "@sgw/common";
import {
  EMPLOYMENT_STATUS,
  IS_SAF_FEATURE_ENABLED,
  RELATIONSHIP_TYPE,
  RELATIONSHIP_TYPE_SAF,
  SITUATION_TYPE,
} from "./constant";

interface BankDetails {
  bankCode: string;
  bankBranch: string;
  bankAccountNumber: string;
  isJointAllAccount?: string;
}

export const validateAndMap = (schemeCode: string, userData: any) => {
  switch (schemeCode) {
    case "clpp1":
      return { gsg: gsgValidateAndMap(userData) };
    case "clpp3":
      return { clpp3: clpp3ValidateAndMap(userData) };
    case "smta":
      return userData ? { [IS_SAF_FEATURE_ENABLED ? "smta" : "ssnet"]: smtaValidateAndMap(userData) } : {};
    default:
      return {};
  }
};

const gsgValidator = z.object({
  child: z
    .object({
      clppUuid: hiddenValidator(),
      name: nameValidator(),
      id: nricValidator(),
      residentialStatus: residentialStatusValidator(),
      dob: dateOfBirthValidator(),
      paymentMode: customDropdownValidator(["Bank Transfer", "CDA Top Up"]),
      cdaJwt: hiddenValidator(1024, true),
      bankDetails: z
        .object({
          bankCode: z.string().trim().min(1),
          bankBranch: z.string().trim().min(1),
          bankAccountNumber: z.string().trim().min(1),
          isJointAllAccount: customRadioValidator(["Yes", "No"]),
        })
        .optional(),
      trusteeDetails: z
        .object({
          name: nameValidator(),
          nric: nricValidator(),
        })
        .optional(),
    })
    .refine(
      (data) => {
        // GSG will only send cda jwt if payment mode is "CDA Top Up"
        if (data.paymentMode === "1") {
          const result = hiddenValidator(1024).safeParse(data.cdaJwt);
          return result.success;
        }
        return true;
      },
      {
        message: "CDA JWT is required and must be valid when payment mode is 'CDA Top Up'",
        path: ["cdaJwt"],
      },
    )
    .array()
    .min(1),
});

const ssnetValidator = z.object({
  highestEducation: educationValidator(true).catch(""), // Return empty string to not prefill if there's error. SSNet might respond with "X" which is a valid education code for "Not Reported" in SG-DRM v5 but SGW does not accept this value.
  bankOwnerType: customRadioValidator(["Mine", "My household member", "Someone else"]),
  bankOwnerName: nameValidator(true),
  bankOwnerNric: nricValidator(true),
  bankOwnerMobile: mobileNumberValidator(true),
  bankOwnerRelationshipType: customDropdownValidator({ RT31: "Family Member", RT32: "Non-Family Member" }, true).catch(
    "",
  ), // Return empty string to not prefill if there's error. SSNet might return code value that's part of their code table but not part of this dropdown value.
  bankDetails: z
    .object({
      bankCode: z.string().optional(),
      bankBranch: z.string().optional(),
      bankAccountNumber: z.string().optional(),
      isJointAllAccount: customRadioValidator(["Yes", "No"], true),
      jointAllName: nameValidator(true),
      jointAllNric: nricValidator(true),
    })
    .optional(),
  homeNumber: homeNumberValidator(true),
  contactType: customRadioValidator(["Contact me", "Contact someone else"], true),
  proxyName: nameValidator(true),
  proxyMobileNumber: mobileNumberValidator(true),
  proxyHomeNumber: homeNumberValidator(true),
  proxyEmail: emailValidator(true),
  proxyRelationshipType: customDropdownValidator(RELATIONSHIP_TYPE, true),
  otherName: nameValidator(true),
  otherMobileNumber: mobileNumberValidator(true),
  otherHomeNumber: homeNumberValidator(true),
  otherEmail: emailValidator(true),
  otherRelationshipType: customDropdownValidator(RELATIONSHIP_TYPE, true),
  otherContact: customRadioValidator(["Yes", "No"], true),
  employmentStatus: customRadioValidator(EMPLOYMENT_STATUS, true),
  jobDetails: z
    .object({
      employmentType: customRadioValidator(["Full-time", "Part-time"], true),
      companyName: nameValidator(true),
      occupation: occupationValidator(true),
    })
    .array()
    .optional(),
  grossMonthlyIncome: z.string().optional(),
  situationType: checkBoxValidator(SITUATION_TYPE, true),
  othersReason: z.string().optional(),
  cohabitationStatus: customRadioValidator(["Yes", "No"], true),
  familyMemberDetails: z
    .object({
      name: nameValidator(true),
      nric: nricValidator(true),
      email: emailValidator(true),
      employmentStatus: customRadioValidator(EMPLOYMENT_STATUS, true),
      grossMonthlyIncome: z.string().optional(),
      situationType: checkBoxValidator(SITUATION_TYPE, true),
      othersReason: z.string().optional(),
      relationshipType: customDropdownValidator(RELATIONSHIP_TYPE, true),
    })
    .array()
    .optional(),
});

const smtaValidator = z.object({
  highestEducation: educationValidator(true).catch(""), // Return empty string to not prefill if there's error. SSNet might respond with "X" which is a valid education code for "Not Reported" in SG-DRM v5 but SGW does not accept this value.
  bankOwnerType: customRadioValidator(["Mine", "My household member", "Someone else"]),
  bankOwnerName: nameValidator(true),
  bankOwnerNric: nricValidator(true),
  bankOwnerMobile: mobileNumberValidator(true),
  bankOwnerRelationshipType: customDropdownValidator({ RT31: "Family Member", RT32: "Non-Family Member" }, true).catch(
    "",
  ), // Return empty string to not prefill if there's error. SSNet might return code value that's part of their code table but not part of this dropdown value.
  bankDetails: z
    .object({
      bankCode: z.string().optional(),
      bankBranch: z.string().optional(),
      bankAccountNumber: z.string().optional(),
      isJointAllAccount: customRadioValidator(["Yes", "No"], true),
      jointAllName: nameValidator(true),
      jointAllNric: nricValidator(true),
    })
    .optional(),
  homeNumber: homeNumberValidator(true),
  cohabitationStatus: customRadioValidator(["Yes", "No"], true),
  familyMemberDetails: z
    .object({
      name: nameValidator(true),
      nric: nricValidator(true),
      email: emailValidator(true),
      relationshipType: customDropdownValidator(RELATIONSHIP_TYPE_SAF, true),
    })
    .array()
    .optional()
    .transform((members) =>
      members
        ? members.map((member) => ({
            family: {
              optionType: "manual",
              memberRecord: "manual",
              memberName: member.name,
              memberIdNumber: member.nric,
            },
            email: member.email,
            relationshipType: member.relationshipType,
          }))
        : undefined,
    ),
});

const clpp3Validator = z.object({
  clppUuid: hiddenValidator(),
});

// Officially, both DBS and POSB have the same bank code "7171". At SGW, we use bank codes "DBS7171" and "POSB7171" for DBS and POSB respectively.
// This function determines whether "7171" is DBS or POSB based on bank branch code and bank account number.
const getDBSOrPOSBCode = (bankBranch: string, bankAccountNumber: string) => {
  if (bankBranch === "081" && bankAccountNumber.length === 9) {
    return "POSB7171";
  }
  return "DBS7171";
};
// OCBC bank account numbers from GSG comes without the branch code, resulting in 7 or 9 characters.
// Merging the branch code with the account number to ensure it passes validation (10 or 12 characters) and accurately reflects the user's account number.
const getOCBCBankAccountNumber = (bankBranch: string, bankAccountNumber: string) => {
  // TODO: OCBC: Remove the condition after the temporary patch is reverted on GSG side post-deployment. The function will return the concatenated bank branch and bank account number.
  if (bankAccountNumber.length === 7 || bankAccountNumber.length === 9) {
    return `${bankBranch}${bankAccountNumber}`;
  }

  return bankAccountNumber;
};

export const gsgValidateAndMap = (data: any) => {
  const gsg = gsgValidator.parse(data);

  const formatBankDetails = ({ bankCode, bankBranch, bankAccountNumber, isJointAllAccount }: BankDetails) => ({
    bankCode: bankCode === "7171" ? getDBSOrPOSBCode(bankBranch, bankAccountNumber) : bankCode,
    bankBranch,
    bankAccountNumber:
      bankCode === "7339" ? getOCBCBankAccountNumber(bankBranch, bankAccountNumber) : bankAccountNumber,
    jointAllAccount: { isJointAllAccount },
  });

  return {
    childDetails: gsg.child.map(({ bankDetails, trusteeDetails, ...rest }) => ({
      ...rest,
      ...(trusteeDetails && {
        trusteeName: trusteeDetails.name,
        trusteeNric: trusteeDetails.nric,
      }),
      ...(bankDetails != null && {
        bankDetails: formatBankDetails(bankDetails),
      }),
    })),
  };
};

export type MappedGsg = ReturnType<typeof gsgValidateAndMap>;

export const smtaValidateAndMap = (data: any) => {
  const result = IS_SAF_FEATURE_ENABLED ? smtaValidator.safeParse(data) : ssnetValidator.safeParse(data);

  if (!result.success) {
    logger.error("SMTA prefilling validation failed:", result.error.errors);
    return {};
  }

  const parsedData = result.data;
  let bankDetails = {};
  if (parsedData.bankDetails) {
    const {
      bankCode = "",
      bankBranch = "",
      bankAccountNumber = "",
      isJointAllAccount = "",
      jointAllName = "",
      jointAllNric = "",
    } = parsedData.bankDetails;

    const specificBankCode = bankCode === "7171" ? getDBSOrPOSBCode(bankBranch, bankAccountNumber) : bankCode;

    // Check if bankCode is "7339" and update finalBankAccountNumber
    const finalBankAccountNumber =
      bankCode === "7339" ? getOCBCBankAccountNumber(bankBranch, bankAccountNumber) : bankAccountNumber;

    bankDetails = {
      bankCode: specificBankCode,
      bankBranch,
      bankAccountNumber: finalBankAccountNumber,
      jointAllAccount: {
        isJointAllAccount,
        name: jointAllName,
        nric: jointAllNric,
      },
    };
  }

  return {
    ...parsedData,
    bankDetails,
  };
};
export type MappedSmta = ReturnType<typeof smtaValidateAndMap>;

export const clpp3ValidateAndMap = (data: any) => {
  return clpp3Validator.parse(data);
};
export type MappedClpp3 = ReturnType<typeof clpp3ValidateAndMap>;

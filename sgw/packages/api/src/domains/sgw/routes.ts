import Router from "@koa/router";
import { Context, DefaultState } from "koa";

import {
  getConsentStatus,
  getPrefill,
  getSchema,
  getStatus,
  submitApplication,
  submitConsent,
  submitOutstandingDocuments,
  getOptions,
  getSafStatus,
  getRshSiJwtToken,
  getSqAuthDetails,
} from "./controllers";

const sgwRoutes = new Router();
sgwRoutes.post("/:schemeCode/submit/:schemaId", submitApplication);
sgwRoutes.post("/:schemeCode/consent/:refId", submitConsent);
sgwRoutes.post("/:schemeCode/outstanding-documents/:refId", submitOutstandingDocuments);
sgwRoutes.get("/:schemeCode", getSchema);
sgwRoutes.get("/:schemeCode/consent/:refId", getConsentStatus);
sgwRoutes.get("/saf/status", getSafStatus);
sgwRoutes.get("/:schemeCode/status", getStatus);
sgwRoutes.get("/:schemeCode/prefill/:schemaId", getPrefill);
sgwRoutes.get("/:schemeCode/options/:refId", getOptions);
sgwRoutes.post("/:schemeCode/external/auth", getRshSiJwtToken);
sgwRoutes.get("/:schemeCode/sso/auth", getSqAuthDetails);

const router = new Router<DefaultState, Context>({ prefix: "/sgw" });
router.use(sgwRoutes.routes(), sgwRoutes.allowedMethods());

export { router };

/**
 * Reflects specific options from `smta-prod.json`, including:
 * - `RELATIONSHIP_TYPE`
 * - `EMPLOYMENT_STATUS`
 * - `SITUATION_TYPE`
 * 
 * Reflects specific options from `saf-prod.json` and `smta-saf-prod.json, including:
 * - `RELATIONSHIP_TYPE_SAF`
 * - `EMPLOYMENT_STATUS`
 * - `SITUATION_TYPE_SAF`
 *
 * Important: These constants must remain in sync with the corresponding schema definitions
 * Otherwise form prefilling may break
 */

export const RELATIONSHIP_TYPE = {
  RT6: "Spouse",
  RT2: "Child",
  RT1: "Parent",
  RT3: "Sibling",
  RT12: "Grandchild",
  RT11: "Grandparent",
  RT44: "Child-In-Law",
  RT43: "Parent-In-Law",
  RT45: "Sibling-In-Law",
  RT29: "In-Laws",
  RT42: "Significant Other",
  RT30: "Ward",
  RT4: "Guardian",
  RT33: "Step Child",
  RT34: "Step Parent",
  RT38: "Step Sibling",
  RT17: "Cousin",
  RT40: "Nephew / Niece",
  RT39: "Uncle / Auntie",
  RT36: "Adopted Child",
  RT35: "Adoptive Parent",
  RT37: "Adopted Sibling",
  RT31: "Other Family Member",
  RT32: "Other Non-Family Member",
  RT41: "Other",
};

export const RELATIONSHIP_TYPE_SAF = {
  REL001: "Spouse",
  REL101: "Husband",
  REL102: "Wife",
  REL004: "Child",
  REL401: "Son",
  REL402: "Daughter",
  REL002: "Parent",
  REL202: "Father",
  REL201: "Mother",
  REL006: "Sibling",
  REL601: "Brother",
  REL602: "Sister",
  REL007: "Grandchild",
  REL701: "Grandson",
  REL702: "Granddaughter",
  REL003: "Grandparent",
  REL301: "Grandfather",
  REL302: "Grandmother",
  REL505: "Son In-Law",
  REL506: "Daughter In-Law",
  REL501: "Father In-Law",
  REL502: "Mother In-Law",
  REL503: "Brother In-Law",
  REL504: "Sister In-Law",
  REL005: "In-Laws",
  REL806: "Significant Other",
  REL008: "Ward",
  REL009: "Guardian",
  REL405: "Step-Son",
  REL406: "Step-Daughter",
  REL205: "Step-Father",
  REL206: "Step-Mother",
  REL605: "Step-Brother",
  REL606: "Step-Sister",
  REL805: "Cousin",
  REL803: "Nephew",
  REL804: "Niece",
  REL801: "Uncle",
  REL802: "Aunt",
  REL403: "Adopted Son",
  REL404: "Adopted Daughter",
  REL603: "Adopted Brother",
  REL604: "Adopted Sister",
  REL203: "Adoptive Father",
  REL204: "Adoptive Mother",
  REL607: "Half-Brother",
  REL608: "Half-Sister",
  REL207: "Foster Father",
  REL208: "Foster Mother",
  REL407: "Foster Child",
  REL010: "Other Family Member",
  REL011: "Other Non-Family Member",
  REL999: "Data Not Available",
};

export const EMPLOYMENT_STATUS = {
  ES1: "Working",
  ES2: "Looking for work",
  ES3: "Not working",
};

export const SITUATION_TYPE = {
  UR1: "Retiree",
  UR2: "Homemaker",
  UR3: "Full-time caregiver",
  UR4: "Student",
  UR5: "Medically unfit for work",
  UR6: "Others",
};

// This list is ordered so that the first scheme is executed first. If there are common items, the next ones will override them.
export const SAF_SCHEMES = ["smta", "scfa"];

// SAF feature toggle
export const IS_SAF_FEATURE_ENABLED = false;
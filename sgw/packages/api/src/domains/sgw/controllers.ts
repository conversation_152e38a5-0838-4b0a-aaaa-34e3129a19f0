import Koa from "koa";

import { ApplySchema, generateApplicationSchema, logger } from "@sgw/common";
import { Consent, schemeDao } from "@sgw/services";

import { redactEmail, redactNric } from "../../util";
import { IS_SAF_FEATURE_ENABLED, SAF_SCHEMES } from "./constant";
import {
  getAppStatusResponse,
  getConsent,
  getDuplicateNricSections,
  getMyInfoIds,
  getApplicantDetailsForPendingConsent,
  getPrefillData,
  isConsentRequired,
  processOutstandingDocuments,
  getOutstandingItemsSchema,
  processSgwConsent,
  retrieveSchema,
  setMyInfoNric,
  getConsenterOptions,
  getAppByRefIdFromDb,
  postProcessBundledSubmission,
  checkInvalidBankBranch,
  duplicateAttachmentsForBundledSubmission,
  processSubmissions,
  extractSchemeDataFromBundledSubmission,
  SchemeDataMap,
  checkFiles,
  checkSignatureFiles,
  mergeUserData,
  hasScfaBeneficiary,
  getSectionTitlesFromIds,
  getApplications,
  getSqServiceIdFromSchemeCode,
  getDelegateSsoJwtToken,
  genRshSiJwtToken,
} from "./service";
import { processMsfOmnibusConsent } from "./service-pdf";
import { SgwAppStatusResponse } from "./util";
import { _validateAndParseSafCodes } from "../saf/controllers";
import { getCachedOrMergedSchema } from "../saf/service";

export const getSchema = async (ctx: Koa.Context): Promise<void> => {
  const schemeCode = ctx.params.schemeCode;

  if (!schemeCode) {
    logger.error("no schemeCode provided to get application schema");
    ctx.throw(400);
  }

  const schema = await retrieveSchema(schemeCode);
  if (!schema) {
    logger.error(`no active schema found for ${schemeCode}`);
    ctx.throw(400);
  }

  const myInfoIds = getMyInfoIds(schema.schema);
  if (schema.consentSchema) {
    // TODO: doesn't seem like the best way to indicate which fields can be cleared now that we have multiple schemas e.g. application, consent etc.
    myInfoIds.clearableMyInfoIds = [
      ...myInfoIds.clearableMyInfoIds,
      ...getMyInfoIds([schema.consentSchema]).clearableMyInfoIds,
    ];
  }

  ctx.body = { schema: { ...schema, ...myInfoIds } };
};

export const submitApplication = async (ctx: Koa.Context): Promise<void> => {
  const requestBody = ctx.request.body;
  const { schemeCode, schemaId } = ctx.params;
  const { nric, userId } = ctx.session;
  const step = "application";

  logger.info(`${schemeCode}: received application from ${redactNric(nric)}`, requestBody);

  // retrieve schema
  const appSchema = await retrieveSchema(schemeCode);
  const schema = appSchema?.schema.find((schema) => schema.id === schemaId);
  if (!appSchema || !schema) {
    logger.error(`${schemeCode}: no active schema found, id - ${schemaId}`);
    ctx.throw(400);
  }

  // validation for bank details
  const invalidBankBranchIds = checkInvalidBankBranch(schema, requestBody);

  if (invalidBankBranchIds.length) {
    const invalidSections = getSectionTitlesFromIds(invalidBankBranchIds, schema);
    const jointInvalidSections = invalidSections.join(", ");
    ctx.body = {
      message: `Invalid bank account number found. Please review these section(s): ${jointInvalidSections} and re-submit.`,
    };
    ctx.throw(400);
  }

  // validate submission data
  const validationSchema = generateApplicationSchema(schema);
  const validate = validationSchema.safeParse(requestBody);
  if (!validate.success) {
    logger.error(`${schemeCode}: application failed form validation`, validate.error);
    ctx.throw(400);
  }

  const hashedFileNames = await checkFiles(schema, appSchema, schemeCode, userId, step, requestBody);

  if (!hashedFileNames) {
    ctx.throw(400);
  }

  // check signatures exist
  logger.info("checking if all signatures in application exists");
  const allSignatureFilesExist = await checkSignatureFiles(schema, schemeCode, userId, step, requestBody);
  if (!allSignatureFilesExist) {
    logger.error(`${schemeCode}: some submitted signatures do not exist`);
    ctx.throw(400);
  }

  let schemeDataMap: SchemeDataMap;
  const isBundledSubmission = schemeCode === "saf";

  if (isBundledSubmission) {
    const schemeCodes = ["smta", "scfa"];
    schemeDataMap = await extractSchemeDataFromBundledSubmission(schemeCodes, schemaId, requestBody, nric);
  } else {
    const updatedSubmissionData = setMyInfoNric(schema, validate.data, nric);
    schemeDataMap = {
      [schemeCode]: { appSchema, applySchema: schema, submissionData: updatedSubmissionData, hashedFileNames },
    };
  }

  // check for
  // 1. duplicate nrics
  // 2. at least one household member must be a SCFA beneficiary
  for (const [schemeCode, { applySchema, submissionData }] of Object.entries(schemeDataMap)) {
    const duplicateSections = getDuplicateNricSections(applySchema, submissionData);

    if (duplicateSections.length) {
      const jointDuplicateSections = duplicateSections.join(", ");
      logger.warn(`${schemeCode}: application has duplicate nric in section(s): ${jointDuplicateSections}`);
      ctx.body = {
        message: `Duplicate NRICs/FINs found. Please review these section(s): ${jointDuplicateSections} and re-submit.`,
      };
      ctx.throw(400);
    }

    // SPECIAL HANDLING FOR SCFA
    if (IS_SAF_FEATURE_ENABLED && schemeCode === "scfa") {
      const hasBeneficiary = hasScfaBeneficiary(schemeCode, applySchema, submissionData);

      if (!hasBeneficiary) {
        logger.info(`${schemeCode}: at least one household member must be a SCFA beneficiary.`);
        ctx.body = {
          message:
            "At least one household member must be a SCFA beneficiary. Please review the Household section and re-submit.",
        };
        ctx.throw(400);
      }
    }
  }

  if (isBundledSubmission) {
    // duplicate attachements for each of the indivdual scheme in the bundled scheme
    await duplicateAttachmentsForBundledSubmission(userId, schemeCode, requestBody, schemeDataMap, step);
  }

  logger.info("submission data to be processed", validate.data);

  try {
    const { email, schemes } = await processSubmissions(schemeDataMap, requestBody, step, { nric, userId });
    ctx.body = { email, schemes: schemes };
  } catch {
    ctx.throw(500);
  }

  if (isBundledSubmission) {
    // post-process asynchronously
    postProcessBundledSubmission(schemeCode, userId, step);
  }
};

export const submitConsent = async (ctx: Koa.Context): Promise<void> => {
  const requestBody = ctx.request.body;
  const { schemeCode, refId } = ctx.params;
  logger.info(`received ${schemeCode} consent from ${redactNric(ctx.session.nric)}`, requestBody);

  const applicationSchema = await retrieveSchema(schemeCode);
  if (!applicationSchema?.consentSchema) {
    logger.error(`no active consent schema found for ${schemeCode}`);
    ctx.throw(400);
  }

  const validationSchema = generateApplicationSchema(applicationSchema.consentSchema);
  const validate = validationSchema.safeParse(requestBody);
  if (!validate.success) {
    logger.error(`${schemeCode} consent failed form validation`, validate.error);
    ctx.throw(400);
  }

  let pendingConsent: Consent | undefined;
  try {
    pendingConsent = await getConsent(ctx.session.userId, refId);
  } catch (error) {
    logger.error("error getting pending consent", error);
    ctx.throw(500);
  }

  if (!pendingConsent || pendingConsent.isProvided) {
    logger.error(`user has no pending ${schemeCode} consent`);
    ctx.throw(400);
  }

  try {
    logger.info("consent submission data to be processed", validate.data);

    const updatedSubmissionData = setMyInfoNric(applicationSchema.consentSchema, validate.data, ctx.session.nric);

    const email = await processSgwConsent(
      ctx.session.nric,
      ctx.session.userId,
      pendingConsent.userUuid,
      pendingConsent.refId,
      applicationSchema,
      updatedSubmissionData,
    );
    ctx.body = { email: email ? redactEmail(email) : "" };
  } catch {
    ctx.throw(500);
  }
};

export const getConsentStatus = async (ctx: Koa.Context): Promise<void> => {
  const { schemeCode, refId } = ctx.params;
  logger.info(`retrieving pending consent status for ${refId}`);

  const applicationSchema = await retrieveSchema(schemeCode);
  if (!applicationSchema?.consentSchema) {
    logger.error(`no active consent schema found for ${schemeCode}`);
    ctx.throw(400);
  }

  try {
    const applicantDetails = await getApplicantDetailsForPendingConsent(ctx.session.userId, refId);
    ctx.body = { applicantName: applicantDetails ? applicantDetails.name : "" };
  } catch {
    logger.error(`unable to retrieve pending consent status for ${refId}`);
    ctx.throw(500);
  }
};

export const getStatus = async (ctx: Koa.Context): Promise<void> => {
  const schemeCode = ctx.params.schemeCode;
  logger.info(`retrieving ${schemeCode} status`);

  try {
    const applications = await getApplications(schemeCode, ctx.session);

    logger.info(`${schemeCode}: retrieved applications`, applications);
    ctx.body = applications;
  } catch {
    logger.warn(`${schemeCode}: unable to retrieve status`);
    ctx.throw(500);
  }
};

export const getSafStatus = async (ctx: Koa.Context): Promise<void> => {
  logger.info("saf: retrieving application status");

  try {
    const statusPromises = SAF_SCHEMES.map(async (schemeCode) => {
      try {
        const status = await getAppStatusResponse(schemeCode, ctx.session);
        logger.info(`${schemeCode}: retrieved application status for saf`, status);
        return status;
      } catch (error) {
        logger.warn(`${schemeCode}: failed to retrieve application status for saf`, error);
        throw error;
      }
    });

    const results = await Promise.all(statusPromises);
    const safState = results.some((status) => status.applicationState !== "allow") ? "unavailable" : "allow";

    // the basic structure send to front-end to tells whether the user can apply for SAF.
    const result: SgwAppStatusResponse = {
      schemeCode: "saf",
      status: [],
      agencyUnavailable: false,
      applicationState: safState,
      consentsRequired: [],
    };

    logger.info("saf: retrieved application status", result);
    ctx.body = result;
  } catch (error) {
    logger.warn("saf: unable to retrieve application status", error);
    ctx.throw(500);
  }
};

export const getPrefill = async (ctx: Koa.Context): Promise<void> => {
  const { schemeCode, schemaId } = ctx.params;

  if (!schemaId) {
    logger.error("missing schema id");
    ctx.throw(400);
  }

  let applicationSchema;
  if (schemeCode === "saf") {
    const bundledSchemeCodes = _validateAndParseSafCodes(ctx);
    applicationSchema = await getCachedOrMergedSchema(bundledSchemeCodes);
  } else {
    applicationSchema = await retrieveSchema(schemeCode);
  }

  if (!applicationSchema) {
    logger.error(`no schema found for ${schemeCode}`);
    ctx.throw(400);
  }

  if (schemaId === "consent") {
    if (!applicationSchema?.consentSchema) {
      logger.error(`no consent schema found for ${schemeCode}`);
      ctx.throw(400);
    } else {
      ctx.body = getPrefillData(applicationSchema.consentSchema, ctx.session.userData);
      return;
    }
  }

  const appSchema = applicationSchema.schema.find((schema) => schema.id === schemaId);
  if (!appSchema) {
    logger.error(`no schema of id ${schemaId} found for ${schemeCode}`);
    ctx.throw(400);
  }

  if (schemeCode === "saf") {
    const safUserData = { saf: mergeUserData(SAF_SCHEMES, ctx.session.userData) };
    ctx.body = getPrefillData(appSchema, safUserData);
  } else {
    ctx.body = getPrefillData(appSchema, ctx.session.userData);
  }
};

export const getOptions = async (ctx: Koa.Context): Promise<void> => {
  const { schemeCode, refId } = ctx.params;

  if (!schemeCode || !refId) {
    logger.error("scheme code or ref id parameters is missing");
    ctx.throw(400);
  }

  const applicationSchema = await retrieveSchema(schemeCode);
  if (!applicationSchema) {
    logger.error(`no schema found for ${schemeCode}`);
    ctx.throw(400);
  }

  const consenterOptions = await getConsenterOptions(refId);
  ctx.body = { consenter: consenterOptions };
};

export const submitOutstandingDocuments = async (ctx: Koa.Context): Promise<void> => {
  const requestBody = ctx.request.body;
  const { schemeCode, refId } = ctx.params;
  logger.info(`received ${schemeCode} outstanding documents from ${redactNric(ctx.session.nric)}`, requestBody);

  const scheme = await schemeDao.getScheme(schemeCode);
  if (!scheme) {
    logger.error(`no scheme found at db for ${schemeCode}`);
    ctx.throw(400);
  }

  const applicationSchema = await retrieveSchema(schemeCode);
  if (!applicationSchema) {
    logger.error(`no schema found for ${schemeCode}`);
    ctx.throw(400);
  }

  const appStatus = await getAppByRefIdFromDb(ctx.session.userId, refId);
  if (!appStatus) {
    logger.error(`cannot find ${refId} to process the submission of outstanding documents`);
    ctx.throw(400);
  }

  const consentRequired = await isConsentRequired(appStatus.statusCode, appStatus.refId);

  let documentsSchema: ApplySchema | undefined;
  if (appStatus.statusCode === "20") {
    if (!applicationSchema.outstandingDocumentsSchema) {
      logger.error(`no outstanding documents schema found for ${schemeCode}`);
      ctx.throw(400);
    }

    if (!appStatus.documentsType) {
      logger.error(`no documents type for processing ${schemeCode} outstanding documents`);
      ctx.throw(400);
    }

    documentsSchema = getOutstandingItemsSchema(applicationSchema.outstandingDocumentsSchema, appStatus.documentsType);
  } else if (consentRequired) {
    if (!applicationSchema.consentDocumentsSchema) {
      logger.error(`no consent documents schema found for ${schemeCode}`);
      ctx.throw(400);
    }

    documentsSchema = applicationSchema.consentDocumentsSchema;
  } else {
    logger.error(
      `submitting ${schemeCode} outstanding documents when consent is not required or status is not Pending Documents`,
    );
    ctx.throw(400);
  }

  if (!documentsSchema) {
    logger.error(`error processing ${schemeCode} outstanding documents`);
    ctx.throw(400);
  }

  const validationSchema = generateApplicationSchema(documentsSchema);
  const validate = validationSchema.safeParse(requestBody);
  if (!validate.success) {
    logger.error(`${schemeCode} outstanding documents failed form validation`, validate.error);
    ctx.throw(400);
  }

  const duplicateSections = getDuplicateNricSections(documentsSchema, validate.data);
  if (duplicateSections.length > 0) {
    ctx.body = {
      message: "Duplicate NRICs/FINs in the form. Please correct and re-submit.",
    };
    ctx.throw(400);
  }

  logger.info("checking if all files in outstanding documents exists");
  const hashedFileNames = await checkFiles(
    documentsSchema,
    applicationSchema,
    schemeCode,
    ctx.session.userId,
    "outstanding-documents",
    validate.data,
  );
  if (!hashedFileNames) {
    logger.error(`some submitted file names for ${schemeCode} do not exist`);
    ctx.throw(400);
  }

  logger.info("checking if all signatures in outstanding documents exists");
  const allSignatureFilesExist = await checkSignatureFiles(
    documentsSchema,
    schemeCode,
    ctx.session.userId,
    "outstanding-documents",
    requestBody,
  );
  if (!allSignatureFilesExist) {
    logger.error(`some submitted signatures for ${schemeCode} do not exist`);
    ctx.throw(400);
  }

  const userAttachmentFolder = `${ctx.session.userId}/${schemeCode}/outstanding-documents`;
  const omnibusConsentHashedFileNames = await processMsfOmnibusConsent(
    documentsSchema,
    requestBody,
    ctx.session,
    userAttachmentFolder,
  );
  hashedFileNames.push(...omnibusConsentHashedFileNames);

  try {
    const email = await processOutstandingDocuments(
      ctx.session,
      scheme,
      applicationSchema,
      documentsSchema,
      appStatus,
      validate.data,
      hashedFileNames,
    );

    ctx.body = { email: email ? redactEmail(email) : "" };
  } catch {
    ctx.throw(500);
  }
};

/**
 * Sign a new JWT token for Sequential's custom backend authentication for handling sensitive file submissions.
 * This endpoint will be used by sgw frontend (SGW FE -> SQ's BE),
 * while the `genJwtToken` method will be used by appgen's form submit endpoint (AppGen -> SQ's BE).
 */
export const getRshSiJwtToken = async (ctx: Koa.Context) => {
  const { schemeCode } = ctx.params;
  try {
    logger.info(`${schemeCode}: Generating jwt token for user`);

    const requestBody = ctx.request.body;

    // Validate request
    if (!schemeCode) {
      logger.error(`missing schemeCode in path param`);
      ctx.throw(400);
    }

    // Validate if scheme is allowed schema
    const appSchema = await retrieveSchema(schemeCode);
    if (!appSchema) {
      logger.error(`${schemeCode}: no active schema found for the scheme`);
      ctx.throw(400);
    }
    if (!appSchema.fileUploadExternal) {
      logger.error(`${schemeCode}: external file uploads are not allowed for the scheme`);
      ctx.throw(400);
    }

    const token = await genRshSiJwtToken(appSchema.fileUploadExternal.url, requestBody.data);

    logger.info(`${schemeCode}: Generated jwt token for user`);

    ctx.body = { token };
  } catch (error) {
    logger.error(`${schemeCode}: Error generating jwt token for user`, error);
    ctx.throw(500);
  }
};

/**
 * Get Sequential's Service ID corresponding to schemeCode and a new JWT token for Sequential's SSO delegation.
 */
export const getSqAuthDetails = async (ctx: Koa.Context) => {
  const schemeCode = ctx.params.schemeCode;
  logger.info(`${schemeCode}: Getting sequential auth details`);

  // Validate if schemeCode is present
  if (!schemeCode) {
    logger.error(`missing schemeCode in path param`);
    ctx.throw(400);
  }

  // Map schemeCode to sqServiceId
  const sqServiceId = await getSqServiceIdFromSchemeCode(schemeCode);

  if (!sqServiceId) {
    logger.error(`${schemeCode}: No sqServiceId found`);
    ctx.throw(404);
  }

  try {
    // Generate token
    const token = await getDelegateSsoJwtToken(ctx.session);

    logger.info(`${schemeCode}: Finished getting sequential auth details`);

    ctx.body = { token, sqServiceId };
  } catch (error) {
    logger.error(`${schemeCode}: Error getting sequential auth details`, error);
    ctx.throw(500);
  }
};

import {
  ApplicationSchema,
  BlankGroupSchema,
  DecoratorSubType,
  GroupConditionalSchema,
  SectionConditionalSchema,
  SectionMemberSubType,
  SectionSchema,
} from "@sgw/common";

// --- Helper Type Aliases ---
type ProcessingNode<T> = T & { __usedIn: string[] };
interface FieldNodeWrapper {
  safId?: string;
  safIdx: number;
  __usedIn: string[];
  __node: ProcessingNode<SectionMemberSubType>;
}
type ProcessingSection = Omit<SectionSchema, "member" | "safIdx"> & {
  safIdx: number;
  __usedIn: string[];
  __fieldNodes: Map<string, FieldNodeWrapper>;
  member: SectionMemberSubType[];
};
interface ConditionalResultItem {
  choice: string[];
  member: SectionMemberSubType[];
}
interface SortableBySafIdx {
  safIdx?: string | number;
}

/**
 * Merges multiple hierarchical schemas into a single, unified schema.
 * @param schemas The array of application schemas to process.
 * @returns The final merged array of sections.
 */
export const mergeSchemas = (schemas: ApplicationSchema[]): SectionSchema[] => {
  // --- State Initialization ---
  const schemaMap = schemas.reduce((acc, scheme) => {
    // Assumes SAF schemes will only support one schema per scheme
    if (scheme.schema && scheme.schema[0] && scheme.schema[0].section) {
      acc[scheme.schemeCode] = scheme.schema[0].section;
    }
    return acc;
  }, {} as Record<string, SectionSchema[]>);

  if (!schemaMap || typeof schemaMap !== "object" || Object.keys(schemaMap).length === 0) {
    throw new Error("mergeSchemas requires a valid schemaMap object.");
  }
  const clonedSchemaMap = structuredClone(schemaMap);
  const mergedSectionMap = new Map<string, ProcessingSection>();
  let uniqueNodeCounter = 0;

  // =======================================================================
  // UTILITY & HELPER FUNCTIONS
  // =======================================================================

  const isConditionalSchema = (node: unknown): node is SectionConditionalSchema | GroupConditionalSchema => {
    if (!node || typeof node !== "object") return false;
    return "type" in node && (node.type === "SECTION_CONDITIONAL" || node.type === "GROUP_CONDITIONAL");
  };

  const sortArrayBySafIdx = (arr: SortableBySafIdx[]): void => {
    if (!Array.isArray(arr)) return;
    arr.sort((a, b) => {
      const idxA = parseInt(String(a.safIdx ?? ""), 10) || Number.MAX_SAFE_INTEGER;
      const idxB = parseInt(String(b.safIdx ?? ""), 10) || Number.MAX_SAFE_INTEGER;
      return idxA - idxB;
    });
  };

  const traverse = (node: object, visitor: (node: object) => void): void => {
    if (!node || typeof node !== "object") return;
    visitor(node);
    if ("member" in node && Array.isArray(node.member)) {
      node.member.forEach((child) => child && typeof child === "object" && traverse(child, visitor));
    }
    if ("group" in node && node.group && typeof node.group === "object") {
      traverse(node.group, visitor);
    }
    if ("result" in node && Array.isArray(node.result)) {
      node.result.forEach((r) => r && typeof r === "object" && traverse(r, visitor));
    }
  };

  const tagNodeWithUsage = <T extends SectionMemberSubType>(node: T, schemaName: string): ProcessingNode<T> => {
    const copy = structuredClone(node);
    traverse(copy, (currentNode) => {
      const internalNode = currentNode as { __usedIn?: string[] };
      if (!internalNode.__usedIn) internalNode.__usedIn = [];
      if (!internalNode.__usedIn.includes(schemaName)) internalNode.__usedIn.push(schemaName);
    });
    return copy as ProcessingNode<T>;
  };

  const mergeConditionalResults = (resultsA: ConditionalResultItem[], resultsB: ConditionalResultItem[]): void => {
    const choiceMap = new Map<string, SectionMemberSubType[]>();
    const processResults = (results: ConditionalResultItem[]) => {
      for (const res of results) {
        const key = JSON.stringify(res.choice || []);
        if (!choiceMap.has(key)) choiceMap.set(key, []);
        choiceMap.get(key)!.push(...(res.member || []));
      }
    };
    processResults(resultsA);
    processResults(resultsB);
    resultsA.length = 0;
    for (const [key, members] of choiceMap.entries()) {
      resultsA.push({ choice: JSON.parse(key), member: members });
    }
  };

  interface WithPrefill {
    prefillSource?: string;
    editable?: boolean;
    selector?: {
      prefillSource?: string;
      editable?: boolean;
    };
  }

  const mergeNodes = (
    nodeA: ProcessingNode<SectionMemberSubType> & WithPrefill,
    nodeB: ProcessingNode<SectionMemberSubType> & WithPrefill,
  ): ProcessingNode<SectionMemberSubType> & WithPrefill => {
    nodeA.__usedIn = Array.from(new Set([...nodeA.__usedIn, ...nodeB.__usedIn]));

    // Field-level: copy prefillSource (+ editable) from B iff A has none
    const aHasPrefill = typeof nodeA.prefillSource === "string" && nodeA.prefillSource.trim() !== "";
    const bHasPrefill = typeof nodeB.prefillSource === "string" && nodeB.prefillSource.trim() !== "";

    if (!aHasPrefill && bHasPrefill) {
      nodeA.prefillSource = nodeB.prefillSource;
      if (nodeB.editable !== undefined) {
        nodeA.editable = nodeB.editable;
      }
    }

    // Conditional selector: same add-only rule
    if (isConditionalSchema(nodeA) && isConditionalSchema(nodeB)) {
      const selA = nodeA.selector ?? {};
      const selB = nodeB.selector;
      if (selB && !selA.prefillSource && selB.prefillSource) {
        selA.prefillSource = selB.prefillSource;
        if (selB.editable !== undefined) {
          selA.editable = selB.editable;
        }
        nodeA.selector = selA;
      }
    }

    if (nodeA.type === "MULTI_VALUE" && nodeB.type === "MULTI_VALUE" && nodeA.group && nodeB.group) {
      nodeA.group = mergeNodes(
        nodeA.group as ProcessingNode<SectionMemberSubType> & WithPrefill,
        nodeB.group as ProcessingNode<SectionMemberSubType> & WithPrefill,
      ) as BlankGroupSchema & WithPrefill;
    } else if (
      isConditionalSchema(nodeA) &&
      isConditionalSchema(nodeB) &&
      Array.isArray(nodeA.result) &&
      Array.isArray(nodeB.result)
    ) {
      mergeConditionalResults(nodeA.result, nodeB.result);
    } else if ("member" in nodeA && Array.isArray(nodeA.member) && "member" in nodeB && Array.isArray(nodeB.member)) {
      nodeA.member.push(...nodeB.member);
    }
    return nodeA;
  };

  const unifyMemberArray = (
    members: ProcessingNode<SectionMemberSubType>[],
  ): ProcessingNode<SectionMemberSubType>[] => {
    const bySafId = new Map<string, ProcessingNode<SectionMemberSubType>[]>();
    const noSafIdBySchema = new Map<string, ProcessingNode<SectionMemberSubType>[]>();
    for (const item of members) {
      if (item.safId) {
        if (!bySafId.has(item.safId)) bySafId.set(item.safId, []);
        bySafId.get(item.safId)!.push(item);
      } else if (item.__usedIn?.length > 0) {
        const schemaName = item.__usedIn[0];
        if (!noSafIdBySchema.has(schemaName)) noSafIdBySchema.set(schemaName, []);
        noSafIdBySchema.get(schemaName)!.push(item);
      }
    }
    const mergedItems = Array.from(bySafId.values()).map((items) => {
      let baseNode = items[0];
      for (let i = 1; i < items.length; i++) {
        baseNode = mergeNodes(baseNode, items[i]);
      }
      return baseNode;
    });
    const groupedNoSafIdItems = Array.from(noSafIdBySchema.keys())
      .sort()
      .flatMap((schemaName) => noSafIdBySchema.get(schemaName)!);
    return [...mergedItems, ...groupedNoSafIdItems];
  };

  const SCHEME_DISPLAY_NAMES: Record<string, string> = {
    scfa: "Student Care Fee Assistance (SCFA)",
    smta: "ComCare Short-to-Medium-Term Assistance (SMTA)",
  };

  interface UsageTransitionOptions {
    /**
     * If true, suppress the HEADER for the very first change at the start of a section.
     * (We still insert the INFO_BLOCK.) Subsequent changes will include the HEADER.
     */
    isSectionStart?: boolean;
    /**
     * Seed the “previous usage” context (typically the parent node’s __usedIn)
     * to avoid a false first transition when the first child shares the same usage.
     */
    prevUsage?: string[] | null;
  }

  const toUsageKey = (usage?: string[] | null) => (usage && usage.length ? [...usage].sort().join(",") : null);

  const insertTransitionBlocks = (
    items: (ProcessingNode<SectionMemberSubType> | DecoratorSubType)[],
    options: UsageTransitionOptions = {},
  ): (ProcessingNode<SectionMemberSubType> | DecoratorSubType)[] => {
    if (!items || items.length === 0) return [];

    const final: (ProcessingNode<SectionMemberSubType> | DecoratorSubType)[] = [];
    const { isSectionStart = false, prevUsage = null } = options;
    let previousUsageKey: string | null = toUsageKey(prevUsage);
    let transitionCount = 0;

    for (const item of items) {
      const usage = (item as Partial<ProcessingNode<SectionMemberSubType>>).__usedIn;
      if (!usage) {
        final.push(item);
        continue;
      }

      const currentKey = toUsageKey(usage);
      const usageChanged = currentKey && currentKey !== previousUsageKey;

      if (usageChanged) {
        const firstChangeAtSectionTop = isSectionStart && transitionCount === 0;

        const infoblockText = [...usage]
          .sort()
          .map((code) => `&nbsp;&nbsp;• ${SCHEME_DISPLAY_NAMES[code] ?? code}`)
          .join("\n");

        // HEADER is suppressed only for the very first change at a section start.
        if (!firstChangeAtSectionTop) {
          final.push({
            type: "DECORATOR",
            subType: "HEADER",
            title: `For ${[...usage]
              .sort()
              .map((c) => c.toUpperCase())
              .join(", ")}`,
          });
        }

        final.push({
          type: "DECORATOR",
          subType: "INFO_BLOCK",
          title: [`The details below are used by the following scheme(s):\n${infoblockText}`],
        });

        transitionCount += 1;
      }
      final.push(item);
      previousUsageKey = currentKey;
    }

    return final;
  };

  const finalizeNode = (node: ProcessingNode<SectionMemberSubType>): ProcessingNode<SectionMemberSubType> => {
    interface MutableNode {
      member?: (ProcessingNode<SectionMemberSubType> | DecoratorSubType)[];
      group?: BlankGroupSchema;
      result?: { choice: string[]; member: (ProcessingNode<SectionMemberSubType> | DecoratorSubType)[] }[];
    }

    const unifyAndFinalize = (members: ProcessingNode<SectionMemberSubType>[] | undefined) => {
      if (!Array.isArray(members)) return [];
      const unified = unifyMemberArray(members);
      sortArrayBySafIdx(unified);
      // Recursive call to self is safe with const
      return unified.map((child) => finalizeNode(child));
    };

    const mutableNode = node as MutableNode;
    if ("member" in mutableNode && Array.isArray(mutableNode.member)) {
      mutableNode.member = unifyAndFinalize(mutableNode.member as ProcessingNode<SectionMemberSubType>[]);
      // seed with the parent node’s usage so the first child doesn’t trigger a “first transition”
      mutableNode.member = insertTransitionBlocks(mutableNode.member, { prevUsage: node.__usedIn ?? null });
    }
    if (node.type === "MULTI_VALUE" && mutableNode.group) {
      const finalizedGroup = finalizeNode(mutableNode.group as ProcessingNode<SectionMemberSubType>);
      mutableNode.group = finalizedGroup as BlankGroupSchema;
    }
    if (isConditionalSchema(node) && Array.isArray(mutableNode.result)) {
      mutableNode.result = mutableNode.result.map((r) => {
        const unified = unifyAndFinalize(r.member as ProcessingNode<SectionMemberSubType>[]);
        return {
          ...r,
          member: insertTransitionBlocks(unified, { prevUsage: node.__usedIn ?? null }),
        };
      });
    }

    return node;
  };

  // NEW: normalize any "scfa.*" / "smta.*" / "<code>[...]" to "saf.*" across the final tree
  const normalizePrefillsDeep = (
    nodes: (ProcessingNode<SectionMemberSubType> | DecoratorSubType)[],
    bundleCodes: string[],
  ) => {
    if (!Array.isArray(bundleCodes) || bundleCodes.length === 0) return;

    // escape special regex chars in each bundle code
    // then match a starting code only if it’s followed by “.” or “[” (e.g., scfa. / smta[)
    const escapeRe = (s: string) => s.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    const prefixRe = new RegExp(`^(${bundleCodes.map(escapeRe).join("|")})(?=\\.|\\[)`);

    const visit = (n: ProcessingNode<SectionMemberSubType> | DecoratorSubType) => {
      if (!n || typeof n !== "object") return;

      // field-level prefill
      if ("prefillSource" in n && typeof n.prefillSource === "string") {
        n.prefillSource = n.prefillSource.replace(prefixRe, "saf");
      }
      // conditional selector prefill
      if ("selector" in n && n.selector && typeof n.selector.prefillSource === "string") {
        n.selector.prefillSource = n.selector.prefillSource.replace(prefixRe, "saf");
      }

      if ("member" in n && Array.isArray(n.member)) {
        n.member.forEach((child) => visit(child as ProcessingNode<SectionMemberSubType> | DecoratorSubType));
      }
      if ("group" in n && n.group && typeof n.group === "object") {
        visit(n.group as ProcessingNode<SectionMemberSubType>);
      }
      if ("result" in n && Array.isArray(n.result)) {
        n.result.forEach((child) => visit(child as ProcessingNode<SectionMemberSubType> | DecoratorSubType));
      }
    };

    nodes.forEach((n) => visit(n));
  };

  const removeInternalProperties = (nodes: object[]): void => {
    const visitor = (node: object) => {
      for (const key in node) {
        if (key.startsWith("__")) {
          delete (node as Record<string, unknown>)[key];
        }
      }
    };
    if (Array.isArray(nodes)) {
      nodes.forEach((node) => traverse(node, visitor));
    }
  };

  const collectAndMergeField = (
    section: ProcessingSection,
    fieldNode: SectionMemberSubType,
    schemaName: string,
  ): void => {
    const { safId, safIdx: safIdxStr = "0" } = fieldNode;
    const safIdx = parseInt(safIdxStr, 10);
    const taggedNode = tagNodeWithUsage(fieldNode, schemaName);
    const fieldMap = section.__fieldNodes;
    if (safId) {
      if (!fieldMap.has(safId)) {
        fieldMap.set(safId, { safId, safIdx, __usedIn: [schemaName], __node: taggedNode });
      } else {
        const existingField = fieldMap.get(safId)!;
        existingField.__node = mergeNodes(existingField.__node, taggedNode);
        existingField.safIdx = Math.min(existingField.safIdx, safIdx);
        if (!existingField.__usedIn.includes(schemaName)) existingField.__usedIn.push(schemaName);
      }
    } else {
      const uniqueKey = `_unique_${uniqueNodeCounter++}`;
      fieldMap.set(uniqueKey, { safIdx, __usedIn: [schemaName], __node: taggedNode });
    }
  };

  const buildSectionMap = (): void => {
    for (const [schemaName, schema] of Object.entries(clonedSchemaMap)) {
      for (const section of schema) {
        if (!section.safId) continue;
        const { safId, safIdx: safIdxStr = "0" } = section;
        const safIdx = parseInt(safIdxStr, 10);
        if (!mergedSectionMap.has(safId)) {
          mergedSectionMap.set(safId, {
            ...section,
            safIdx,
            __usedIn: [schemaName],
            __fieldNodes: new Map(),
            member: [],
          });
        } else {
          const existingSection = mergedSectionMap.get(safId)!;
          existingSection.safIdx = Math.min(existingSection.safIdx, safIdx);
          existingSection.__usedIn.push(schemaName);
        }
        const currentSection = mergedSectionMap.get(safId)!;
        for (const field of section.member) {
          collectAndMergeField(currentSection, field, schemaName);
        }
      }
    }
  };

  // =======================================================================
  // MAIN EXECUTION LOGIC
  // =======================================================================

  buildSectionMap();

  const mergedSections = Array.from(mergedSectionMap.values());
  sortArrayBySafIdx(mergedSections);

  return mergedSections.map((section) => {
    const members = Array.from(section.__fieldNodes.values()).map((fieldNode) => {
      const node = fieldNode.__node;
      (node as unknown as { safIdx: string }).safIdx = String(fieldNode.safIdx);
      (node as { __usedIn: string[] }).__usedIn.sort();
      return node;
    });

    const unifiedMembers = unifyMemberArray(members);
    const safIdGroup = unifiedMembers.filter((m) => m.safId);
    const noSafIdGroup = unifiedMembers.filter((m) => !m.safId);

    sortArrayBySafIdx(safIdGroup);
    const correctlyOrderedMembers = [...safIdGroup, ...noSafIdGroup];

    let finalMembers: (ProcessingNode<SectionMemberSubType> | DecoratorSubType)[] = correctlyOrderedMembers.map(
      (node) => finalizeNode(node),
    );

    finalMembers = insertTransitionBlocks(finalMembers, { isSectionStart: true });

    normalizePrefillsDeep(finalMembers, Object.keys(schemaMap));

    removeInternalProperties(finalMembers);

    const finalSection: SectionSchema & Partial<Pick<ProcessingSection, "__fieldNodes" | "__usedIn">> = {
      ...section,
      safIdx: String(section.safIdx),
      member: finalMembers as SectionMemberSubType[],
    };

    delete finalSection.__fieldNodes;
    delete finalSection.__usedIn;

    return finalSection as SectionSchema;
  });
};

/**
 * Merges contact details from a pre-sorted array of schemes.
 * @param sortedSchemas A pre-sorted array of application schemas.
 * @returns A single array of merged contact objects.
 */
export const mergeContacts = (sortedSchemas: ApplicationSchema[]) => {
  // Use flatMap directly on the pre-sorted array
  return sortedSchemas.flatMap(
    (scheme) =>
      scheme.contacts?.map((contact) => ({
        title: scheme.schemeName,
        ...contact,
      })) ?? [],
  );
};

/**
 * Merges application guidance from a pre-sorted array of schemes.
 * @param sortedSchemas A pre-sorted array of application schemas.
 * @returns A single, formatted markdown string.
 */
export const mergeDashboardGuidance = (sortedSchemas: ApplicationSchema[]): string => {
  // Map directly on the pre-sorted array
  const guidanceBlocks = sortedSchemas.map((scheme) => {
    const guidanceText = scheme.dashboard?.applicationGuidance || "";
    return guidanceText
      ? `**${scheme.schemeName}**\n${guidanceText}`
      : "";
  });

  const additionalText = "Apply for multiple schemes in just 1 form\nYour application may take 15 minutes to complete.";

  // Filter and join the blocks
  return [...guidanceBlocks.filter(Boolean), additionalText].join("\n\n&nbsp;\n<hr />\n&nbsp;\n\n");
};

import <PERSON><PERSON> from "koa";

import { ApplicationSchema, logger } from "@sgw/common";
import { mergeContacts, mergeDashboardGuidance, mergeSchemas } from "./utils-schema-merge";
import { getAppStatusResponse, retrieveSchemas } from "../sgw/service";
import { SgwAppStateResponse, SgwAppStatusResponse } from "../sgw/util";
import { SAF_SUPPORTED_SCHEME_CODES } from "./controllers";
import { schemeDao } from "@sgw/services";

/**
 * Retrieves a merged schema for a bundle of SAF schemes.
 * This function checks the cache first, and if the schema is not cached,
 * it fetches the schemas for the specified codes, merges them, and caches the result.
 * It throws an error if any of the requested schemes are not found or are not active.
 * @param schemeCodes - An array of scheme codes to be merged.
 * @returns A merged ApplicationSchema for the specified codes.
 * TODO: implement caching logic
 */
export const getCachedOrMergedSchema = async (schemeCodes: string[]): Promise<ApplicationSchema> => {
  // Create a unique cache key from the sorted codes
  const cacheKey = `saf-schema:${[...schemeCodes].sort().join("-")}`;

  // // Check the cache first
  // const cachedData = await cacheClient.get(cacheKey);
  // if (cachedData) {
  //   logger.info(`Cache HIT for SAF schema: ${cacheKey}`);
  //   return JSON.parse(cachedData);
  // }

  logger.info(`Cache MISS for SAF schema: ${cacheKey}`);

  // On cache miss, fetch the data
  const schemas = await retrieveSchemas(schemeCodes);
  if (schemas.length !== schemeCodes.length) {
    throw new Error(
      `One or more of the requested schemes were not found or are not active.
      Expected: ${schemeCodes.length}, Found: ${schemas.length}`,
    );
  }

  // Call your original, pure merging function
  const mergedSchema = getOrMergeSafSchema(schemas);

  // // Store the new result in the cache (for 1 hour)
  // await cacheClient.set(cacheKey, JSON.stringify(mergedSchema), { EX: 3600 });

  return mergedSchema;
};

/**
 * Merges multiple SAF schemas into a single schema.
 * This function sorts the schemas by schemeCode, merges their dashboard guidance,
 * contact details, and schema sections, and returns a unified ApplicationSchema.
 * It logs the process duration and the number of schemes being merged.
 * @param schemas - An array of ApplicationSchema objects to be merged.
 * @returns A merged ApplicationSchema containing all the information from the input schemas.
 * @throws Error if the input schemas array is empty.
 * @throws Error if the input schemas do not contain valid schemeCode properties.
 * @throws Error if the merging process fails for any reason.
 */
export const getOrMergeSafSchema = (schemas: ApplicationSchema[]): ApplicationSchema => {
  const startTime = Date.now();

  const sortedSchemas = [...schemas].sort((a, b) => a.schemeCode.localeCompare(b.schemeCode));

  const numberOfSchemes = sortedSchemas.length;
  const schemeCodes = sortedSchemas.map((scheme) => scheme.schemeCode);
  const subtitle = sortedSchemas
    .filter((s) => s.schemeName)
    .map((s) => s.schemeName)
    .join("\n");
  logger.info(`Starting SAF schema merge for ${numberOfSchemes} schemes: [${schemeCodes.join(", ")}]`);

  const mergedDashboardGuidance = mergeDashboardGuidance(sortedSchemas);
  const mergedContactDetails = mergeContacts(sortedSchemas);
  const mergedSafSchema = mergeSchemas(sortedSchemas); // Assuming mergeSchemas can also use the sorted array

  const endTime = Date.now();
  const durationMs = endTime - startTime;
  logger.info(
    `SAF schema merge for ${numberOfSchemes} schemes (${schemeCodes.join(", ")}) completed in ${durationMs}ms.`,
  );

  return {
    schemeName: `Applying for ${numberOfSchemes} schemes`,
    schemeCode: "saf",
    bundledSchemeCodes: schemeCodes,
    schemeDetailsLink: "/schemes/SAF",
    subtitle,
    dashboard: {
      applicationGuidance: mergedDashboardGuidance,
    },
    contacts: mergedContactDetails,
    nextSteps: "Upon the submission of all supporting documents, we will take 4-6 weeks to process your application.",
    schema: [
      {
        id: "main",
        section: mergedSafSchema,
      },
    ],
  };
};

/**
 * Retrieves the merged application status for a bundle of SAF schemes.
 * This function fetches the application status for each scheme code in the provided array,
 * and merges the results to determine the overall application state.
 * It logs the process and returns a SgwAppStatusResponse object.
 * If any scheme's application state is not "allow", the overall state will be "unavailable".
 * @param schemeCodes - An array of scheme codes for which to retrieve the application status.
 * @param session - The Koa session object, used for authentication and session management.
 * @returns A SgwAppStatusResponse object containing the merged application status for the specified schemes.
 * @throws Error if the application status retrieval fails for any reason.
 */
export const getMergedStatus = async (
  schemeCodes: string[],
  session: Koa.Context["session"],
): Promise<SgwAppStatusResponse> => {
  logger.info(`SAF: Retrieving SAF status for schemes: ${schemeCodes.join(", ")}`);

  const appStatusPromises = schemeCodes.map(async (schemeCode) => {
    const status = await getAppStatusResponse(schemeCode, session);
    logger.info(`SAF: ${schemeCode}: retrieved application status`, status);
    return status;
  });

  const results = await Promise.all(appStatusPromises);

  // Business logic to determine the final state
  const safState = results.some((status) => status.applicationState !== "allow") ? "unavailable" : "allow";

  // TODO: to assess on and relook at this response data
  const result: SgwAppStatusResponse = {
    schemeCode: "saf",
    status: [],
    agencyUnavailable: false,
    applicationState: safState,
    consentsRequired: [],
  };

  logger.info("SAF: retrieved final application status", result);
  return result;
};

/**
 * Retrieves the user's eligibility of SAF schemes
 * This function fetches the application status for each scheme code in SAF_SUPPORTED_SCHEME_CODES
 * and merges the results to determine the user's eligibilty of each SAF scheme.
 * It logs the process and returns a SgwAppStateResponse array.
 * @param session - The Koa session object, used for authentication and session management.
 * @returns A SgwAppStateResponse object containing the merged application status for the specified schemes.
 * @throws Error if the application status retrieval fails for any reason.
 */
export const getAppState = async (session: Koa.Context["session"]): Promise<SgwAppStateResponse> => {
  let verifiedSafSchemes: string[] = [];

  // check whether "saf" bundledSchemeCode exists in schema of SAF_SUPPORTED_SCHEME_CODES
  try {
    logger.info("SAF: retrieving active SAF schemes");
    const activeSafSchemes = await schemeDao.getActiveSafSchemes(SAF_SUPPORTED_SCHEME_CODES);
    verifiedSafSchemes = activeSafSchemes.map((s) => s.schemeCode);
  } catch (error) {
    logger.error("SAF: error retrieving active SAF schemes", error);
  }

  if (verifiedSafSchemes.length === 0) {
    logger.info("SAF: no verified SAF schemes found");
    return [];
  }

  const appStatusPromises = verifiedSafSchemes.map(async (schemeCode) => {
    const status = await getAppStatusResponse(schemeCode, session);
    logger.info(`SAF: ${schemeCode}: retrieved application status`, status);
    return status;
  });

  const results = await Promise.all(appStatusPromises);

  const result: SgwAppStateResponse = results.map((status) => ({
    schemeCode: status.schemeCode,
    applicationState: status.applicationState,
  }));

  logger.info("SAF: retrieved final application state", result);
  return result;
};

import Router from "@koa/router";
import { Context, DefaultState } from "koa";
import { getSafAppState, getSafSchema, getSafStatus } from "./controllers";

const safRoutes = new Router();
safRoutes.get("/schema", getSafSchema);
safRoutes.get("/status", getSafStatus);
safRoutes.get("/app-state", getSafAppState);

const router = new Router<DefaultState, Context>({ prefix: "/saf" });
router.use(safRoutes.routes(), safRoutes.allowedMethods());

export { router };

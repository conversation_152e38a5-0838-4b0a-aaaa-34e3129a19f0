import { ApplicationSchema, SectionSchema } from "@sgw/common";
import { mergeContacts, mergeDashboardGuidance, mergeSchemas } from "./utils-schema-merge"

const smtaSchema: ApplicationSchema = {
  schemeCode: "smta",
  bundleSchemeCode: "saf",
  schemeName: "ComCare Short-to-Medium-Term Assistance (SMTA)",
  nextSteps: "ss",
  subtitle: "sss",
  schema: [
    {
      id: "main",
      section: [
        {
          id: "profile",
          title: "Profile",
          subtitle: "Tell us about yourself",
          safId: "sect-profile",
          safIdx: "5",
          member: [
            {
              type: "DECORATOR",
              subType: "HEADER",
              safId: "personalDetails",
              safIdx: "5",
              title: "Personal details",
            },
            {
              id: "name",
              type: "PRESET_FIELD",
              subType: "name",
              safId: "name",
              safIdx: "10",
              prefillSource: "myInfo.name",
            },
            {
              type: "SECTION_CONDITIONAL",
              id: "employment",
              safId: "employmentStatus",
              safIdx: "15",
              selector: {
                type: "CUSTOM_FIELD",
                subType: "RADIO",
                id: "employmentStatus",
                prefillSource: "smta.employmentStatus",
                editable: true,
                title: "Employment status",
                options: {
                  ES1: "Working",
                  ES2: "Looking for work",
                  ES3: "Not working",
                },
              },
              result: [
                {
                  choice: ["ES1"],
                  member: [
                    {
                      id: "employmentWithCPF",
                      type: "CUSTOM_FIELD",
                      title: "Are you employed with CPF contribution/taxable income?",
                      options: ["Yes", "No"],
                      subType: "RADIO",
                      safId: "employmentWithCPF",
                      safIdx: "5",
                    },
                    {
                      type: "CUSTOM_FIELD",
                      subType: "MONEY",
                      id: "grossMonthlyIncome",
                      prefillSource: "smta.grossMonthlyIncome",
                      editable: true,
                      title: "Gross monthly income",
                      description: [
                        "Gross monthly income from work refers to income earned from employment.\n&nbsp;\nFor employees, it refers to the gross monthly wages or salaries before deduction of employee CPF contributions and personal income tax. It comprises basic wages, overtime pay, commissions, tips, other allowances and one-twelfth of annual bonuses.\n&nbsp;\nFor self-employed persons, gross monthly income refers to the average monthly profits from their business, trade or profession (i.e. total receipts less business expenses incurred) before deduction of income tax.",
                      ],
                    },
                  ],
                },
                {
                  choice: ["ES3"],
                  member: [
                    {
                      type: "SECTION_CONDITIONAL",
                      id: "situation",
                      safId: "situation",
                      safIdx: "5",
                      selector: {
                        type: "CUSTOM_FIELD",
                        subType: "CHECKBOX",
                        id: "situationType",
                        prefillSource: "smta.situationType",
                        editable: true,
                        title: "Describe your current situation",
                        description: ["You may select more than one option."],
                        options: {
                          UR1: "Retiree",
                          UR2: "Homemaker",
                          UR3: "Full-time caregiver",
                          UR4: "Student",
                          UR5: "Medically unfit for work",
                          UR99: "Others",
                        },
                      },
                      result: [
                        {
                          choice: ["UR99"],
                          member: [
                            {
                              type: "CUSTOM_FIELD",
                              subType: "MULTILINE_TEXT",
                              id: "others",
                              safId: "others",
                              safIdx: "5",
                              prefillSource: "smta.othersReason",
                              editable: true,
                              title: "Tell us more about the situation",
                              maxLength: 300,
                            },
                          ],
                        },
                      ],
                    },
                    {
                      id: "unemploymentDoc",
                      type: "CUSTOM_FIELD",
                      title: "Supporting documents for unemployment",
                      subType: "FILE_UPLOAD",
                      safId: "unemploymentDoc",
                      safIdx: "10",
                      documents: [
                        {
                          paragraph:
                            "Any relevant **supporting documents about the situation** (e.g. Letter of Incarceration (LOI), Medical certificate)",
                        },
                      ],
                      instructions: ["Please upload a copy of:"],
                      optional: true,
                    },
                  ],
                },
              ],
            },
            {
              type: "DECORATOR",
              subType: "HEADER",
              title: "Education details",
            },
            {
              id: "highestEducation",
              type: "CUSTOM_FIELD",
              subType: "DROPDOWN",
              prefillSource: "smta.highestEducation",
              editable: true,
              title: "Highest education level",
              options: {
                0: "No Formal Qualification/Lower Primary",
                1: "Primary",
                2: "Lower Secondary",
                3: "Secondary",
                4: "Post-Secondary (Non-tertiary): General Vocational",
                5: "Polytechnic Diploma Course",
                6: "Professional Qualification and Other Diploma",
                7: "University First Degree",
                8: "University Postgraduate Diploma/Degree",
                9: "Other Education (Non-Award Courses/Miscellaneous)",
                10: "Others",
              },
            },
          ],
        },
        {
          id: "household",
          title: "Household",
          safId: "sect-household",
          safIdx: "10",
          member: [
            {
              type: "SECTION_CONDITIONAL",
              id: "marital",
              safId: "marital",
              safIdx: "5",
              selector: {
                type: "PRESET_FIELD",
                subType: "maritalStatus",
                title: "Marital status",
                id: "maritalStatus",
                prefillSource: "myInfo.maritalStatus",
                editable: true,
              },
              result: [
                {
                  choice: ["4"],
                  member: [
                    {
                      type: "CUSTOM_FIELD",
                      subType: "DATE",
                      safId: "dateOfSeparation",
                      safIdx: "5",
                      id: "dateOfSeparation",
                      title: "Date of separation",
                    },
                  ],
                },
                {
                  choice: ["2"],
                  member: [
                    {
                      type: "DECORATOR",
                      subType: "HEADER",
                      safId: "spouseDetails",
                      safIdx: "5",
                      title: "Spouse details",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "name",
                      safId: "spouseName",
                      safIdx: "10",
                      id: "spouseName",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "nric",
                      safId: "spouseIdNumber",
                      safIdx: "15",
                      id: "spouseIdNumber",
                      consent: true,
                      consenterInfo: {
                        name: "spouseName",
                      },
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "dob",
                      safId: "spouseDob",
                      safIdx: "20",
                      id: "spouseDob",
                    },
                    {
                      type: "PRESET_FIELD",
                      id: "spouseSex",
                      subType: "sex",
                      safId: "spouseSex",
                      safIdx: "25",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "residentialStatus",
                      safId: "spouseResidentialStatus",
                      safIdx: "30",
                      id: "spouseResidentialStatus",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "email",
                      safId: "spouseEmail",
                      safIdx: "35",
                      id: "spouseEmail",
                      optional: true,
                    },
                  ],
                },
              ],
            },
            {
              type: "DECORATOR",
              subType: "HEADER",
              safId: "householdMembers",
              safIdx: "10",
              title: "Household members",
            },
            {
              type: "SECTION_CONDITIONAL",
              id: "cohabitation",
              safId: "cohabitation",
              safIdx: "15",
              selector: {
                type: "CUSTOM_FIELD",
                subType: "RADIO",
                id: "cohabitationStatus",
                prefillSource: "smta.cohabitationStatus",
                editable: true,
                title: "Do you live with any household member?",
                description: [
                  "This refers to family members who are living in the same registered address (e.g. parents, children). You are not required to provide your spouse details (if any) again.",
                ],
                options: ["Yes", "No"],
              },
              result: [
                {
                  choice: ["0"],
                  member: [
                    {
                      type: "MULTI_VALUE",
                      id: "familyMember",
                      safId: "familyMember",
                      safIdx: "5",
                      prefillSource: "smta.familyMemberDetails",
                      editable: true,
                      title: "How many household members (apart from spouse) live with you?",
                      maxGroup: 15,
                      header: "Household member details",
                      group: {
                        type: "CUSTOM_GROUP",
                        subType: "BLANK",
                        id: "familyMemberDetails",
                        title: "Household member",
                        member: [
                          {
                            id: "family",
                            safId: "family",
                            safIdx: "5",
                            prefillSource: "family",
                            editable: true,
                            type: "PRESET_FIELD",
                            consent: true,
                            subType: "family",
                          },
                          {
                            type: "PRESET_FIELD",
                            subType: "residentialStatus",
                            safId: "residentialStatus",
                            safIdx: "10",
                            id: "residentialStatus",
                          },
                          {
                            type: "PRESET_FIELD",
                            subType: "email",
                            safId: "email",
                            safIdx: "15",
                            id: "email",
                            prefillSource: "email",
                            editable: true,
                            optional: true,
                          },
                          {
                            type: "PRESET_FIELD",
                            subType: "relationshipType",
                            safId: "relationshipType",
                            safIdx: "20",
                            id: "relationshipType",
                            title: "Relationship to applicant",
                            excludedOptions: ["REL001"],
                            prefillSource: "relationshipType",
                            editable: true,
                          },
                          {
                            id: "memberMinorConsent",
                            safId: "memberMinorConsent",
                            safIdx: "25",
                            type: "GLOBAL_GROUP_CONDITIONAL",
                            result: [
                              {
                                choice: [
                                  {
                                    type: "value",
                                    sourcePath: "computedAge",
                                    operator: "lessThan",
                                    value: "21",
                                  },
                                ],
                                member: [
                                  {
                                    id: "consent",
                                    safId: "consent",
                                    safIdx: "5",
                                    type: "CUSTOM_FIELD",
                                    subType: "TNC",
                                    title: "Consent / declaration for minors",
                                    acknowledge: "I acknowledge and consent to the terms, on behalf of this minor",
                                    content: [
                                      {
                                        header:
                                          "Consent for collection, use and disclosure of personal information for the application or renewal of application for the subsidy and/or the Start Up Grant",
                                        indent: [
                                          {
                                            paragraph:
                                              "I am the parent/legal guardian of the Child who is under 21 years of age.",
                                          },
                                          {
                                            indent: [
                                              {
                                                paragraph:
                                                  "to verify my, my Child and Family’s identity and relationship for the Subsidy and/or the Start Up Grant (“the Subsidies”) and other Services or Scheme;",
                                              },
                                              {
                                                paragraph:
                                                  "to determine my, my Child and Family’s eligibility for the Subsidies and other Services or Scheme;",
                                              },
                                              {
                                                paragraph:
                                                  "to provide me, my Child and Family with both or any of the Subsidies and other Services or Scheme; and",
                                              },
                                              {
                                                paragraph:
                                                  "for data analysis, evaluation and policy-making, for the Subsidies and other Services or Scheme.",
                                              },
                                            ],
                                            paragraph:
                                              "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:",
                                          },
                                          {
                                            paragraph:
                                              "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information, to the extent permitted by the Singapore Public Agencies and Participating Organisations, for the purposes stated in Paragraph 2. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that my personal information will not be shared with non-participating agencies and organisations.",
                                          },
                                          {
                                            paragraph:
                                              "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place.",
                                          },
                                          {
                                            paragraph:
                                              "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form.",
                                          },
                                          {
                                            paragraph:
                                              "This consent shall be governed by and construed in accordance with the laws of the Republic of Singapore.",
                                          },
                                        ],
                                      },
                                      {
                                        header: "Declaration",
                                        indent: [
                                          {
                                            paragraph:
                                              "I, the undersigned, declare that I have read and understood the content in this Application Form. I confirm that the information that I have provided is true and correct and I furnish the information knowing that I may be liable to criminal prosecution if I have stated any information that I know to be false or not believe to be true.",
                                          },
                                          {
                                            paragraph:
                                              "In the event my application is successful and my Child receives any of the Subsidies which I am applying for, I hereby acknowledge that I may also be liable to make full repayment to the Government of the Subsidies which were provided, should I be found to have provided false or inaccurate information in this form.",
                                          },
                                        ],
                                      },
                                      {
                                        header: "Other terms",
                                        indent: [
                                          {
                                            indent: [
                                              {
                                                paragraph:
                                                  "It shall be my responsibility to stay employed (i.e. to be engaged under a contract or service and receive a salary) to continue to enjoy the Subsidies for my Child. If I am unemployed and intend to seek employment, the onus is on me to actively seek employment.",
                                              },
                                              {
                                                paragraph:
                                                  "(only applicable to applications for the Start Up Grant) The Start Up Grant shall only be given once to my Child, and any subsequent applications shall be assessed and granted only in MSF’s sole discretion.",
                                              },
                                              {
                                                paragraph:
                                                  "In order to continue enjoying the relevant monthly Subsidy, I must ensure that my Child attends at least 30% (June and December) and 50% (Other Calendar months) of the number of days in which the SCC operates per month. My Child must be present at the centre for at least 3 hours in order to be considered present for the day. If my Child does not meet the minimum attendance rate, the Subsidy paid for the relevant month may be refunded to MSF and I am liable to pay the full SCC monthly fee.",
                                              },
                                              {
                                                paragraph:
                                                  " I shall provide the SCC with a one-month notice before withdrawing my Child from the SCC.",
                                              },
                                            ],
                                            paragraph: "I understand and agree to the following:",
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                              {
                                choice: [
                                  {
                                    type: "value",
                                    sourcePath: "computedAge",
                                    operator: "greaterThanInclusive",
                                    value: "21",
                                  },
                                ],
                                member: [
                                  {
                                    type: "DECORATOR",
                                    safId: "consentInfoblock",
                                    safIdx: "5",
                                    title: [
                                      "**Household members’ consent is required for the application.**\nMore details on how your household members can provide consent will be given after the application is submitted.",
                                    ],
                                    subType: "INFO_BLOCK",
                                  },
                                ],
                              },
                            ],
                            selector: {
                              id: "computedAge",
                              type: "CUSTOM_FIELD",
                              subType: "HIDDEN",
                              globalAction: {
                                type: "calculateMultiValueAge",
                                dependsOn: ["household.cohabitation.familyMember.familyMemberDetails"],
                                relativeIds: ["family.memberDob"],
                              },
                            },
                          },
                        ],
                      },
                    },
                  ],
                },
              ],
            },
            {
              type: "DECORATOR",
              subType: "HEADER",
              title: "Other family members",
            },
            {
              type: "SECTION_CONDITIONAL",
              id: "dependent",
              selector: {
                type: "CUSTOM_FIELD",
                subType: "RADIO",
                id: "dependentStatus",
                title: "Do you have any other family members that live separately but depend on you financially?",
                description: [
                  "This refers to family members who are not living in the same registered address (e.g. parents, children). You are not required to provide your spouse and household member details (if any) again.",
                ],
                options: ["Yes", "No"],
              },
              result: [
                {
                  choice: ["0"],
                  member: [
                    {
                      type: "MULTI_VALUE",
                      id: "familyMember",
                      title: "How many of these other family members are there?",
                      maxGroup: 15,
                      header: "Other family member details ",
                      group: {
                        type: "CUSTOM_GROUP",
                        subType: "BLANK",
                        id: "familyMemberDetails",
                        title: "Other family member",
                        member: [
                          {
                            type: "PRESET_FIELD",
                            subType: "name",
                            id: "name",
                          },
                          {
                            type: "PRESET_FIELD",
                            subType: "nric",
                            id: "nric",
                            consent: true,
                            consenterInfo: {
                              name: "name",
                            },
                          },
                          {
                            type: "PRESET_FIELD",
                            subType: "dob",
                            id: "dob",
                          },
                          {
                            type: "PRESET_FIELD",
                            id: "sex",
                            subType: "sex",
                          },
                          {
                            type: "PRESET_FIELD",
                            subType: "residentialStatus",
                            id: "residentialStatus",
                          },
                          {
                            type: "PRESET_FIELD",
                            subType: "email",
                            id: "email",
                            optional: true,
                          },
                          {
                            type: "PRESET_FIELD",
                            subType: "relationshipType",
                            id: "relationshipType",
                            title: "Relationship to applicant",
                            excludedOptions: ["REL001"],
                          },
                          {
                            id: "memberMinorConsent",
                            type: "GLOBAL_GROUP_CONDITIONAL",
                            result: [
                              {
                                choice: [
                                  {
                                    type: "value",
                                    sourcePath: "computedMemberAge",
                                    operator: "lessThan",
                                    value: "21",
                                  },
                                ],
                                member: [
                                  {
                                    id: "consent",
                                    type: "CUSTOM_FIELD",
                                    subType: "TNC",
                                    title: "Consent / declaration for minors",
                                    acknowledge: "I acknowledge and consent to the terms, on behalf of this minor",
                                    content: [
                                      {
                                        header:
                                          "Consent for collection, use and disclosure of personal information for the application or renewal of application for the subsidy and/or the Start Up Grant",
                                        indent: [
                                          {
                                            paragraph:
                                              "I am the parent/legal guardian of the Child who is under 21 years of age.",
                                          },
                                          {
                                            indent: [
                                              {
                                                paragraph:
                                                  "to verify my, my Child and Family’s identity and relationship for the Subsidy and/or the Start Up Grant (“the Subsidies”) and other Services or Scheme;",
                                              },
                                              {
                                                paragraph:
                                                  "to determine my, my Child and Family’s eligibility for the Subsidies and other Services or Scheme;",
                                              },
                                              {
                                                paragraph:
                                                  "to provide me, my Child and Family with both or any of the Subsidies and other Services or Scheme; and",
                                              },
                                              {
                                                paragraph:
                                                  "for data analysis, evaluation and policy-making, for the Subsidies and other Services or Scheme.",
                                              },
                                            ],
                                            paragraph:
                                              "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:",
                                          },
                                          {
                                            paragraph:
                                              "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information, to the extent permitted by the Singapore Public Agencies and Participating Organisations, for the purposes stated in Paragraph 2. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that my personal information will not be shared with non-participating agencies and organisations.",
                                          },
                                          {
                                            paragraph:
                                              "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place.",
                                          },
                                          {
                                            paragraph:
                                              "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form.",
                                          },
                                          {
                                            paragraph:
                                              "This consent shall be governed by and construed in accordance with the laws of the Republic of Singapore.",
                                          },
                                        ],
                                      },
                                      {
                                        header: "Declaration",
                                        indent: [
                                          {
                                            paragraph:
                                              "I, the undersigned, declare that I have read and understood the content in this Application Form. I confirm that the information that I have provided is true and correct and I furnish the information knowing that I may be liable to criminal prosecution if I have stated any information that I know to be false or not believe to be true.",
                                          },
                                          {
                                            paragraph:
                                              "In the event my application is successful and my Child receives any of the Subsidies which I am applying for, I hereby acknowledge that I may also be liable to make full repayment to the Government of the Subsidies which were provided, should I be found to have provided false or inaccurate information in this form.",
                                          },
                                        ],
                                      },
                                      {
                                        header: "Other terms",
                                        indent: [
                                          {
                                            indent: [
                                              {
                                                paragraph:
                                                  "It shall be my responsibility to stay employed (i.e. to be engaged under a contract or service and receive a salary) to continue to enjoy the Subsidies for my Child. If I am unemployed and intend to seek employment, the onus is on me to actively seek employment.",
                                              },
                                              {
                                                paragraph:
                                                  "(only applicable to applications for the Start Up Grant) The Start Up Grant shall only be given once to my Child, and any subsequent applications shall be assessed and granted only in MSF’s sole discretion.",
                                              },
                                              {
                                                paragraph:
                                                  "In order to continue enjoying the relevant monthly Subsidy, I must ensure that my Child attends at least 30% (June and December) and 50% (Other Calendar months) of the number of days in which the SCC operates per month. My Child must be present at the centre for at least 3 hours in order to be considered present for the day. If my Child does not meet the minimum attendance rate, the Subsidy paid for the relevant month may be refunded to MSF and I am liable to pay the full SCC monthly fee.",
                                              },
                                              {
                                                paragraph:
                                                  " I shall provide the SCC with a one-month notice before withdrawing my Child from the SCC.",
                                              },
                                            ],
                                            paragraph: "I understand and agree to the following:",
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                              {
                                choice: [
                                  {
                                    type: "value",
                                    sourcePath: "computedMemberAge",
                                    operator: "greaterThanInclusive",
                                    value: "21",
                                  },
                                ],
                                member: [
                                  {
                                    type: "DECORATOR",
                                    title: [
                                      "**Household members’ consent is required for the application.**\nMore details on how your household members can provide consent will be given after the application is submitted.",
                                    ],
                                    subType: "INFO_BLOCK",
                                  },
                                ],
                              },
                            ],
                            selector: {
                              id: "computedMemberAge",
                              type: "CUSTOM_FIELD",
                              subType: "HIDDEN",
                              globalAction: {
                                type: "calculateMultiValueAge",
                                dependsOn: ["household.dependent.familyMember.familyMemberDetails"],
                                relativeIds: ["dob"],
                              },
                            },
                          },
                        ],
                      },
                    },
                  ],
                },
              ],
            },
            {
              type: "CUSTOM_FIELD",
              subType: "FILE_UPLOAD",
              id: "hhMemberBankStatement",
              title: "Updated bank statements/bank books of all household and other family members",
              optional: true,
              instructions: ["Please upload a copy of:"],
              documents: [
                {
                  paragraph:
                    "Your **bank statements/bank books of all household and other family members for all bank accounts,** if any",
                },
              ],
              additionalDetails: [
                "For each account, your household and other family members’ full name, bank account number, and latest transaction details of at least 1 month before the application date must be shown clearly (e.g. if you are applying on 1 Jun 2024, transactions between 1 May 2024 and 1 Jun 2024 must be reflected).",
                "I understand that submitting incorrect documents may cause delays in processing my application.",
              ],
            },
            {
              type: "CUSTOM_FIELD",
              subType: "FILE_UPLOAD",
              id: "hhMemberIdDoc",
              title: "NRIC/FIN/BC of household and other family members",
              optional: true,
              instructions: ["Please upload a copy of:"],
              documents: [
                {
                  paragraph: "Front and back of the **NRIC/FIN of all adults,** if any",
                },
                {
                  paragraph: "**Birth certificate of all children,** if any",
                },
              ],
              additionalDetails: ["Your household and other family member’s name and details must be shown clearly."],
            },
          ],
        },
        {
          id: "additionalDetails",
          title: "Additional details",
          subtitle:
            "Provide additional details about your application, if relevant, to help us better assess your situation.",
          safId: "sect-additional-details",
          safIdx: "15",
          member: [
            {
              type: "CUSTOM_FIELD",
              subType: "MULTILINE_TEXT",
              id: "concerns",
              title:
                "What concerns do you have about your household's situation, and what support are you currently receiving?",
              optional: true,
              maxLength: 300,
              description: [
                "It can be about a loss of job or income, difficulty paying for basic needs, or any other concerns. Please also share any support you're currently receiving from other sources e.g. community organisations, non-immediate family members, etc. We will do our best to help you.",
              ],
            },
          ],
        },
      ],
    },
  ],
  contacts: [
    {
      faqLink: "https://ask.gov.sg/msf?topic=ComCare&subtopic=Short-to-Medium-Term%20Assistance",
      email: "<EMAIL>",
      hotlineNumber: "1800 222 0000",
      locationLink: "https://www.msf.gov.sg/dfcs/sso/",
      helpExtra: [
        "You may also visit your nearest Social Service Office if you require urgent help.",
        "If you are receiving SMTA/LTA and require further help, please contact your SSO officer directly.",
        "Charges apply for mobile calls to 1800 service lines. Calls are free of charge only if made from regular land lines in Singapore.",
      ],
    },
  ],
  dashboard: { applicationGuidance: "Your application may take 10 minutes to complete." },
};

const scfaSchema: ApplicationSchema = {
  schemeCode: "scfa",
  bundleSchemeCode: "saf",
  schemeName: "Student Care Fee Assistance (SCFA)",
  nextSteps: "ss",
  subtitle: "sss",
  schema: [
    {
      id: "main",
      section: [
        {
          id: "profile",
          title: "Profile",
          subtitle: "Tell us about yourself",
          safId: "sect-profile",
          safIdx: "5",
          member: [
            {
              type: "DECORATOR",
              subType: "HEADER",
              safId: "personalDetails",
              safIdx: "5",
              title: "Personal details",
            },
            {
              id: "name",
              type: "PRESET_FIELD",
              subType: "name",
              safId: "name",
              safIdx: "10",
              prefillSource: "myInfo.name",
            },
            {
              type: "SECTION_CONDITIONAL",
              id: "employment",
              safId: "employmentStatus",
              safIdx: "15",
              selector: {
                type: "CUSTOM_FIELD",
                subType: "RADIO",
                id: "employmentStatus",
                title: "Employment status",
                options: {
                  ES1: "Working",
                  ES2: "Looking for work",
                  ES3: "Not working",
                },
              },
              result: [
                {
                  choice: ["ES1"],
                  member: [
                    {
                      id: "employmentWithCPF",
                      type: "CUSTOM_FIELD",
                      title: "Are you employed with CPF contribution/taxable income?",
                      options: ["Yes", "No"],
                      subType: "RADIO",
                      safId: "employmentWithCPF",
                      safIdx: "5",
                    },
                  ],
                },
                {
                  choice: ["ES2"],
                  member: [
                    {
                      id: "SD17",
                      type: "CUSTOM_FIELD",
                      title: "Proof of job search",
                      subType: "FILE_UPLOAD",
                      documents: [
                        {
                          paragraph:
                            "**Screenshot** of your **job seeker profile,** and **submitted job applications** (if any), from one of the following platforms:",
                          indent: [
                            {
                              paragraph:
                                "Career Centre under Workforce Singapore (WSG) or Employment and Employability Institute (e2i)",
                            },
                            {
                              paragraph: "Private employment agency",
                            },
                            {
                              paragraph: "Any other job search portal",
                            },
                          ],
                        },
                        {
                          paragraph: "**Email correspondence** with company on **submitted job applications**",
                        },
                      ],
                      instructions: ["Please upload **any** of the following documents:"],
                    },
                  ],
                },
                {
                  choice: ["ES3"],
                  member: [
                    {
                      type: "SECTION_CONDITIONAL",
                      id: "situation",
                      safId: "situation",
                      safIdx: "5",
                      selector: {
                        type: "CUSTOM_FIELD",
                        subType: "CHECKBOX",
                        id: "situationType",
                        title: "Describe your current situation",
                        description: ["You may select more than one option."],
                        options: {
                          UR1: "Retiree",
                          UR2: "Homemaker",
                          UR3: "Full-time caregiver",
                          UR4: "Student",
                          UR5: "Medically unfit for work",
                          UR99: "Others",
                        },
                      },
                      result: [
                        {
                          choice: ["UR99"],
                          member: [
                            {
                              type: "CUSTOM_FIELD",
                              subType: "MULTILINE_TEXT",
                              id: "others",
                              safId: "others",
                              safIdx: "5",
                              title: "Tell us more about the situation",
                              maxLength: 300,
                            },
                          ],
                        },
                      ],
                    },
                    {
                      id: "unemploymentDoc",
                      type: "CUSTOM_FIELD",
                      title: "Supporting documents for unemployment",
                      subType: "FILE_UPLOAD",
                      safId: "unemploymentDoc",
                      safIdx: "10",
                      documents: [
                        {
                          paragraph:
                            "Any relevant **supporting documents about the situation** (e.g. Letter of Incarceration (LOI), Medical certificate)",
                        },
                      ],
                      instructions: ["Please upload a copy of:"],
                      optional: true,
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: "household",
          title: "Household",
          safId: "sect-household",
          safIdx: "10",
          member: [
            {
              type: "SECTION_CONDITIONAL",
              id: "marital",
              safId: "marital",
              safIdx: "5",
              selector: {
                type: "PRESET_FIELD",
                subType: "maritalStatus",
                title: "Marital status",
                id: "maritalStatus",
                prefillSource: "myInfo.maritalStatus",
                editable: true,
              },
              result: [
                {
                  choice: ["4"],
                  member: [
                    {
                      type: "CUSTOM_FIELD",
                      subType: "DATE",
                      safId: "dateOfSeparation",
                      safIdx: "5",
                      id: "dateOfSeparation",
                      title: "Date of separation",
                    },
                  ],
                },
                {
                  choice: ["2"],
                  member: [
                    {
                      type: "DECORATOR",
                      subType: "HEADER",
                      safId: "spouseDetails",
                      safIdx: "5",
                      title: "Spouse details",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "name",
                      safId: "spouseName",
                      safIdx: "10",
                      id: "spouseName",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "nric",
                      safId: "spouseIdNumber",
                      safIdx: "15",
                      id: "spouseIdNumber",
                      consent: true,
                      consenterInfo: {
                        name: "spouseName",
                      },
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "dob",
                      safId: "spouseDob",
                      safIdx: "20",
                      id: "spouseDob",
                    },
                    {
                      type: "PRESET_FIELD",
                      id: "spouseSex",
                      subType: "sex",
                      safId: "spouseSex",
                      safIdx: "25",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "residentialStatus",
                      safId: "spouseResidentialStatus",
                      safIdx: "30",
                      id: "spouseResidentialStatus",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "email",
                      safId: "spouseEmail",
                      safIdx: "35",
                      id: "spouseEmail",
                      optional: true,
                    },
                  ],
                },
              ],
            },
            {
              type: "DECORATOR",
              subType: "HEADER",
              safId: "householdMembers",
              safIdx: "10",
              title: "Household members",
            },
            {
              type: "SECTION_CONDITIONAL",
              id: "cohabitation",
              safId: "cohabitation",
              safIdx: "15",
              selector: {
                type: "CUSTOM_FIELD",
                subType: "RADIO",
                id: "cohabitationStatus",
                title: "Do you live with any household member?",
                description: [
                  "This refers to family members who are living in the same registered address (e.g. parents, children). You are not required to provide your spouse details (if any) again.",
                ],
                options: ["Yes", "No"],
              },
              result: [
                {
                  choice: ["0"],
                  member: [
                    {
                      type: "MULTI_VALUE",
                      id: "familyMember",
                      safId: "familyMember",
                      safIdx: "5",
                      title: "How many household members (apart from spouse) live with you?",
                      maxGroup: 15,
                      header: "Household member details",
                      group: {
                        type: "CUSTOM_GROUP",
                        subType: "BLANK",
                        id: "familyMemberDetails",
                        title: "Household member",
                        member: [
                          {
                            id: "family",
                            safId: "family",
                            safIdx: "5",
                            type: "PRESET_FIELD",
                            consent: true,
                            subType: "family",
                          },
                          {
                            type: "PRESET_FIELD",
                            subType: "residentialStatus",
                            safId: "residentialStatus",
                            safIdx: "10",
                            id: "residentialStatus",
                          },
                          {
                            type: "PRESET_FIELD",
                            subType: "email",
                            safId: "email",
                            safIdx: "15",
                            id: "email",
                            optional: true,
                          },
                          {
                            type: "PRESET_FIELD",
                            subType: "relationshipType",
                            safId: "relationshipType",
                            safIdx: "20",
                            id: "relationshipType",
                            title: "Relationship to applicant",
                            excludedOptions: ["REL001"],
                          },
                          {
                            id: "memberMinorConsent",
                            safId: "memberMinorConsent",
                            safIdx: "25",
                            type: "GLOBAL_GROUP_CONDITIONAL",
                            result: [
                              {
                                choice: [
                                  {
                                    type: "value",
                                    sourcePath: "computedAge",
                                    operator: "lessThan",
                                    value: "21",
                                  },
                                ],
                                member: [
                                  {
                                    id: "consent",
                                    safId: "consent",
                                    safIdx: "5",
                                    type: "CUSTOM_FIELD",
                                    subType: "TNC",
                                    title: "Consent / declaration for minors",
                                    acknowledge: "I acknowledge and consent to the terms, on behalf of this minor",
                                    content: [
                                      {
                                        header:
                                          "Consent for collection, use and disclosure of personal information for the application or renewal of application for the subsidy and/or the Start Up Grant",
                                        indent: [
                                          {
                                            paragraph:
                                              "I am the parent/legal guardian of the Child who is under 21 years of age.",
                                          },
                                          {
                                            indent: [
                                              {
                                                paragraph:
                                                  "to verify my, my Child and Family’s identity and relationship for the Subsidy and/or the Start Up Grant (“the Subsidies”) and other Services or Scheme;",
                                              },
                                              {
                                                paragraph:
                                                  "to determine my, my Child and Family’s eligibility for the Subsidies and other Services or Scheme;",
                                              },
                                              {
                                                paragraph:
                                                  "to provide me, my Child and Family with both or any of the Subsidies and other Services or Scheme; and",
                                              },
                                              {
                                                paragraph:
                                                  "for data analysis, evaluation and policy-making, for the Subsidies and other Services or Scheme.",
                                              },
                                            ],
                                            paragraph:
                                              "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:",
                                          },
                                          {
                                            paragraph:
                                              "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information, to the extent permitted by the Singapore Public Agencies and Participating Organisations, for the purposes stated in Paragraph 2. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that my personal information will not be shared with non-participating agencies and organisations.",
                                          },
                                          {
                                            paragraph:
                                              "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place.",
                                          },
                                          {
                                            paragraph:
                                              "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form.",
                                          },
                                          {
                                            paragraph:
                                              "This consent shall be governed by and construed in accordance with the laws of the Republic of Singapore.",
                                          },
                                        ],
                                      },
                                      {
                                        header: "Declaration",
                                        indent: [
                                          {
                                            paragraph:
                                              "I, the undersigned, declare that I have read and understood the content in this Application Form. I confirm that the information that I have provided is true and correct and I furnish the information knowing that I may be liable to criminal prosecution if I have stated any information that I know to be false or not believe to be true.",
                                          },
                                          {
                                            paragraph:
                                              "In the event my application is successful and my Child receives any of the Subsidies which I am applying for, I hereby acknowledge that I may also be liable to make full repayment to the Government of the Subsidies which were provided, should I be found to have provided false or inaccurate information in this form.",
                                          },
                                        ],
                                      },
                                      {
                                        header: "Other terms",
                                        indent: [
                                          {
                                            indent: [
                                              {
                                                paragraph:
                                                  "It shall be my responsibility to stay employed (i.e. to be engaged under a contract or service and receive a salary) to continue to enjoy the Subsidies for my Child. If I am unemployed and intend to seek employment, the onus is on me to actively seek employment.",
                                              },
                                              {
                                                paragraph:
                                                  "(only applicable to applications for the Start Up Grant) The Start Up Grant shall only be given once to my Child, and any subsequent applications shall be assessed and granted only in MSF’s sole discretion.",
                                              },
                                              {
                                                paragraph:
                                                  "In order to continue enjoying the relevant monthly Subsidy, I must ensure that my Child attends at least 30% (June and December) and 50% (Other Calendar months) of the number of days in which the SCC operates per month. My Child must be present at the centre for at least 3 hours in order to be considered present for the day. If my Child does not meet the minimum attendance rate, the Subsidy paid for the relevant month may be refunded to MSF and I am liable to pay the full SCC monthly fee.",
                                              },
                                              {
                                                paragraph:
                                                  " I shall provide the SCC with a one-month notice before withdrawing my Child from the SCC.",
                                              },
                                            ],
                                            paragraph: "I understand and agree to the following:",
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                              {
                                choice: [
                                  {
                                    type: "value",
                                    sourcePath: "computedAge",
                                    operator: "greaterThanInclusive",
                                    value: "21",
                                  },
                                ],
                                member: [
                                  {
                                    type: "DECORATOR",
                                    safId: "consentInfoblock",
                                    safIdx: "5",
                                    title: [
                                      "**Household members’ consent is required for the application.**\nMore details on how your household members can provide consent will be given after the application is submitted.",
                                    ],
                                    subType: "INFO_BLOCK",
                                  },
                                ],
                              },
                            ],
                            selector: {
                              id: "computedAge",
                              type: "CUSTOM_FIELD",
                              subType: "HIDDEN",
                              globalAction: {
                                type: "calculateMultiValueAge",
                                dependsOn: ["household.cohabitation.familyMember.familyMemberDetails"],
                                relativeIds: ["family.memberDob"],
                              },
                            },
                          },
                          {
                            type: "GROUP_CONDITIONAL",
                            id: "scfaBeneficiary",
                            selector: {
                              type: "CUSTOM_FIELD",
                              subType: "RADIO",
                              id: "isBeneficiary",
                              title: "Are you applying SCFA for this household member?",
                              options: ["Yes", "No"],
                            },
                            result: [
                              {
                                choice: ["0"],
                                member: [
                                  {
                                    type: "PRESET_FIELD",
                                    subType: "school",
                                    id: "childSchoolName",
                                  },
                                  {
                                    type: "GROUP_CONDITIONAL",
                                    id: "childScc",
                                    selector: {
                                      type: "CUSTOM_FIELD",
                                      subType: "RADIO",
                                      id: "sccType",
                                      title: "Student care centre (SCC) type",
                                      description: [
                                        "**School-based SCC**: only accepts students from primary schools they are located in.",
                                        "**Community-based SCC**: located in community, outside school compounds.",
                                      ],
                                      options: {
                                        SCC1: "School-based SCC",
                                        SCC2: "Community-based SCC",
                                      },
                                    },
                                    result: [
                                      {
                                        choice: ["SCC1"],
                                        member: [
                                          {
                                            type: "CUSTOM_FIELD",
                                            subType: "DYNAMIC_DROPDOWN",
                                            id: "sccCodeSchool",
                                            title: "School-based SCC",
                                            url: "http://localhost:3456/agency-dropdown-options/scfa?query=school",
                                          },
                                        ],
                                      },
                                      {
                                        choice: ["SCC2"],
                                        member: [
                                          {
                                            type: "CUSTOM_FIELD",
                                            subType: "DYNAMIC_DROPDOWN",
                                            id: "sccCodeCommunity",
                                            title: "Community-based SCC",
                                            url: "http://localhost:3456/agency-dropdown-options/scfa?query=community",
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                  {
                                    type: "CUSTOM_FIELD",
                                    subType: "DATE_MONTH",
                                    id: "childSubsidyStartMonth",
                                    title: "Request for subsidy start month",
                                    description: [
                                      "You may apply for a renewal of your SCFA support within 4 months of expiry if you meet the eligibility criteria.",
                                    ],
                                    minMonthRange: 5,
                                    maxMonthRange: 4,
                                  },
                                  {
                                    type: "GROUP_CONDITIONAL",
                                    id: "relationship",
                                    selector: {
                                      type: "PRESET_FIELD",
                                      subType: "relationshipType",
                                      id: "childRelationshipType",
                                      title: "Relationship to beneficiary",
                                      description: ["Beneficiary refers to the child you are applying for."],
                                      allowedOptions: ["REL002", "REL205", "REL206", "REL009"],
                                    },
                                    result: [
                                      {
                                        choice: ["REL002", "REL205", "REL206"],
                                        member: [
                                          {
                                            type: "CUSTOM_FIELD",
                                            subType: "SINGLE_CHECK",
                                            id: "declaration",
                                            title:
                                              "I declare that I am now living with my child(ren) in the same address, and I am caring for the child(ren). If there is another parent with shared care and control of the child(ren), I have sought his/her consent for the SCFA application.",
                                          },
                                          {
                                            type: "PRESET_FIELD",
                                            subType: "relationshipType",
                                            id: "spouseChildRelationshipType",
                                            title: "Spouse’s relationship to beneficiary (if applicable)",
                                            allowedOptions: ["REL002", "REL205", "REL206"],
                                            description: ["Beneficiary refers to the child you are applying for."],
                                            optional: true,
                                          },
                                        ],
                                      },
                                      {
                                        choice: ["REL009"],
                                        member: [
                                          {
                                            type: "CUSTOM_FIELD",
                                            subType: "FILE_UPLOAD",
                                            id: "SD5",
                                            title: "Guardianship documents",
                                            documents: [
                                              {
                                                paragraph: "**Legal guardian**:",
                                                indent: [
                                                  {
                                                    paragraph: "Guardianship paper",
                                                  },
                                                ],
                                              },
                                              {
                                                paragraph: "**Non-legal guardian**:",
                                                indent: [
                                                  {
                                                    paragraph:
                                                      "Any of the following documents explaining the need to be the non-legal guardian of child such as:",
                                                    indent: [
                                                      "Parent(s)’ death certificate",
                                                      "Police report",
                                                      "Prison letter",
                                                      "[Annex 6](https://www.msf.gov.sg/docs/default-source/student-care/annex-6-self-declaration-form.pdf): SCFA Self Declaration Form",
                                                      "Proof that non-legal guardian is also applicant of approved MOE-FAS application for child",
                                                    ],
                                                  },
                                                ],
                                              },
                                              {
                                                paragraph: "**Foster parent**:",
                                                indent: [
                                                  {
                                                    paragraph: "Letter of recommendation from foster care agencies",
                                                  },
                                                ],
                                              },
                                            ],
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: "additionalDetails",
          title: "Additional details",
          subtitle:
            "Provide additional details about your application, if relevant, to help us better assess your situation.",
          safId: "sect-additional-details",
          safIdx: "15",
          member: [
            {
              type: "CUSTOM_FIELD",
              subType: "MULTILINE_TEXT",
              id: "comments",
              title: "Comments",
              optional: true,
              maxLength: 300,
              description: [
                "Let us know if you had difficulties providing any document or feedback to help improve the form experience.",
              ],
            },
          ],
        },
      ],
    },
  ],
  contacts: [
    {
      faqLink: "https://ask.gov.sg/msf?topic=ComCare&subtopic=Student%20Care%20Fee%20Assistance",
      email: "<EMAIL>",
      hotlineNumber: "1800 111 2222",
      helpExtra: [
        "Charges apply for mobile calls to 1800 service lines. Calls are free of charge only if made from regular land lines in Singapore.",
      ],
    },
  ],
  dashboard: {
    applicationGuidance:
      "**Child must attend a [Student Care Centre](https://www.msf.gov.sg/our-services/directories#studenttab) registered with MSF to qualify for the assistance.**\n&nbsp;\nThe application may take 15 mins to complete.\n&nbsp;\nYou may apply for a **renewal** of your SCFA support up to 6 months from now if you meet the eligibility criteria.",
  },
};

const mergedSafSchema: SectionSchema[] = [
  {
    id: "profile",
    title: "Profile",
    subtitle: "Tell us about yourself",
    safId: "sect-profile",
    safIdx: "5",
    member: [
      {
        type: "DECORATOR",
        subType: "INFO_BLOCK",
        title: [
          [
            "The details below are used by the following scheme(s):",
            "&nbsp;&nbsp;• Student Care Fee Assistance (SCFA)",
            "&nbsp;&nbsp;• ComCare Short-to-Medium-Term Assistance (SMTA)",
          ].join("\n"),
        ],
      },
      {
        type: "DECORATOR",
        subType: "HEADER",
        safId: "personalDetails",
        safIdx: "5",
        title: "Personal details",
      },
      {
        id: "name",
        type: "PRESET_FIELD",
        subType: "name",
        safId: "name",
        safIdx: "10",
        prefillSource: "myInfo.name",
      },
      {
        type: "SECTION_CONDITIONAL",
        id: "employment",
        safId: "employmentStatus",
        safIdx: "15",
        selector: {
          type: "CUSTOM_FIELD",
          subType: "RADIO",
          id: "employmentStatus",
          prefillSource: "saf.employmentStatus",
          editable: true,
          title: "Employment status",
          options: {
            ES1: "Working",
            ES2: "Looking for work",
            ES3: "Not working",
          },
        },
        result: [
          {
            choice: ["ES1"],
            member: [
              {
                id: "employmentWithCPF",
                type: "CUSTOM_FIELD",
                title: "Are you employed with CPF contribution/taxable income?",
                options: ["Yes", "No"],
                subType: "RADIO",
                safId: "employmentWithCPF",
                safIdx: "5",
              },
              { type: "DECORATOR", subType: "HEADER", title: "For SMTA" },
              {
                type: "DECORATOR",
                subType: "INFO_BLOCK",
                title: [
                  [
                    "The details below are used by the following scheme(s):",
                    "&nbsp;&nbsp;• ComCare Short-to-Medium-Term Assistance (SMTA)",
                  ].join("\n"),
                ],
              },
              {
                type: "CUSTOM_FIELD",
                subType: "MONEY",
                id: "grossMonthlyIncome",
                prefillSource: "saf.grossMonthlyIncome",
                editable: true,
                title: "Gross monthly income",
                description: [
                  "Gross monthly income from work refers to income earned from employment.\n&nbsp;\nFor employees, it refers to the gross monthly wages or salaries before deduction of employee CPF contributions and personal income tax. It comprises basic wages, overtime pay, commissions, tips, other allowances and one-twelfth of annual bonuses.\n&nbsp;\nFor self-employed persons, gross monthly income refers to the average monthly profits from their business, trade or profession (i.e. total receipts less business expenses incurred) before deduction of income tax.",
                ],
              },
            ],
          },
          {
            choice: ["ES2"],
            member: [
              { type: "DECORATOR", subType: "HEADER", title: "For SCFA" },
              {
                type: "DECORATOR",
                subType: "INFO_BLOCK",
                title: [
                  [
                    "The details below are used by the following scheme(s):",
                    "&nbsp;&nbsp;• Student Care Fee Assistance (SCFA)",
                  ].join("\n"),
                ],
              },
              {
                id: "SD17",
                type: "CUSTOM_FIELD",
                title: "Proof of job search",
                subType: "FILE_UPLOAD",
                documents: [
                  {
                    paragraph:
                      "**Screenshot** of your **job seeker profile,** and **submitted job applications** (if any), from one of the following platforms:",
                    indent: [
                      {
                        paragraph:
                          "Career Centre under Workforce Singapore (WSG) or Employment and Employability Institute (e2i)",
                      },
                      {
                        paragraph: "Private employment agency",
                      },
                      {
                        paragraph: "Any other job search portal",
                      },
                    ],
                  },
                  {
                    paragraph: "**Email correspondence** with company on **submitted job applications**",
                  },
                ],
                instructions: ["Please upload **any** of the following documents:"],
              },
            ],
          },
          {
            choice: ["ES3"],
            member: [
              {
                type: "SECTION_CONDITIONAL",
                id: "situation",
                safId: "situation",
                safIdx: "5",
                selector: {
                  type: "CUSTOM_FIELD",
                  subType: "CHECKBOX",
                  id: "situationType",
                  prefillSource: "saf.situationType",
                  editable: true,
                  title: "Describe your current situation",
                  description: ["You may select more than one option."],
                  options: {
                    UR1: "Retiree",
                    UR2: "Homemaker",
                    UR3: "Full-time caregiver",
                    UR4: "Student",
                    UR5: "Medically unfit for work",
                    UR99: "Others",
                  },
                },
                result: [
                  {
                    choice: ["UR99"],
                    member: [
                      {
                        type: "CUSTOM_FIELD",
                        subType: "MULTILINE_TEXT",
                        id: "others",
                        safId: "others",
                        safIdx: "5",
                        prefillSource: "saf.othersReason",
                        editable: true,
                        title: "Tell us more about the situation",
                        maxLength: 300,
                      },
                    ],
                  },
                ],
              },
              {
                id: "unemploymentDoc",
                type: "CUSTOM_FIELD",
                title: "Supporting documents for unemployment",
                subType: "FILE_UPLOAD",
                safId: "unemploymentDoc",
                safIdx: "10",
                documents: [
                  {
                    paragraph:
                      "Any relevant **supporting documents about the situation** (e.g. Letter of Incarceration (LOI), Medical certificate)",
                  },
                ],
                instructions: ["Please upload a copy of:"],
                optional: true,
              },
            ],
          },
        ],
      },
      { type: "DECORATOR", subType: "HEADER", title: "For SMTA" },
      {
        type: "DECORATOR",
        subType: "INFO_BLOCK",
        title: [
          [
            "The details below are used by the following scheme(s):",
            "&nbsp;&nbsp;• ComCare Short-to-Medium-Term Assistance (SMTA)",
          ].join("\n"),
        ],
      },
      {
        type: "DECORATOR",
        subType: "HEADER",
        title: "Education details",
        safIdx: "0",
      },
      {
        id: "highestEducation",
        safIdx: "0",
        type: "CUSTOM_FIELD",
        subType: "DROPDOWN",
        prefillSource: "saf.highestEducation",
        editable: true,
        title: "Highest education level",
        options: {
          0: "No Formal Qualification/Lower Primary",
          1: "Primary",
          2: "Lower Secondary",
          3: "Secondary",
          4: "Post-Secondary (Non-tertiary): General Vocational",
          5: "Polytechnic Diploma Course",
          6: "Professional Qualification and Other Diploma",
          7: "University First Degree",
          8: "University Postgraduate Diploma/Degree",
          9: "Other Education (Non-Award Courses/Miscellaneous)",
          10: "Others",
        },
      },
    ],
  },
  {
    id: "household",
    title: "Household",
    safId: "sect-household",
    safIdx: "10",
    member: [
      {
        type: "DECORATOR",
        subType: "INFO_BLOCK",
        title: [
          [
            "The details below are used by the following scheme(s):",
            "&nbsp;&nbsp;• Student Care Fee Assistance (SCFA)",
            "&nbsp;&nbsp;• ComCare Short-to-Medium-Term Assistance (SMTA)",
          ].join("\n"),
        ],
      },
      {
        type: "SECTION_CONDITIONAL",
        id: "marital",
        safId: "marital",
        safIdx: "5",
        selector: {
          type: "PRESET_FIELD",
          subType: "maritalStatus",
          title: "Marital status",
          id: "maritalStatus",
          prefillSource: "myInfo.maritalStatus",
          editable: true,
        },
        result: [
          {
            choice: ["4"],
            member: [
              {
                type: "CUSTOM_FIELD",
                subType: "DATE",
                safId: "dateOfSeparation",
                safIdx: "5",
                id: "dateOfSeparation",
                title: "Date of separation",
              },
            ],
          },
          {
            choice: ["2"],
            member: [
              {
                type: "DECORATOR",
                subType: "HEADER",
                safId: "spouseDetails",
                safIdx: "5",
                title: "Spouse details",
              },
              {
                type: "PRESET_FIELD",
                subType: "name",
                safId: "spouseName",
                safIdx: "10",
                id: "spouseName",
              },
              {
                type: "PRESET_FIELD",
                subType: "nric",
                safId: "spouseIdNumber",
                safIdx: "15",
                id: "spouseIdNumber",
                consent: true,
                consenterInfo: {
                  name: "spouseName",
                },
              },
              {
                type: "PRESET_FIELD",
                subType: "dob",
                safId: "spouseDob",
                safIdx: "20",
                id: "spouseDob",
              },
              {
                type: "PRESET_FIELD",
                id: "spouseSex",
                subType: "sex",
                safId: "spouseSex",
                safIdx: "25",
              },
              {
                type: "PRESET_FIELD",
                subType: "residentialStatus",
                safId: "spouseResidentialStatus",
                safIdx: "30",
                id: "spouseResidentialStatus",
              },
              {
                type: "PRESET_FIELD",
                subType: "email",
                safId: "spouseEmail",
                safIdx: "35",
                id: "spouseEmail",
                optional: true,
              },
            ],
          },
        ],
      },
      {
        type: "DECORATOR",
        subType: "HEADER",
        safId: "householdMembers",
        safIdx: "10",
        title: "Household members",
      },
      {
        type: "SECTION_CONDITIONAL",
        id: "cohabitation",
        safId: "cohabitation",
        safIdx: "15",
        selector: {
          type: "CUSTOM_FIELD",
          subType: "RADIO",
          id: "cohabitationStatus",
          prefillSource: "saf.cohabitationStatus",
          editable: true,
          title: "Do you live with any household member?",
          description: [
            "This refers to family members who are living in the same registered address (e.g. parents, children). You are not required to provide your spouse details (if any) again.",
          ],
          options: ["Yes", "No"],
        },
        result: [
          {
            choice: ["0"],
            member: [
              {
                type: "MULTI_VALUE",
                id: "familyMember",
                safId: "familyMember",
                safIdx: "5",
                prefillSource: "saf.familyMemberDetails",
                editable: true,
                title: "How many household members (apart from spouse) live with you?",
                maxGroup: 15,
                header: "Household member details",
                group: {
                  type: "CUSTOM_GROUP",
                  subType: "BLANK",
                  id: "familyMemberDetails",
                  title: "Household member",
                  member: [
                    {
                      id: "family",
                      safId: "family",
                      safIdx: "5",
                      prefillSource: "family",
                      editable: true,
                      type: "PRESET_FIELD",
                      consent: true,
                      subType: "family",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "residentialStatus",
                      safId: "residentialStatus",
                      safIdx: "10",
                      id: "residentialStatus",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "email",
                      safId: "email",
                      safIdx: "15",
                      id: "email",
                      prefillSource: "email",
                      editable: true,
                      optional: true,
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "relationshipType",
                      safId: "relationshipType",
                      safIdx: "20",
                      id: "relationshipType",
                      title: "Relationship to applicant",
                      excludedOptions: ["REL001"],
                      prefillSource: "relationshipType",
                      editable: true,
                    },
                    {
                      id: "memberMinorConsent",
                      safId: "memberMinorConsent",
                      safIdx: "25",
                      type: "GLOBAL_GROUP_CONDITIONAL",
                      result: [
                        {
                          choice: [
                            {
                              type: "value",
                              sourcePath: "computedAge",
                              operator: "lessThan",
                              value: "21",
                            },
                          ],
                          member: [
                            {
                              id: "consent",
                              safId: "consent",
                              safIdx: "5",
                              type: "CUSTOM_FIELD",
                              subType: "TNC",
                              title: "Consent / declaration for minors",
                              acknowledge: "I acknowledge and consent to the terms, on behalf of this minor",
                              content: [
                                {
                                  header:
                                    "Consent for collection, use and disclosure of personal information for the application or renewal of application for the subsidy and/or the Start Up Grant",
                                  indent: [
                                    {
                                      paragraph:
                                        "I am the parent/legal guardian of the Child who is under 21 years of age.",
                                    },
                                    {
                                      indent: [
                                        {
                                          paragraph:
                                            "to verify my, my Child and Family’s identity and relationship for the Subsidy and/or the Start Up Grant (“the Subsidies”) and other Services or Scheme;",
                                        },
                                        {
                                          paragraph:
                                            "to determine my, my Child and Family’s eligibility for the Subsidies and other Services or Scheme;",
                                        },
                                        {
                                          paragraph:
                                            "to provide me, my Child and Family with both or any of the Subsidies and other Services or Scheme; and",
                                        },
                                        {
                                          paragraph:
                                            "for data analysis, evaluation and policy-making, for the Subsidies and other Services or Scheme.",
                                        },
                                      ],
                                      paragraph:
                                        "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:",
                                    },
                                    {
                                      paragraph:
                                        "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information, to the extent permitted by the Singapore Public Agencies and Participating Organisations, for the purposes stated in Paragraph 2. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that my personal information will not be shared with non-participating agencies and organisations.",
                                    },
                                    {
                                      paragraph:
                                        "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place.",
                                    },
                                    {
                                      paragraph:
                                        "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form.",
                                    },
                                    {
                                      paragraph:
                                        "This consent shall be governed by and construed in accordance with the laws of the Republic of Singapore.",
                                    },
                                  ],
                                },
                                {
                                  header: "Declaration",
                                  indent: [
                                    {
                                      paragraph:
                                        "I, the undersigned, declare that I have read and understood the content in this Application Form. I confirm that the information that I have provided is true and correct and I furnish the information knowing that I may be liable to criminal prosecution if I have stated any information that I know to be false or not believe to be true.",
                                    },
                                    {
                                      paragraph:
                                        "In the event my application is successful and my Child receives any of the Subsidies which I am applying for, I hereby acknowledge that I may also be liable to make full repayment to the Government of the Subsidies which were provided, should I be found to have provided false or inaccurate information in this form.",
                                    },
                                  ],
                                },
                                {
                                  header: "Other terms",
                                  indent: [
                                    {
                                      indent: [
                                        {
                                          paragraph:
                                            "It shall be my responsibility to stay employed (i.e. to be engaged under a contract or service and receive a salary) to continue to enjoy the Subsidies for my Child. If I am unemployed and intend to seek employment, the onus is on me to actively seek employment.",
                                        },
                                        {
                                          paragraph:
                                            "(only applicable to applications for the Start Up Grant) The Start Up Grant shall only be given once to my Child, and any subsequent applications shall be assessed and granted only in MSF’s sole discretion.",
                                        },
                                        {
                                          paragraph:
                                            "In order to continue enjoying the relevant monthly Subsidy, I must ensure that my Child attends at least 30% (June and December) and 50% (Other Calendar months) of the number of days in which the SCC operates per month. My Child must be present at the centre for at least 3 hours in order to be considered present for the day. If my Child does not meet the minimum attendance rate, the Subsidy paid for the relevant month may be refunded to MSF and I am liable to pay the full SCC monthly fee.",
                                        },
                                        {
                                          paragraph:
                                            " I shall provide the SCC with a one-month notice before withdrawing my Child from the SCC.",
                                        },
                                      ],
                                      paragraph: "I understand and agree to the following:",
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                        {
                          choice: [
                            {
                              type: "value",
                              sourcePath: "computedAge",
                              operator: "greaterThanInclusive",
                              value: "21",
                            },
                          ],
                          member: [
                            {
                              type: "DECORATOR",
                              safId: "consentInfoblock",
                              safIdx: "5",
                              title: [
                                "**Household members’ consent is required for the application.**\nMore details on how your household members can provide consent will be given after the application is submitted.",
                              ],
                              subType: "INFO_BLOCK",
                            },
                          ],
                        },
                      ],
                      selector: {
                        id: "computedAge",
                        type: "CUSTOM_FIELD",
                        subType: "HIDDEN",
                        globalAction: {
                          type: "calculateMultiValueAge",
                          dependsOn: ["household.cohabitation.familyMember.familyMemberDetails"],
                          relativeIds: ["family.memberDob"],
                        },
                      },
                    },
                    { type: "DECORATOR", subType: "HEADER", title: "For SCFA" },
                    {
                      type: "DECORATOR",
                      subType: "INFO_BLOCK",
                      title: [
                        [
                          "The details below are used by the following scheme(s):",
                          "&nbsp;&nbsp;• Student Care Fee Assistance (SCFA)",
                        ].join("\n"),
                      ],
                    },
                    {
                      type: "GROUP_CONDITIONAL",
                      id: "scfaBeneficiary",
                      selector: {
                        type: "CUSTOM_FIELD",
                        subType: "RADIO",
                        id: "isBeneficiary",
                        title: "Are you applying SCFA for this household member?",
                        options: ["Yes", "No"],
                      },
                      result: [
                        {
                          choice: ["0"],
                          member: [
                            {
                              type: "PRESET_FIELD",
                              subType: "school",
                              id: "childSchoolName",
                            },
                            {
                              type: "GROUP_CONDITIONAL",
                              id: "childScc",
                              selector: {
                                type: "CUSTOM_FIELD",
                                subType: "RADIO",
                                id: "sccType",
                                title: "Student care centre (SCC) type",
                                description: [
                                  "**School-based SCC**: only accepts students from primary schools they are located in.",
                                  "**Community-based SCC**: located in community, outside school compounds.",
                                ],
                                options: {
                                  SCC1: "School-based SCC",
                                  SCC2: "Community-based SCC",
                                },
                              },
                              result: [
                                {
                                  choice: ["SCC1"],
                                  member: [
                                    {
                                      type: "CUSTOM_FIELD",
                                      subType: "DYNAMIC_DROPDOWN",
                                      id: "sccCodeSchool",
                                      title: "School-based SCC",
                                      url: "http://localhost:3456/agency-dropdown-options/scfa?query=school",
                                    },
                                  ],
                                },
                                {
                                  choice: ["SCC2"],
                                  member: [
                                    {
                                      type: "CUSTOM_FIELD",
                                      subType: "DYNAMIC_DROPDOWN",
                                      id: "sccCodeCommunity",
                                      title: "Community-based SCC",
                                      url: "http://localhost:3456/agency-dropdown-options/scfa?query=community",
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              type: "CUSTOM_FIELD",
                              subType: "DATE_MONTH",
                              id: "childSubsidyStartMonth",
                              title: "Request for subsidy start month",
                              description: [
                                "You may apply for a renewal of your SCFA support within 4 months of expiry if you meet the eligibility criteria.",
                              ],
                              minMonthRange: 5,
                              maxMonthRange: 4,
                            },
                            {
                              type: "GROUP_CONDITIONAL",
                              id: "relationship",
                              selector: {
                                type: "PRESET_FIELD",
                                subType: "relationshipType",
                                id: "childRelationshipType",
                                title: "Relationship to beneficiary",
                                description: ["Beneficiary refers to the child you are applying for."],
                                allowedOptions: ["REL002", "REL205", "REL206", "REL009"],
                              },
                              result: [
                                {
                                  choice: ["REL002", "REL205", "REL206"],
                                  member: [
                                    {
                                      type: "CUSTOM_FIELD",
                                      subType: "SINGLE_CHECK",
                                      id: "declaration",
                                      title:
                                        "I declare that I am now living with my child(ren) in the same address, and I am caring for the child(ren). If there is another parent with shared care and control of the child(ren), I have sought his/her consent for the SCFA application.",
                                    },
                                    {
                                      type: "PRESET_FIELD",
                                      subType: "relationshipType",
                                      id: "spouseChildRelationshipType",
                                      title: "Spouse’s relationship to beneficiary (if applicable)",
                                      allowedOptions: ["REL002", "REL205", "REL206"],
                                      description: ["Beneficiary refers to the child you are applying for."],
                                      optional: true,
                                    },
                                  ],
                                },
                                {
                                  choice: ["REL009"],
                                  member: [
                                    {
                                      type: "CUSTOM_FIELD",
                                      subType: "FILE_UPLOAD",
                                      id: "SD5",
                                      title: "Guardianship documents",
                                      documents: [
                                        {
                                          paragraph: "**Legal guardian**:",
                                          indent: [
                                            {
                                              paragraph: "Guardianship paper",
                                            },
                                          ],
                                        },
                                        {
                                          paragraph: "**Non-legal guardian**:",
                                          indent: [
                                            {
                                              paragraph:
                                                "Any of the following documents explaining the need to be the non-legal guardian of child such as:",
                                              indent: [
                                                "Parent(s)’ death certificate",
                                                "Police report",
                                                "Prison letter",
                                                "[Annex 6](https://www.msf.gov.sg/docs/default-source/student-care/annex-6-self-declaration-form.pdf): SCFA Self Declaration Form",
                                                "Proof that non-legal guardian is also applicant of approved MOE-FAS application for child",
                                              ],
                                            },
                                          ],
                                        },
                                        {
                                          paragraph: "**Foster parent**:",
                                          indent: [
                                            {
                                              paragraph: "Letter of recommendation from foster care agencies",
                                            },
                                          ],
                                        },
                                      ],
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
      { type: "DECORATOR", subType: "HEADER", title: "For SMTA" },
      {
        type: "DECORATOR",
        subType: "INFO_BLOCK",
        title: [
          [
            "The details below are used by the following scheme(s):",
            "&nbsp;&nbsp;• ComCare Short-to-Medium-Term Assistance (SMTA)",
          ].join("\n"),
        ],
      },
      {
        type: "DECORATOR",
        subType: "HEADER",
        title: "Other family members",
        safIdx: "0",
      },
      {
        type: "SECTION_CONDITIONAL",
        id: "dependent",
        safIdx: "0",
        selector: {
          type: "CUSTOM_FIELD",
          subType: "RADIO",
          id: "dependentStatus",
          title: "Do you have any other family members that live separately but depend on you financially?",
          description: [
            "This refers to family members who are not living in the same registered address (e.g. parents, children). You are not required to provide your spouse and household member details (if any) again.",
          ],
          options: ["Yes", "No"],
        },
        result: [
          {
            choice: ["0"],
            member: [
              {
                type: "MULTI_VALUE",
                id: "familyMember",
                title: "How many of these other family members are there?",
                maxGroup: 15,
                header: "Other family member details ",
                group: {
                  type: "CUSTOM_GROUP",
                  subType: "BLANK",
                  id: "familyMemberDetails",
                  title: "Other family member",
                  member: [
                    {
                      type: "PRESET_FIELD",
                      subType: "name",
                      id: "name",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "nric",
                      id: "nric",
                      consent: true,
                      consenterInfo: {
                        name: "name",
                      },
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "dob",
                      id: "dob",
                    },
                    {
                      type: "PRESET_FIELD",
                      id: "sex",
                      subType: "sex",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "residentialStatus",
                      id: "residentialStatus",
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "email",
                      id: "email",
                      optional: true,
                    },
                    {
                      type: "PRESET_FIELD",
                      subType: "relationshipType",
                      id: "relationshipType",
                      title: "Relationship to applicant",
                      excludedOptions: ["REL001"],
                    },
                    {
                      id: "memberMinorConsent",
                      type: "GLOBAL_GROUP_CONDITIONAL",
                      result: [
                        {
                          choice: [
                            {
                              type: "value",
                              sourcePath: "computedMemberAge",
                              operator: "lessThan",
                              value: "21",
                            },
                          ],
                          member: [
                            {
                              id: "consent",
                              type: "CUSTOM_FIELD",
                              subType: "TNC",
                              title: "Consent / declaration for minors",
                              acknowledge: "I acknowledge and consent to the terms, on behalf of this minor",
                              content: [
                                {
                                  header:
                                    "Consent for collection, use and disclosure of personal information for the application or renewal of application for the subsidy and/or the Start Up Grant",
                                  indent: [
                                    {
                                      paragraph:
                                        "I am the parent/legal guardian of the Child who is under 21 years of age.",
                                    },
                                    {
                                      indent: [
                                        {
                                          paragraph:
                                            "to verify my, my Child and Family’s identity and relationship for the Subsidy and/or the Start Up Grant (“the Subsidies”) and other Services or Scheme;",
                                        },
                                        {
                                          paragraph:
                                            "to determine my, my Child and Family’s eligibility for the Subsidies and other Services or Scheme;",
                                        },
                                        {
                                          paragraph:
                                            "to provide me, my Child and Family with both or any of the Subsidies and other Services or Scheme; and",
                                        },
                                        {
                                          paragraph:
                                            "for data analysis, evaluation and policy-making, for the Subsidies and other Services or Scheme.",
                                        },
                                      ],
                                      paragraph:
                                        "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:",
                                    },
                                    {
                                      paragraph:
                                        "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information, to the extent permitted by the Singapore Public Agencies and Participating Organisations, for the purposes stated in Paragraph 2. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that my personal information will not be shared with non-participating agencies and organisations.",
                                    },
                                    {
                                      paragraph:
                                        "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place.",
                                    },
                                    {
                                      paragraph:
                                        "I have read and understood this consent form fully, including the attached [Terms of Consent](https://go.gov.sg/termsofconsent). I declare that the information that I have provided is accurate at the time I submit this form.",
                                    },
                                    {
                                      paragraph:
                                        "This consent shall be governed by and construed in accordance with the laws of the Republic of Singapore.",
                                    },
                                  ],
                                },
                                {
                                  header: "Declaration",
                                  indent: [
                                    {
                                      paragraph:
                                        "I, the undersigned, declare that I have read and understood the content in this Application Form. I confirm that the information that I have provided is true and correct and I furnish the information knowing that I may be liable to criminal prosecution if I have stated any information that I know to be false or not believe to be true.",
                                    },
                                    {
                                      paragraph:
                                        "In the event my application is successful and my Child receives any of the Subsidies which I am applying for, I hereby acknowledge that I may also be liable to make full repayment to the Government of the Subsidies which were provided, should I be found to have provided false or inaccurate information in this form.",
                                    },
                                  ],
                                },
                                {
                                  header: "Other terms",
                                  indent: [
                                    {
                                      indent: [
                                        {
                                          paragraph:
                                            "It shall be my responsibility to stay employed (i.e. to be engaged under a contract or service and receive a salary) to continue to enjoy the Subsidies for my Child. If I am unemployed and intend to seek employment, the onus is on me to actively seek employment.",
                                        },
                                        {
                                          paragraph:
                                            "(only applicable to applications for the Start Up Grant) The Start Up Grant shall only be given once to my Child, and any subsequent applications shall be assessed and granted only in MSF’s sole discretion.",
                                        },
                                        {
                                          paragraph:
                                            "In order to continue enjoying the relevant monthly Subsidy, I must ensure that my Child attends at least 30% (June and December) and 50% (Other Calendar months) of the number of days in which the SCC operates per month. My Child must be present at the centre for at least 3 hours in order to be considered present for the day. If my Child does not meet the minimum attendance rate, the Subsidy paid for the relevant month may be refunded to MSF and I am liable to pay the full SCC monthly fee.",
                                        },
                                        {
                                          paragraph:
                                            " I shall provide the SCC with a one-month notice before withdrawing my Child from the SCC.",
                                        },
                                      ],
                                      paragraph: "I understand and agree to the following:",
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                        {
                          choice: [
                            {
                              type: "value",
                              sourcePath: "computedMemberAge",
                              operator: "greaterThanInclusive",
                              value: "21",
                            },
                          ],
                          member: [
                            {
                              type: "DECORATOR",
                              title: [
                                "**Household members’ consent is required for the application.**\nMore details on how your household members can provide consent will be given after the application is submitted.",
                              ],
                              subType: "INFO_BLOCK",
                            },
                          ],
                        },
                      ],
                      selector: {
                        id: "computedMemberAge",
                        type: "CUSTOM_FIELD",
                        subType: "HIDDEN",
                        globalAction: {
                          type: "calculateMultiValueAge",
                          dependsOn: ["household.dependent.familyMember.familyMemberDetails"],
                          relativeIds: ["dob"],
                        },
                      },
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
      {
        type: "CUSTOM_FIELD",
        subType: "FILE_UPLOAD",
        id: "hhMemberBankStatement",
        title: "Updated bank statements/bank books of all household and other family members",
        optional: true,
        safIdx: "0",
        instructions: ["Please upload a copy of:"],
        documents: [
          {
            paragraph:
              "Your **bank statements/bank books of all household and other family members for all bank accounts,** if any",
          },
        ],
        additionalDetails: [
          "For each account, your household and other family members’ full name, bank account number, and latest transaction details of at least 1 month before the application date must be shown clearly (e.g. if you are applying on 1 Jun 2024, transactions between 1 May 2024 and 1 Jun 2024 must be reflected).",
          "I understand that submitting incorrect documents may cause delays in processing my application.",
        ],
      },
      {
        type: "CUSTOM_FIELD",
        subType: "FILE_UPLOAD",
        id: "hhMemberIdDoc",
        title: "NRIC/FIN/BC of household and other family members",
        optional: true,
        safIdx: "0",
        instructions: ["Please upload a copy of:"],
        documents: [
          {
            paragraph: "Front and back of the **NRIC/FIN of all adults,** if any",
          },
          {
            paragraph: "**Birth certificate of all children,** if any",
          },
        ],
        additionalDetails: ["Your household and other family member’s name and details must be shown clearly."],
      },
    ],
  },
  {
    id: "additionalDetails",
    title: "Additional details",
    subtitle:
      "Provide additional details about your application, if relevant, to help us better assess your situation.",
    safId: "sect-additional-details",
    safIdx: "15",
    member: [
      {
        type: "DECORATOR",
        subType: "INFO_BLOCK",
        title: [
          [
            "The details below are used by the following scheme(s):",
            "&nbsp;&nbsp;• Student Care Fee Assistance (SCFA)",
          ].join("\n"),
        ],
      },
      {
        type: "CUSTOM_FIELD",
        subType: "MULTILINE_TEXT",
        id: "comments",
        safIdx: "0",
        title: "Comments",
        optional: true,
        maxLength: 300,
        description: [
          "Let us know if you had difficulties providing any document or feedback to help improve the form experience.",
        ],
      },
      { type: "DECORATOR", subType: "HEADER", title: "For SMTA" },
      {
        type: "DECORATOR",
        subType: "INFO_BLOCK",
        title: [
          [
            "The details below are used by the following scheme(s):",
            "&nbsp;&nbsp;• ComCare Short-to-Medium-Term Assistance (SMTA)",
          ].join("\n"),
        ],
      },
      {
        type: "CUSTOM_FIELD",
        subType: "MULTILINE_TEXT",
        id: "concerns",
        safIdx: "0",
        title:
          "What concerns do you have about your household's situation, and what support are you currently receiving?",
        optional: true,
        maxLength: 300,
        description: [
          "It can be about a loss of job or income, difficulty paying for basic needs, or any other concerns. Please also share any support you're currently receiving from other sources e.g. community organisations, non-immediate family members, etc. We will do our best to help you.",
        ],
      },
    ],
  },
];

describe("mergeSchemas", () => {
  it("produces the expected merged sections for SAF", () => {
    const output = mergeSchemas([scfaSchema, smtaSchema]);
    expect(output).toEqual(mergedSafSchema);
  });
});

describe("mergeContacts", () => {
  it("merges contact details from a pre-sorted array of schemes", () => {
    const output = mergeContacts([smtaSchema, scfaSchema]);
    const expected = [
      { title: smtaSchema.schemeName, ...smtaSchema.contacts![0] },
      { title: scfaSchema.schemeName, ...scfaSchema.contacts![0] },
    ];

    expect(output).toEqual(expected);
  });
});

describe("mergeDashboardGuidance", () => {
  it("merges application guidance from a pre-sorted array of schemes", () => {
    const output = mergeDashboardGuidance([smtaSchema, scfaSchema]); // preserve input order

    const sep = "\n\n&nbsp;\n<hr />\n&nbsp;\n\n";
    const expected =
      `**${smtaSchema.schemeName}**\n${smtaSchema.dashboard?.applicationGuidance}` +
      sep +
      `**${scfaSchema.schemeName}**\n${scfaSchema.dashboard?.applicationGuidance}` +
      sep +
      "Apply for multiple schemes in just 1 form\nYour application may take 15 minutes to complete.";

    expect(output).toBe(expected);
  });
});
import Koa from "koa";

import { logger } from "@sgw/common";
import { getAppState, getCachedOrMergedSchema, getMergedStatus } from "./service";

export const SAF_SUPPORTED_SCHEME_CODES = ["smta", "scfa"];

/**
 * Retrieves a merged schema for a bundle of SAF schemes.
 * This function validates the 'codes' query parameter, ensuring it contains at least two unique,
 * supported scheme codes. It then fetches the merged schema for these schemes.
 * If the schema is already cached, it returns the cached version.
 * Otherwise, it merges the schemas from the specified codes.
 * @param ctx - The Koa context object containing the request and response.
 * @throws {Koa.Error} If the 'codes' query parameter is missing, contains less than two unique codes,
 * or includes unsupported scheme codes. It also throws an error if the schema retrieval fails.
 * @returns {Promise<void>} A promise that resolves when the schema is successfully retrieved and set in the response body.
 * The response body will contain a 'schema' property with the merged ApplicationSchema.
 */
export const getSafSchema = async (ctx: Koa.Context): Promise<void> => {
  const schemeCodes = _validateAndParseSafCodes(ctx);

  try {
    const mergedSafSchema = await getCachedOrMergedSchema(schemeCodes);
    ctx.body = { schema: mergedSafSchema };
  } catch (error) {
    logger.error(`Failed to get cached or merged schema for codes: ${schemeCodes}`, error);
    ctx.throw(500);
  }
};

/**
 * Retrieves the application status for a bundle of SAF schemes.
 * This function validates the 'codes' query parameter, ensuring it contains at least two unique,
 * supported scheme codes. It then fetches the merged status for these schemes.
 * @param ctx - The Koa context object containing the request and response.
 * @throws {Koa.Error} If the 'codes' query parameter is missing, contains less than two unique codes,
 * or includes unsupported scheme codes. It also throws an error if the status retrieval fails.
 * @returns {Promise<void>} A promise that resolves when the application status is successfully retrieved
 * and set in the response body. The response body will contain a SgwAppStatusResponse object
 * with the merged application status for the specified schemes.
 */
export const getSafStatus = async (ctx: Koa.Context): Promise<void> => {
  const schemeCodes = _validateAndParseSafCodes(ctx);

  try {
    ctx.body = await getMergedStatus(schemeCodes, ctx.session);
  } catch (error) {
    logger.error(`SAF: failed to retrieve application statuses for schemes: ${schemeCodes}`, error);
    ctx.throw(500);
  }
};

/**
 * Retrieves the SAF application state for supported schemes.
 * This function fetches the application state for each scheme code in the
 * `SAF_SUPPORTED_SCHEME_CODES` array and returns an array of application states.
 * It logs the process and handles errors if the retrieval fails.
 *
 * @param session - The Koa session object, used for authentication and session management.
 * @returns {Promise<SgwAppStateResponse>} A promise that resolves to an array of application states
 * for the supported SAF schemes. Each state includes the scheme code and its application state.
 * @throws {Error} If the application state retrieval fails for any reason.
 */
export const getSafAppState = async (ctx: Koa.Context): Promise<void> => {
  try {
    logger.info("SAF: Retrieving SAF app state for eligibility");
    ctx.body = await getAppState(ctx.session);
  } catch (error) {
    logger.error(`SAF: failed to retrieve app state`, error);
    ctx.throw(500);
  }
};

/**
 * A private helper to validate and parse the 'codes' query parameter.
 * Throws a Koa error if validation fails.
 * @param ctx - The Koa context object.
 * @returns A sorted array of unique, supported scheme codes.
 */
export const _validateAndParseSafCodes = (ctx: Koa.Context): string[] => {
  const schemeCodeQuery = ctx.query.codes;
  if (!schemeCodeQuery) {
    logger.error("SAF: no scheme codes provided");
    ctx.throw(400, "Query parameter 'codes' is required.");
  }

  const schemeCodes = Array.isArray(schemeCodeQuery) ? schemeCodeQuery : schemeCodeQuery.split(",");
  const uniqueSchemeCodes = [...new Set(schemeCodes)].filter(Boolean).sort();

  if (uniqueSchemeCodes.length <= 1) {
    logger.error("SAF: less than two unique scheme codes provided for a bundle");
    ctx.throw(400, "At least two unique scheme codes are required.");
  }

  const unsupportedSchemeCodes = uniqueSchemeCodes.filter((code) => !SAF_SUPPORTED_SCHEME_CODES.includes(code));
  if (unsupportedSchemeCodes.length > 0) {
    const errorMsg = `Unsupported scheme codes provided: ${unsupportedSchemeCodes.join(", ")}`;
    logger.error(`SAF: ${errorMsg}`);
    ctx.throw(400, errorMsg);
  }

  return uniqueSchemeCodes;
};

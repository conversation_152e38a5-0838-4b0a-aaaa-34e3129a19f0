import cors from "@koa/cors";
import Koa from "koa";
import body from "koa-body";
import compress from "koa-compress";
import helmet from "koa-helmet";
import https from "node:https";

import { logger } from "@sgw/common";

import { appConfig } from "../config/app";
import { router as draft } from "../domains/draft/routes";
import { router as file } from "../domains/files/routes";
import { router as healthCheck } from "../domains/healthcheck/routes";
import { router as mcf } from "../domains/my-careers-future/routes";
import { router as ndi } from "../domains/ndi/routes";
import { router as sgw } from "../domains/sgw/routes";
import { router as enquiry } from "../domains/enquiry/routes";
import { router as user } from "../domains/user/routes";
import { router as sequential } from "../domains/sequential/routes";
import { router as saf } from "../domains/saf/routes";
import { cacheControl } from "./middlewares/cache-control";
import { csrfGuard } from "./middlewares/csrf";
import { notFoundHandler } from "./middlewares/notfound-handler";
import { requestTracer } from "./middlewares/request-tracer";
import { sessionGuard, sessionRefresh } from "./middlewares/session";

export const startServer = (): void => {
  const app = new Koa();
  app.keys = appConfig.koa.KOA_KEY.split(",");
  app.proxy = appConfig.app.NODE_ENV !== "local";

  const corsOptions = {
    origin: appConfig.cors.CORS_ALLOW_ORIGIN, //Access-Control-Allow-Origin
    allowMethods: appConfig.cors.CORS_ALLOW_METHODS.split(","), //Access-Control-Allow-Methods
    allowHeaders: appConfig.cors.CORS_ALLOW_HEADERS.split(","), //Access-Control-Allow-Headers
    maxAge: appConfig.cors.CORS_MAX_AGE, //Access-Control-Max-Age
  };
  logger.info(`server cors options`, corsOptions);

  const helmetOptions = {
    contentSecurityPolicy: false,
    xssFilter: false,
    frameguard: false,
  };

  const koaBodyOptions = {
    multipart: true,
    jsonLimit: "10mb",
    formLimit: "10mb",
    textLimit: "10mb",
  };

  app
    .use(healthCheck.routes()) //purely for alb healthcheck. endpoint is never exposed
    .use(requestTracer)
    .use(compress({ threshold: 2048 }))
    .use(cacheControl)
    .use(helmet(helmetOptions))
    .use(cors(corsOptions))
    .use(body(koaBodyOptions))
    .use(enquiry.routes())
    .use(ndi.login.routes())
    .use(sessionGuard)
    .use(sessionRefresh)
    .use(sequential.routes())
    // CSRF guard only applies to browser or internal logged in routes.
    // Previous routes (e.g., sequential) are system to system endpoints.
    .use(csrfGuard())
    .use(ndi.myInfoPrivate.routes())
    .use(user.routes())
    .use(mcf.routes())
    .use(file.routes())
    .use(draft.routes())
    .use(saf.routes())
    .use(sgw.routes())
    .use(notFoundHandler);

  try {
    if (appConfig.https.HTTPS_IS_ENABLE) {
      const httpsConfig = {
        port: appConfig.koa.KOA_PORT,
        options: {
          cert: appConfig.https.HTTPS_SERVER_CERT,
          key: appConfig.https.HTTPS_SERVER_KEY,
          secureProtocol: "TLSv1_2_method",
        },
      };
      startHttpsServer(httpsConfig, app.callback);
    } else {
      app.listen(appConfig.koa.KOA_PORT, () => {
        logger.info(`server started on http://localhost:${appConfig.koa.KOA_PORT}`);
      });
    }
  } catch (error) {
    logger.error(`failed to startup server. Please review the logs`, error);
  }
};

function startHttpsServer(httpsConfig, appCallback) {
  https.createServer(httpsConfig.options, appCallback()).listen(httpsConfig.port, () => {
    logger.info(`https is enabled. server started on https://localhost:${httpsConfig.port}`);
  });
}

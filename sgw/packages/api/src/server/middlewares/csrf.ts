import compose from "koa-compose";
import CSRF from "koa-csrf";
import Koa from "koa";
import { cookieService } from "../cookies";

export const csrfGuard = (): Koa.Middleware<Koa.DefaultState, Koa.Context> =>
  compose([
    // koa-csrf (v3.0.8) README mentioned the use of koa-generic-session middleware prior to mounting koa-csrf.
    // For SGW, we inject session into Koa.Context ourselves instead of relying on other middlewares.
    // Check koa-csrf source code for more info.
    new CSRF({
      invalidTokenMessage: "Invalid CSRF token",
      invalidTokenStatusCode: 400,
      excludedMethods: ["GET", "HEAD", "OPTIONS"],
      disableQuery: false,
    }),
    async (ctx: Koa.Context, next: Koa.Next) => {
      if (ctx.method === "GET") {
        cookieService.setCsrfCookie(ctx, ctx.csrf);
      }

      return next();
    },
  ]);

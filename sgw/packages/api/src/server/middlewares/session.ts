import Koa from "koa";

import { sessionService } from "../session";
import { cookieService } from "../cookies";
import { traceAls } from "./request-tracer";
import { logger } from "@sgw/common";
import { sgwService } from "@sgw/services";

export const sessionGuard = async (ctx: Koa.Context, next: Koa.Next): Promise<void> => {
  let sessionId: string;

  if (ctx.path.startsWith("/sequential")) {
    sessionId = await handleSequentialSession(ctx);
  } else {
    sessionId = await handleSgwSession(ctx);
  }

  const sgwSession = await sessionService.getSession(sessionId);
  if (!sgwSession) {
    logger.warn(`session id ${sessionId} has expired`, { sessionId });
    ctx.throw(401);
  }
  ctx.session = sgwSession;

  const traceId = traceAls.getStore();
  if (traceId) {
    traceId.userId = sgwSession.userId;
    traceId.sessionId = sgwSession.sessionId;
  }

  await next();
};

const handleSequentialSession = async (ctx: Koa.Context): Promise<string> => {
  try {
    const token = ctx.get("x-ccube-token");
    const audience = ctx.origin + ctx.path;

    // validate jwt token
    await sgwService.verifySequentialJwt(token, audience);

    // get sgw sessionId from sequential webhook
    const sessionId = ctx.request.body?.userData?.sso?.sessionId;
    if (!sessionId) {
      logger.error("missing sgw sessionId in sequential webhook request body");
      ctx.throw(400);
    }

    return sessionId;
  } catch (error) {
    logger.error("error processing sequential webhook token", error);
    ctx.throw(401);
  }
};

const handleSgwSession = (ctx: Koa.Context): string => {
  const sessionId = cookieService.getSessionCookie(ctx);

  if (!sessionId) {
    logger.warn("user has no active session. no session id provided from cookie");
    ctx.throw(401);
  }

  return sessionId;
};

export const sessionRefresh = async (ctx: Koa.Context, next: Koa.Next): Promise<void> => {
  try {
    await sessionService.refreshSession(ctx.session);
  } catch (error) {
    logger.error(`error refreshing session for ${ctx.session.sessionId}`, error);
    ctx.throw(500);
  }

  return next();
};

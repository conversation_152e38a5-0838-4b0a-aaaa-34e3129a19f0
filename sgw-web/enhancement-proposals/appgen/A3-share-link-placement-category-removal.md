# EP-A3: Share link layout change and Breadcrumb & category removal

- [Summary](#summary)
- [Motivation](#motivation)
  - [Goals](#goals)
  - [Non-Goals](#non-goals)
- [Proposal](#proposal)
  - [Share Link Placement](#share-link-placement)
  - [Breadcrumbs/Categories Removal](#breadcrumbscategories-removal)
  - [Back Button Removal](#back-button-removal)
  - [Affected Pages](#affected-pages)
  - [Acceptance criteria (Optional)](#acceptance-criteria-optional)
    - [AC 1](#ac-1)
  - [Notes/Constraints/Caveats (Optional)](#notesconstraintscaveats-optional)
  - [Risks and Mitigation](#risks-and-mitigation)
- [Design Details](#design-details)
- [Implementation History](#implementation-history)
- [Alternatives](#alternatives)
- [Infrastructure Needed (Optional)](#infrastructure-needed-optional)

## Summary

The share link placement has been updated across multiple pages. Previously, the
share link was displayed in-line with the back button. It is now displayed
in-line with the title. Additionally, the breadcrumbs/categories have been
removed from the "Scheme Details" and "Service Details" pages. The back button
has also been removed from the "Bookmarks", "Scheme Details", and "Service
Details" pages.

## Motivation

### Goals

- Improve the layout consistency by aligning the share link with the title.
- Simplify the UI by removing breadcrumbs/categories and back buttons where
  they are redundant or unnecessary.

### Non-Goals

- This proposal does not introduce new functionality for the share link or
  other components.

## Proposal

### Share Link Placement

- **Previous Placement**: The share link was displayed in-line with the back
  button.
- **Updated Placement**: The share link is now displayed in-line with the title.

### Breadcrumbs/Categories Removal

- The breadcrumbs/categories have been removed from the following pages:
  - "Scheme Details" page
  - "Service Details" page

### Back Button Removal

- The back button has been removed from the following pages:
  - "Bookmarks" page
  - "Scheme Details" page
  - "Service Details" page

### Affected Pages

- **Care Services Recommender Results Page**
- **Support Recommender Results Page**
- **Bookmark Page**
- **Scheme Details Page**
- **Service Details Page**

### Acceptance criteria (Optional)

#### AC 1

- Jira ticket: [link](https://sgtechstack.atlassian.net/jira/software/c/projects/DCUBESDQLR/boards/782?selectedIssue=DCUBESDQLR-1982)
- The share link is displayed in-line with the title on all affected pages.
- Breadcrumbs/categories are no longer visible on the "Scheme Details" and
  "Service Details" pages.
- The back button is no longer visible on the "Bookmarks", "Scheme Details",
  and "Service Details" pages.

### Notes/Constraints/Caveats (Optional)

- Base component is changed and will affect multiple pages across appgen and
  directory. To ensure layout remains the same for unspecified pages.
- Verify that the share link placement is consistent across all affected pages.

### Risks and Mitigation

- **Risk**: Base component is updated and affects multiple pages
  - **Mitigation**: Where masthead component is used, ensure page UI has no
    no change compared to previous iteration
- **Risk**: Users may rely on breadcrumbs or back buttons for navigation.
  - **Mitigation**: Ensure that alternative navigation methods (e.g., browser
    back button, main navigation) are intuitive and functional.

## Design Details

- The `Masthead` component has been updated to align the share link with the
  title.
- Flexbox (`display: flex`) is now used in the title container to space the
  title text and the share link button, ensuring proper alignment and spacing.
- Breadcrumbs/categories have been removed from the "Scheme Details" and
  "Service Details" pages.
- The back button has been removed from the "Bookmarks", "Scheme Details", and
  "Service Details" pages.

## Implementation History

- 2025-09-01: Initial proposal created

## Alternatives

- Remove back button across all pages where Share link button is present
  - not implemented as designers prefer the back button to still be present on
    CSR and SR results page.

## Infrastructure Needed (Optional)

_No additional infrastructure is required for this enhancement._

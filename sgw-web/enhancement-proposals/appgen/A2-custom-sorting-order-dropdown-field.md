# EP-A2: Custom sorting order for dropdown field

<!--
This is the title of your EP. Keep it short, simple, and descriptive. A good
title can help communicate what the EP is and should be considered as part of
any review.
-->

<!--
A table of contents is helpful for quickly jumping to sections of a EP and for
highlighting any additional information provided beyond the standard EP
template.

To generate TOC and update it easily, install markdown-all-in-one extension
https://marketplace.visualstudio.com/items?itemName=yzhang.markdown-all-in-one.

- As a rule of thumb, set the TOC levels to 2..4 for the extension setting
-->

- [Summary](#summary)
- [Motivation](#motivation)
  - [Goals](#goals)
  - [Non-Goals](#non-goals)
- [Proposal](#proposal)
  - [Acceptance criteria (Optional)](#acceptance-criteria-optional)
    - [AC 1](#ac-1)
  - [Notes/Constraints/Caveats (Optional)](#notesconstraintscaveats-optional)
  - [Risks and Mitigation](#risks-and-mitigation)
- [Design Details](#design-details)
- [Implementation History](#implementation-history)
- [Alternatives](#alternatives)
- [Infrastructure Needed (Optional)](#infrastructure-needed-optional)

## Summary

<!--
In this section, you will briefly explain what the proposal is about, without
going into too much technical details. A reader of this section should be able
to go "Oh, I know **what** you are trying to achieve, though i do not know
exactly **how** you are going to do that, or **how** does this impact the rest
of the system. But hey, at least i know where you're going on this so that i
can make sense of the rest of the EP"

Both in this section and below, follow the guidelines of the documentation
style guide
https://github.com/kubernetes/community/blob/master/contributors/guide/style-guide.md
where it make sense for us. In particular, wrap lines to a reasonable length
(80 characters) using a repository co-pilot prompt: `/80-char-markdown-formatter`
-->

This proposal introduces support for custom dropdown fields to retain the order of
options as defined in the schema. This allows stakeholders to specify the exact order
in which dropdown options appear to users.

## Motivation

<!--
This section is for explicitly listing the motivation. Describe why the change
is important/necessary and the benefits to users. Usually this is for you to
list down more context of what is existing behavior and what's the need for
the change
-->

Stakeholders have requested the ability to control the order of dropdown options for
custom fields. This is especially useful for schemes with multiple entry points or
roles, where the order of options can help users more quickly find and select the
appropriate value based on their context.

### Goals

<!--
List the specific goals of the EP. What is it trying to achieve? How will we
know that this has succeeded?
-->

- When options are defined in a specific order for a custom dropdown field, that order
  should be preserved when the form is rendered.
- Users should see and interact with dropdown options in the order specified in the
  schema.

### Non-Goals

<!--
What is out of scope for this EP? Listing non-goals helps to focus discussion
and make progress.
-->

- This enhancement does not change the format for all dropdown fields. Existing array or
  object formats will continue to be supported.
- The proposal does not enforce a single format for all dropdowns; it simply adds
  support for ordered arrays of objects.

## Proposal

<!--
This is where we get down to the specifics of what the proposal actually is.
This should have enough detail that reviewers can understand exactly what
you're proposing, but should not include things like API designs or
implementation. What is the desired outcome and how do we measure success?.
The "Design Details" section below is for the real nitty-gritty.
-->

Allow custom dropdown fields to use an array of objects to retain the order of options.
Each object in the array will represent a key-value pair, where the key is the value
submitted and the value is the label shown to the user. The dropdown component and
validator will be updated to support this format, ensuring that the selected value is
validated against the option list.

### Acceptance criteria (Optional)

<!--
Detail the things that people will be able to do if this EP is implemented.
Include as much detail as possible so that people can understand the "how" of
the system. The goal here is to make this feel real for users without getting
bogged down.
-->

#### AC 1

- The field "Role to person with disability" in the Guardian / Caregiver section for PWDR
  scheme should display options in the following order for both "As a Person with disability"
  and "As a Parent" entry points:
  1. Parent / Legal guardian
  2. Donee / Deputy
  3. Non-legal guardian

### Notes/Constraints/Caveats (Optional)

<!--
What are the caveats to the proposal?
What are some important details that didn't come across above?
Go in to as much detail as necessary here.
-->

- Each object in the array should contain only a single key-value pair.
- This enhancement is backward compatible—existing custom dropdown fields using object or
  array formats will continue to work.
- The validator logic for checkbox and radio fields is also updated because
  their option formats and validation requirements are similar to dropdowns.
  This ensures consistent handling of ordered options and validation across all
  selection field types.

### Risks and Mitigation

<!--
What are the risks of this proposal, and how do we mitigate? Think broadly.
For example, consider security, UX and performance risk.
-->

- There may be inconsistencies if some dropdowns use the new array format and others use
  the array or object format. Clear documentation and schema validation will help mitigate this.

## Design Details

<!--
This section should contain enough information that the specifics of your
change are understandable. This may include API specs (though not always
required) or even code snippets. If there's any ambiguity about HOW your
proposal will be implemented, this is the place to discuss them.
-->

- `customFieldSelect`: Update zod validation to accept an array of objects, enabling
  ordered options in the schema.
- `dropdownValidator`, `radioValidator`, `checkBoxValidator`: Update the validator to
  handle multiple option formats and ensure synchronization with the backend.
- The custom select fields will render options in the order provided by the schema when an
  array of objects is used.
- Update the PWDR schema to allow specifying dropdown options as an ordered array of objects.

## Implementation History

<!--
Major milestones in the lifecycle of a EP should be tracked in this section.
Major milestones might include:
- Initial proposal created
- Proposal accepted
-->

- 2025-08-13: Initial proposal created

## Alternatives

<!--
What other approaches did you consider, and why did you rule them out? These
do not need to be as detailed as the proposal, but should include enough
information to express the idea and why it was not acceptable.
-->

- No alternatives were pursued, as this approach is backward compatible and minimally
  invasive.

## Infrastructure Needed (Optional)

<!--
Use this section if you need things from the project. Example you need an S3
bucket, you need SQS.
-->

_No additional infrastructure is required for this enhancement._

# EP-A1: Default option for dropdown field

<!--
This is the title of your EP. Keep it short, simple, and descriptive. A good
title can help communicate what the EP is and should be considered as part of
any review.
-->

<!--
A table of contents is helpful for quickly jumping to sections of a EP and for
highlighting any additional information provided beyond the standard EP
template.

To generate TOC and update it easily, install markdown-all-in-one extension
https://marketplace.visualstudio.com/items?itemName=yzhang.markdown-all-in-one.

- As a rule of thumb, set the TOC levels to 2..4 for the extension setting
-->

- [Summary](#summary)
- [Motivation](#motivation)
  - [Goals](#goals)
  - [Non-Goals](#non-goals)
- [Proposal](#proposal)
  - [Acceptance criteria (Optional)](#acceptance-criteria-optional)
  - [Notes/Constraints/Caveats (Optional)](#notesconstraintscaveats-optional)
  - [Risks and Mitigation](#risks-and-mitigation)
- [Design Details](#design-details)
    - [Field Disable Logic](#field-disable-logic)
    - [Data Flow and Component Updates](#data-flow-and-component-updates)
- [Implementation History](#implementation-history)
- [Alternatives](#alternatives)
- [Infrastructure Needed (Optional)](#infrastructure-needed-optional)

## Summary

<!--
In this section, you will briefly explain what the proposal is about, without
going into too much technical details. A reader of this section should be able
to go "Oh, I know **what** you are trying to achieve, though i do not know
exactly **how** you are going to do that, or **how** does this impact the rest
of the system. But hey, at least i know where you're going on this so that i
can make sense of the rest of the EP"

Both in this section and below, follow the guidelines of the documentation
style guide
https://github.com/kubernetes/community/blob/master/contributors/guide/style-guide.md
where it make sense for us. In particular, wrap lines to a reasonable length
(80 characters) using a repository co-pilot prompt: `/80-char-markdown-formatter`
-->

This proposal introduces a new global action type and property to set a default value for dropdown fields. The global action will populate the default value for the field if it exists, and the field will be disabled to prevent user changes. This ensures the correct value is shown and submitted, without user selection.

## Motivation

<!--
This section is for explicitly listing the motivation. Describe why the change
is important/necessary and the benefits to users. Usually this is for you to
list down more context of what is existing behavior and what's the need for
the change
-->

Some schemes have multiple entry points or user roles. Depending on the user's role,certain fields should be set to a specific value. For example, if the user is applying "As a Parent," the "Relationship to person with disability" field should be set to "Parent" automatically. While prefilling is common, there are cases where the value should be fixed and not editable by the user. This enhancement uses a global action to ensure the default value is set and the field is disabled to prevent changes.

### Goals

<!--
List the specific goals of the EP. What is it trying to achieve? How will we
know that this has succeeded?
-->

- When the form initializes, dropdown fields with a default option are
  populated and disabled.
- The value is submitted with the form without user intervention.

### Non-Goals

<!--
What is out of scope for this EP? Listing non-goals helps to focus discussion
and make progress.
-->

- Bulk defaulting for multi value fields is out of scope.
- Dynamic or computed default values are not supported; only static default
  values are handled.

## Proposal

<!--
This is where we get down to the specifics of what the proposal actually is.
This should have enough detail that reviewers can understand exactly what
you're proposing, but should not include things like API designs or
implementation. What is the desired outcome and how do we measure success?.
The "Design Details" section below is for the real nitty-gritty.
-->

Introduce a new global action type (`populateDefaultValue`) and property
(`defaultValue`) in the global action options. The global action will populate
the default value for dropdown fields if it exists. The form generator will then
disable the field, preventing user interaction and changes.

### Acceptance criteria (Optional)

<!--
Detail the things that people will be able to do if this EP is implemented.
Include as much detail as possible so that people can understand the "how" of
the system. The goal here is to make this feel real for users without getting
bogged down.
-->

- The dropdown field is populated and disabled if a default value is provided.
- The default value is submitted with the form.
- Refer to [ticket DCUBESDQLR-1987](https://sgtechstack.atlassian.net/browse/DCUBESDQLR-1987)
  for more details.

### Notes/Constraints/Caveats (Optional)

<!--
What are the caveats to the proposal?
What are some important details that didn't come across above?
Go in to as much detail as necessary here.
-->

- If both prefill and default value are present, the default value will take
  precedence.
- Default values are applied via global action after prefill logic completes.
- Only static default values are supported; dynamic or computed values are not
  handled.

### Risks and Mitigation

<!--
What are the risks of this proposal, and how do we mitigate? Think broadly.
For example, consider security, UX and performance risk.
-->

- In terms of UX, a section will be marked as completed if all its fields are
  either prefilled, have default value, or are optional. While field values
  should be accurate, users might skip reviewing the section since it appears
  completed. However, users will still verify all field values on the review page
  before submission.

## Design Details

<!--
This section should contain enough information that the specifics of your
change are understandable. This may include API specs (though not always
required) or even code snippets. If there's any ambiguity about HOW your
proposal will be implemented, this is the place to discuss them.
-->

- The schema is updated to support a new `defaultValue` property in global action
  options.
- A new global action type, `populateDefaultValue`, is introduced to set the form
  value for the fields when a default value exists.
- When the form is initialized, the global action sets the default value for fields
  if it exists.
- If a default value is set, the field is then disabled to prevent user changes.

#### Field Disable Logic

- The helper function (`isFieldDisabled`) will be updated to include logic that
  disables the field when a default value exists.

#### Data Flow and Component Updates

- Schema validation supports the `defaultValue` key in global action options.
- A new global action type `populateDefaultValue` is created to set the form
  value when a default value exists.
- If a default value exists, the field will be disabled by the helper function
  `isFieldDisabled`.

## Implementation History

<!--
Major milestones in the lifecycle of a EP should be tracked in this section.
Major milestones might include:
- Initial proposal created
- Proposal accepted
-->

- 2025-08-13: Initial proposal created
- 2025-08-21: Updated to use global action for default value population

## Alternatives

<!--
What other approaches did you consider, and why did you rule them out? These
do not need to be as detailed as the proposal, but should include enough
information to express the idea and why it was not acceptable.
-->

- Using `prefillSource` is not suitable, as it is intended for values from
  external sources like MyInfo.
- Creating a separate display-only dropdown component is unnecessary and
  increases maintenance.
- Using `useEffect` in field components only updates the field when it is
  mounted (i.e., when the user visits the page). This may cause confusion if
  the section button shows "x field(s) left" after prefill and validation,
  but no error message appears when the user enters the section.

## Infrastructure Needed (Optional)

<!--
Use this section if you need things from the project. Example you need an S3
bucket, you need SQS.
-->

_No additional infrastructure is required for this enhancement._

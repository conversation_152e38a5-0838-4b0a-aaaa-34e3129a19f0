import { render, waitFor } from "@testing-library/react";
import { TestContext } from "directory/app/DirectoryContext";
import { Route } from "react-router-dom";
import { SchemeDetailsPage } from "directory/pages/schemes/SchemeDetails/SchemeDetailsPage";
import { schemeService } from "directory/app/services/Services";
import { slug } from "commons/util/slug/slug";
import { SchemeContentFactory } from "directory/data/factories/SchemeContentFactory";

describe("SchemeDetailsPage", () => {
  const dummySchemeContent = SchemeContentFactory.new().content;
  const dummyScheme = SchemeContentFactory.new({
    content: {
      ...dummySchemeContent,
      categories: ["CHILDREN_YOUTH"],
      title: "title",
      description: "description"
    }
  });

  const renderWithRouter = (route: string) => {
    window.history.pushState({}, "Test page", route);

    return render(
      <TestContext>
        <Route path="/schemes/:schemeCode">
          <SchemeDetailsPage />
        </Route>
      </TestContext>
    );
  };

  //TODO: to implement after excel sheet with id is fixed
  it("should render specified scheme", async () => {
    jest
      .spyOn(schemeService, "getSchemeBySchemeCode")
      .mockResolvedValue(dummyScheme);
    const { getByText } = renderWithRouter(
      `/schemes/${dummyScheme.schemeCode}`
    );

    expect(
      await waitFor(() => getByText(dummyScheme.content.en?.title as any))
    ).toBeInTheDocument();
    expect(
      getByText(dummyScheme.content.en?.description as any)
    ).toBeInTheDocument();
  });

  it("should render not found", async () => {
    jest
      .spyOn(schemeService, "getSchemeBySchemeCode")
      .mockImplementation(() => {
        throw Error();
      });
    const { getByText } = renderWithRouter("/schemes/NOSUCHSCHEME");
    expect(
      await waitFor(() => getByText(/page not found/i))
    ).toBeInTheDocument();
  });

  it("should render proper slug when its incorrect", async () => {
    jest
      .spyOn(schemeService, "getSchemeBySchemeCode")
      .mockResolvedValue(dummyScheme);
    const { getByText } = renderWithRouter(
      `/schemes/${dummyScheme.schemeCode}/random-slug`
    );

    await waitFor(() => getByText(dummyScheme.content.en?.title as any));
    expect(window.location.pathname).toBe(
      `/schemes/${dummyScheme.schemeCode}/${slug(dummyScheme.title)}`
    );
  });

  it("should render proper slug when not specified", async () => {
    jest
      .spyOn(schemeService, "getSchemeBySchemeCode")
      .mockResolvedValue(dummyScheme);
    const { getByText } = renderWithRouter(
      `/schemes/${dummyScheme.schemeCode}`
    );

    await waitFor(() => getByText(dummyScheme.content.en?.title as any));
    expect(window.location.pathname).toBe(
      `/schemes/${dummyScheme.schemeCode}/${slug(dummyScheme.title)}`
    );
  });

  it("should show closed scheme banner when application deadline is passed", async () => {
    jest.spyOn(schemeService, "getSchemeBySchemeCode").mockResolvedValue({
      ...dummyScheme,
      applicationDeadline: new Date(),
      checkStatus: true
    });

    const { getByText } = renderWithRouter(
      `/schemes/${dummyScheme.schemeCode}/${slug(dummyScheme.title)}`
    );

    expect(
      await waitFor(() =>
        getByText(/This scheme is no longer accepting applications./i)
      )
    ).toBeInTheDocument();
  });
});

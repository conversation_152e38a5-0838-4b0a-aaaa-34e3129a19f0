import DayJS from "dayjs";
import { useEffect, useState } from "react";
import { Container, Row } from "react-grid-system";
import { useLocation, useParams } from "react-router-dom";

import { Link } from "commons/components/Link";
import { Spinner } from "commons/components/Spinner";
import { StatusBlock } from "commons/components/StatusBlock";
import { Text } from "commons/components/Text";
import { WebsiteErrorPage } from "commons/components/WebsiteError";
import { useI18n } from "commons/hooks/i18n";
import { useViewport } from "commons/hooks/viewport";
import { cl, sp } from "commons/styles";
import { Format } from "commons/util/Format";
import { slug } from "commons/util/slug/slug";
import { path } from "directory/app/router/paths";
import { schemeService } from "directory/app/services/Services";
import { Masthead } from "directory/components/Masthead";
import { Page } from "directory/components/Page";
import { MyCol } from "directory/components/ReactGrid/MyCol";
import { SchemeHighlights } from "directory/components/SchemeHighlights";
import { SRBannerButton } from "directory/composites/SRBannerButton";
import {
  SchemeDetailsAccordionWrapper,
  SchemeDetailsContainer,
  SchemeHighlightsWrapper,
  StatusWrapper,
  StyledDiv
} from "directory/pages/schemes/SchemeDetails/SchemeDetailsPage.components";
import {
  SchemeContent,
  SchemeContentTranslationType,
  SchemeDetailsTranslationType
} from "directory/types/Schemes.type";

import { Language } from "directory/types/Language.type";
import { SchemeDetailsAccordion } from "./_components/SchemeDetailsAccordion";

type SchemeDetailsViewProps = {
  hasMissingTranslation: boolean;
  scheme: SchemeContent;
  schemeTranslation: SchemeContentTranslationType;
  schemeDetailsTranslation: SchemeDetailsTranslationType;
};

const SchemeDetailsView = ({
  hasMissingTranslation,
  scheme,
  schemeTranslation,
  schemeDetailsTranslation
}: SchemeDetailsViewProps) => {
  const {
    details,
    lastUpdated,
    checkStatus,
    applicationDeadline,
    applicationUrl
  } = scheme;
  const { title, description } = schemeTranslation;
  const { mode } = useViewport();
  const { LL, locale } = useI18n();

  const showClosedSchemeBanner =
    applicationUrl &&
    checkStatus &&
    applicationDeadline &&
    DayJS().isAfter(applicationDeadline);
  const showMissingTranslationBanner = locale !== "en" && hasMissingTranslation;

  return (
    <Page
      title={LL.metadata.title.scheme_details_page({ SCHEME_NAME: title })}
      description={LL.metadata.description.scheme_details_page({
        SCHEME_DESCRIPTION: description
      })}
      ogURL={window.location.href}
    >
      <Masthead
        mastheadTitle={
          <MyCol sm={12} md={9}>
            <Text typeScale="h3">{title}</Text>
          </MyCol>
        }
        mastheadSubtitle={
          <MyCol sm={12} md={9}>
            <Text typeScale="t2">{description}</Text>
          </MyCol>
        }
        extraBottomPadding={{ mobile: sp._16, desktop: "0" }}
        shareUrl={window.location.href}
        showMore={mode === "desktop" ? false : true}
      />
      <SchemeDetailsContainer>
        <Container fluid style={{ padding: 0 }}>
          {(showClosedSchemeBanner || showMissingTranslationBanner) && (
            <StatusWrapper>
              {showClosedSchemeBanner && (
                <StatusBlock
                  status="notice"
                  Description={
                    <Text typeScale="b2">
                      {LL.scheme_details_page.closed_scheme_banner.description_1()}
                      <Link
                        to={applicationUrl}
                        linkType="internal"
                        typeScale="b2"
                        style={{ color: cl.primary.blue.dark }}
                      >
                        {LL.scheme_details_page.closed_scheme_banner.description_2()}
                      </Link>
                      {LL.scheme_details_page.closed_scheme_banner.description_3()}
                    </Text>
                  }
                />
              )}
              {showMissingTranslationBanner && (
                <StatusBlock
                  status="info"
                  Description={LL.translation_in_progress_status_block.you_may_see_some_information_in_English_as_we_are_currently_working_on_the_translations()}
                />
              )}
            </StatusWrapper>
          )}
          <Row gutterWidth={mode === "desktop" ? 32 : 0} direction="row">
            <MyCol
              sm={12}
              md={4}
              style={{ marginBottom: mode === "desktop" ? 0 : sp._28 }}
            >
              <SchemeHighlightsWrapper>
                <SchemeHighlights
                  scheme={scheme}
                  schemeTranslation={schemeTranslation}
                />
              </SchemeHighlightsWrapper>
            </MyCol>
            <MyCol sm={12} md={8}>
              {details && (
                <SchemeDetailsAccordionWrapper>
                  <SchemeDetailsAccordion
                    schemeId={scheme.schemeCode}
                    details={schemeDetailsTranslation}
                  />
                </SchemeDetailsAccordionWrapper>
              )}
              {lastUpdated && (
                <StyledDiv>
                  <Text typeScale="b2">
                    {LL.scheme_details_page.scheme_last_updated() +
                      Format.date(lastUpdated)}
                  </Text>
                </StyledDiv>
              )}
            </MyCol>
          </Row>
        </Container>
        <SRBannerButton
          className="display-none-print"
          style={{ marginTop: sp._48 }}
          to={path.eligibility.index}
          title={LL.banner_button.support_recommender()}
          subtitle={LL.banner_button.find_out_which_support_schemes_might_be_relevant_for_you()}
        />
      </SchemeDetailsContainer>
    </Page>
  );
};

const SchemeDetailsPage = () => {
  const [scheme, setScheme] = useState<SchemeContent>();
  const [
    schemeTranslation,
    setSchemeTranslation
  ] = useState<SchemeContentTranslationType>();
  const [
    schemeDetailsTranslation,
    setSchemeDetailsTranslation
  ] = useState<SchemeDetailsTranslationType>({});

  const { locale, setLocale } = useI18n();
  const location = useLocation();
  const { schemeCode } = useParams<{ schemeCode: string }>();

  const [hasMissingTranslation, setHasMissingTranslation] = useState(false);
  const [pageNotExist, setPageNotExist] = useState<boolean>(false);
  /**
   * This is to save the api calls being made since the locale used
   * could come from the query params or context. Here we want to control
   * which to use before we trigger the effect to run
   */
  const [isFirstRun, setFirstRun] = useState<boolean>(true);

  // -----------------------------------------------------------------------------
  // EFFECTS
  // -----------------------------------------------------------------------------
  useEffect(() => {
    const localeInQueryParams = getLocaleFromQueryParams();
    if (localeInQueryParams) {
      setLocale(localeInQueryParams);
    }

    setFirstRun(false);
  }, []);

  useEffect(() => {
    if (locale && !isFirstRun) {
      getDetails();
    }
  }, [schemeCode, locale, isFirstRun]);

  // -----------------------------------------------------------------------------
  // HELPERS
  // -----------------------------------------------------------------------------
  const getDetails = async () => {
    try {
      const retrievedScheme = await schemeService.getSchemeBySchemeCode(
        schemeCode,
        locale
      );
      window.history.replaceState(
        window.history.state,
        "",
        `/schemes/${schemeCode}/${slug(retrievedScheme.title_en)}`
      );

      setScheme(retrievedScheme);
      setSchemeTranslation(
        retrievedScheme.content[locale] ?? retrievedScheme.content
      );

      setSchemeDetailsTranslation(
        retrievedScheme.details[locale] ?? retrievedScheme.details
      );
      setHasMissingTranslation(
        retrievedScheme.details[locale] && retrievedScheme.content[locale]
          ? false
          : true
      );
    } catch {
      setPageNotExist(true);
    }
  };

  function getLocaleFromQueryParams(): Language {
    const queryParams = new URLSearchParams(location.search);

    return queryParams.get("locale") as Language; // Key that is passed in from cards
  }

  // -----------------------------------------------------------------------------
  // RENDERS
  // -----------------------------------------------------------------------------
  if (pageNotExist) {
    return <WebsiteErrorPage error="404" />;
  }

  return scheme && schemeTranslation ? (
    <SchemeDetailsView
      hasMissingTranslation={hasMissingTranslation}
      scheme={scheme}
      schemeTranslation={schemeTranslation}
      schemeDetailsTranslation={schemeDetailsTranslation}
    />
  ) : (
    <Spinner />
  );
};

export { SchemeDetailsPage, SchemeDetailsView };

import cloneDeep from "lodash/cloneDeep";
import React, { useCallback, useEffect, useState } from "react";
import { Col, Row } from "react-grid-system";

import { Icon } from "commons/components/Icon";
import { Text } from "commons/components/Text";
import { ConstElemIds, ConstServiceFriendlyId } from "commons/constants";
import { useI18n } from "commons/hooks/i18n";
import { useViewport } from "commons/hooks/viewport";
import { sgwWogaa } from "commons/services/wogaa";
import { sp } from "commons/styles";
import { Format } from "commons/util/Format";
import { FeatureGate } from "directory/app/featuregate/FeatureGate";
import { path } from "directory/app/router/paths";
import { locationService } from "directory/app/services/Services";
import EmptyStateIllustration from "directory/assets/images/EmptyState.svg";
import { Masthead } from "directory/components/Masthead";
import { Page } from "directory/components/Page";
import { MyCol } from "directory/components/ReactGrid/MyCol";
import { StatusBlock } from "directory/components/StatusBlock";
import mapPostalCodeToDistrictNum from "directory/data/caregiving/postalDistrictNumMapper";
import { LocationCard } from "directory/pages/map/components/LocationCard/LocationCard";
import {
  HelplineWrapper,
  SchemeHighlightsWrapper,
  StyledDiv
} from "directory/pages/schemes/SchemeDetails/SchemeDetailsPage.components";
import {
  AdditionalDetailsWrapper,
  ServiceDetailsContainer,
  ServiceLocationContainer,
  ServiceProvidersHeader
} from "directory/pages/services/ServiceDetails/_components/components";
import { ServiceDetailsAccordion } from "directory/pages/services/ServiceDetails/_components/ServiceDetailsAccordion";
import {
  generateLabels,
  ServiceHighlights
} from "directory/pages/services/ServiceDetails/_components/ServiceHighlights";
import { RecommendedLocationRes } from "directory/services/api/APIService.types";
import {
  OneMapAddress,
  OneMapSearchResults
} from "directory/services/onemap/OneMapService.types";
import {
  SvcFilterOptions,
  BundleFriendlyIds,
  Location,
  PhysicalLocation,
  ServiceDetails,
  ServiceFilters,
  SortByTypes
} from "directory/services/services/types";

import { Link } from "commons/components/Link";
import { TextButton } from "../../../../commons/components/TextButton";
import { Highlights } from "../../../components/Highlights/Highlights";
import { NoResultsListing } from "../../../composites/NoResultsListing";
import { HelplineCard } from "./_components/HelplineCard/HelplineCard";
import { ServiceProviderFilters } from "./Filters/AllFilters";
import { FiltersLeftWrapper } from "./Filters/Filters.components";
import { LocationFilters } from "./Filters/LocationFilters/LocationFilters";
import { ShowMoreLocations } from "./Filters/LocationFilters/ShowMoreLocations/ShowMoreLocations";
import {
  filterLocations,
  getFilteredSP,
  getPhysicalAndHomeBasedLocations,
  LocationWithLatLngAndDistance,
  sortLocations,
  sortSPByDistance
} from "./ServiceDetailsView.helpers";
import { SortBy } from "./SortBy/SortBy";

const SP_HEADER_ID = "sp-header";
type SvcDetailsViewProps = {
  service: ServiceDetails;
  /**
   * Control visibility of service provider filter(not including search & sorter)
   * @default true
   */
  withFilter?: boolean;
  filtersOptions?: SvcFilterOptions;
};
const SvcDetailsView = ({
  service,
  withFilter = true,
  filtersOptions
}: SvcDetailsViewProps) => {
  const { LL, locale } = useI18n();
  const { mode } = useViewport();
  const [filteredLocations, setFilteredLocations] = useState<Location[]>([]);
  const [spFilters, setSPFilters] = useState<ServiceFilters>({});
  const [sortBy, setSortBy] = useState<SortByTypes>("RECENTLY_UPDATED");
  const [isSearchApplied, setIsSearchApplied] = useState<boolean>(false);
  const [locations, setLocations] = useState<Location[]>([]);
  const [recommendedLocationIds, setRecommendedLocationIds] = useState<
    string[] | undefined
  >();
  const [
    locationSelected,
    setLocationSelected
  ] = useState<OneMapSearchResults>();
  const [filteredByDistance, setFilteredByDistance] = useState<boolean>(false);
  const [userAddress, setUserAddress] = useState<OneMapAddress>();
  const [
    shouldOverrideAndDisable,
    setShouldOverrideAndDisable
  ] = useState<boolean>(false);
  const onSortByChanged = (
    sortBy: SortByTypes,
    location?: OneMapSearchResults
  ) => {
    setSortBy(sortBy);
    setShouldOverrideAndDisable(true);
    if (location) setLocationSelected(location);
    const sortedLocations = sortLocations(sortBy, filteredLocations, {
      lat: parseFloat(location?.value.LATITUDE ?? "0"),
      lng: parseFloat(location?.value.LONGITUDE ?? "0")
    });
    setLocations([...sortedLocations]);
  };
  const onFiltersApplied = (filters) => {
    setSPFilters(filters);
    /**
     * default sorter when filter is applied
     */
    setSortBy("RECENTLY_UPDATED");
  };
  const onSearchLocationApplied = (searchLocation?: OneMapSearchResults) => {
    // Filter location based on location filters
    if (!searchLocation) {
      setShouldOverrideAndDisable(true);
      setIsSearchApplied(!!searchLocation);
      setFilteredByDistance(false);
      setFilteredLocations(filterLocations(locations, spFilters));
      setUserAddress(undefined);
      return;
    }
    setShouldOverrideAndDisable(true);
    setIsSearchApplied(!!searchLocation);
    setFilteredByDistance(true);
    setUserAddress(searchLocation?.value);
    const filteredSP = getFilteredSP(
      getPhysicalAndHomeBasedLocations(filteredLocations),
      mapPostalCodeToDistrictNum(searchLocation?.value.POSTAL ?? ""),
      service.code === ConstServiceFriendlyId.AAC
    );
    const sortedSP = sortSPByDistance(filteredSP, {
      lat: parseFloat(searchLocation?.value.LATITUDE ?? "0"),
      lng: parseFloat(searchLocation?.value.LONGITUDE ?? "0")
    });
    // Set final location
    setFilteredLocations(sortedSP);
  };

  const onUserAddressChange = async (
    locations: Location[],
    postalCode?: string
  ) => {
    const recommendedLocations = await fetchRecommendedLocations(postalCode);
    if (!recommendedLocations?.length) return;
    const sortedLocations = sortRecommendedLocationToFront(
      locations,
      recommendedLocations
    );
    setFilteredLocations(sortedLocations);
  };

  const handleScrollSpHeader = useCallback(() => {
    window.location.hash = ""; // only hashchange event trigger scrolling
    window.location.hash = `#${SP_HEADER_ID}`;
  }, []);

  const fetchRecommendedLocations = async (
    postalCode?: string
  ): Promise<RecommendedLocationRes | undefined> => {
    if (!postalCode) {
      setRecommendedLocationIds(undefined);
      return;
    }

    const data = await locationService.getRecommendedLocationsByPostal({
      serviceId: service.code,
      postalCode
    });
    setRecommendedLocationIds(
      data?.length ? data.map(({ friendlyId }) => friendlyId) : undefined
    );

    return data;
  };

  const sortRecommendedLocationToFront = (
    locations: Location[],
    recommendedLocations: RecommendedLocationRes
  ): Location[] => {
    const sortedLocations = cloneDeep(locations);

    const recommendedLocationsIds = recommendedLocations.map(
      ({ friendlyId }) => friendlyId
    );
    const frontLocations = sortedLocations.filter((location) =>
      recommendedLocationsIds.includes(location.codeId)
    );
    if (!frontLocations.length) return sortedLocations;

    return [
      /* recommended locations */
      ...frontLocations,
      /* sorted locations */
      ...sortedLocations.filter(
        (loc) => !recommendedLocationsIds.includes(loc.codeId)
      )
    ];
  };

  const renderNoSPView = () => {
    return (
      <NoResultsListing
        style={{ marginTop: sp._24 }}
        title={LL.service_details_page.location_card.no_results_found()}
        imageSrc={EmptyStateIllustration}
      />
    );
  };

  const onViewServiceLocation = (location: Location) => {
    if (location.accessTypes.includes("PHYSICAL"))
      window.open(
        `${window.location.origin}${path.location.map}?serviceId=${
          service.code
        }&goTo=${(location as PhysicalLocation).lat},${
          (location as PhysicalLocation).lng
        }`,
        "_blank",
        "noopener noreferrer"
      );
  };

  useEffect(() => {
    // Filter location based on main filters
    const finalLocations = filterLocations(locations, spFilters);
    setFilteredLocations(finalLocations);
    setShouldOverrideAndDisable(false);
    setFilteredByDistance(false);
    setUserAddress(undefined);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [spFilters, locations]);

  useEffect(() => {
    if (!window.location.hash) return;

    setTimeout(handleScrollSpHeader, 300);
  }, [handleScrollSpHeader]);

  useEffect(() => {
    onUserAddressChange(filteredLocations, userAddress?.POSTAL || "");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userAddress]);

  useEffect(() => {
    setLocations(sortLocations(sortBy, service.locations, { lat: 0, lng: 0 }));
  }, [service, sortBy]);

  const hasAdditionalDetails =
    service.details.helplineNumber ||
    service.details.helplineOperatingHours ||
    generateLabels(service, LL).length > 0;

  const shouldShowTranslationBanner = service.language !== locale;
  const fscSvcCodes = [
    ConstServiceFriendlyId.FSCF,
    ConstServiceFriendlyId.SFPFFF
  ];
  const disallowedSvcCodes = [ConstServiceFriendlyId.TST, ...fscSvcCodes];
  const shouldShowSPSection =
    service.locations.length > 0 && !disallowedSvcCodes.includes(service.code);

  return (
    <Page
      title={LL.metadata.title.svc_details_page({ SVC_NAME: service.title })}
      description={LL.metadata.description.svc_details_page({
        SVC_DESCRIPTION: service.description
      })}
      ogURL={window.location.href}
    >
      <Masthead
        mastheadTitle={
          <MyCol sm={12} md={9}>
            <Text typeScale="h3">{service.title}</Text>
          </MyCol>
        }
        mastheadSubtitle={
          <MyCol sm={12} md={9}>
            <Text typeScale="t2">{service.description}</Text>
          </MyCol>
        }
        shareUrl={window.location.href}
        showMore={mode !== "desktop"}
        extraBottomPadding={{ mobile: sp._16, desktop: "0" }}
      />
      <ServiceDetailsContainer style={{ padding: 0 }}>
        {shouldShowTranslationBanner && (
          <StatusBlock
            description={LL.translation_in_progress_status_block.you_may_see_some_support_in_English_as_we_are_working_on_the_translations_of_the_recently_added_social_services()}
            style={{
              marginBottom: mode === "desktop" ? sp._48 : sp._16,
              marginTop: mode === "desktop" ? 0 : `-${sp._32}`
            }}
          />
        )}
        <Row gutterWidth={mode === "desktop" ? 32 : 0} direction="row">
          {hasAdditionalDetails && (
            <MyCol
              sm={12}
              md={4}
              style={{ marginBottom: mode === "desktop" ? 0 : sp._28 }}
            >
              <AdditionalDetailsWrapper
                isFirstChild={!shouldShowTranslationBanner}
              >
                <SchemeHighlightsWrapper>
                  <ServiceHighlights serviceDetails={service} />
                </SchemeHighlightsWrapper>
                {service.details.helplineNumber && (
                  <HelplineWrapper>
                    <HelplineCard
                      helplineNumberText={service.details.helplineNumber}
                      helplineHoursText={service.details.helplineOperatingHours}
                    />
                  </HelplineWrapper>
                )}
                {(shouldShowSPSection ||
                  fscSvcCodes.includes(service.code)) && (
                  <SchemeHighlightsWrapper
                    style={{ marginTop: mode === "mobile" ? sp._16 : sp._24 }}
                  >
                    <Highlights
                      title={LL.service_details_page.service_providers()}
                      labels={
                        <div style={{ display: "flex", paddingTop: sp._12 }}>
                          <div style={{ display: "flex" }}>
                            <Icon name="mapMarker-grey" size="m" />
                            {fscSvcCodes.includes(service.code) ? (
                              <Link
                                className="link"
                                to={
                                  service.code === ConstServiceFriendlyId.FSCF
                                    ? "https://www.msf.gov.sg/our-services/directories#familytab"
                                    : "https://www.msf.gov.sg/our-services/directories#famtab"
                                }
                                linkType="external"
                                typeScale={mode === "desktop" ? "b2" : "b1"}
                                showExtLinkIcon
                              >
                                {LL.service_details_page.location_card.search_placeholder()}
                              </Link>
                            ) : (
                              <TextButton
                                typeScale="bt1"
                                style={{
                                  paddingLeft: sp._8,
                                  textAlign: "left"
                                }}
                                onClick={handleScrollSpHeader}
                              >
                                {LL.service_details_page.location_card.search_placeholder()}
                              </TextButton>
                            )}
                          </div>
                        </div>
                      }
                    />
                  </SchemeHighlightsWrapper>
                )}
              </AdditionalDetailsWrapper>
            </MyCol>
          )}

          <MyCol sm={12} md={hasAdditionalDetails ? 8 : 12}>
            <ServiceDetailsAccordion
              serviceId={service.code}
              details={service.details}
            />
            {service.infoLastUpdated && (
              <StyledDiv>
                <Text typeScale="b2">
                  {LL.service_details_page.information_last_updated_on({
                    date: Format.date(service.infoLastUpdated)
                  })}
                </Text>
              </StyledDiv>
            )}
          </MyCol>
        </Row>
      </ServiceDetailsContainer>
      {shouldShowSPSection && (
        <ServiceLocationContainer>
          <ServiceProvidersHeader id={SP_HEADER_ID}>
            <Text typeScale="h3">
              {LL.service_details_page.service_providers()}
            </Text>
            <Row>
              <Col md={8} xs={12}>
                <FiltersLeftWrapper>
                  <LocationFilters
                    placeholder={LL.service_details_page.location_card.search_placeholder()}
                    onSearchLocationApplied={onSearchLocationApplied}
                  />
                </FiltersLeftWrapper>
              </Col>
              <Col
                md={4}
                xs={12}
                style={{ paddingTop: mode === "mobile" ? sp._16 : undefined }}
              >
                <Row
                  gutterWidth={8}
                  justify={mode === "desktop" ? "end" : "start"}
                >
                  <Col
                    hidden={mode === "mobile" && !withFilter}
                    width={mode === "desktop" ? "auto" : undefined}
                    md={6}
                    xs={6}
                  >
                    {withFilter && (
                      <ServiceProviderFilters
                        onFiltersApplied={onFiltersApplied}
                        filtersOptions={filtersOptions}
                        filters={spFilters}
                        serviceCategories={service.category}
                      ></ServiceProviderFilters>
                    )}
                  </Col>

                  <Col
                    width={mode === "desktop" ? "auto" : undefined}
                    md={6}
                    xs={6}
                  >
                    <SortBy
                      sortBy={sortBy}
                      onSortByApplied={onSortByChanged}
                      userLocation={locationSelected}
                      disableDistance={true}
                      shouldOverrideAndDisable={shouldOverrideAndDisable}
                      hasSortByDistance={filteredByDistance}
                      disabled={isSearchApplied}
                    />
                  </Col>
                </Row>
              </Col>
            </Row>
            <FeatureGate feature={"R2_FEATURE"}>
              {filteredByDistance ? (
                <Text typeScale="st3">
                  {LL.service_details_page.location_card.service_providers_nearest_to(
                    { ADDRESS: userAddress?.ADDRESS }
                  )}
                </Text>
              ) : (
                <></>
              )}
            </FeatureGate>
          </ServiceProvidersHeader>
          {filteredByDistance ? (
            filteredLocations.length > 0 ? (
              <>
                <ShowMoreLocations
                  items={filteredLocations as LocationWithLatLngAndDistance[]}
                  initialLocationCount={3}
                  hasShowLess={true}
                  showRenderFullList={false}
                  render={(
                    location: LocationWithLatLngAndDistance,
                    index: number
                  ) => (
                    <LocationCard
                      elemIds={{
                        locationName: `${ConstElemIds.SERVICE_DETAIL_LOCATION_CARD_NAME}_${index}`,
                        viewInMapButton: `${ConstElemIds.SERVICE_DETAIL_LOCATION_CARD_VIEW_IN_MAP_BUTTON}_${index}`,
                        showMoreDetailButton: `${ConstElemIds.SERVICE_DETAIL_LOCATION_CARD_SHOW_MORE_DETAIL_BUTTON}_${index}`
                      }}
                      isRecommended={recommendedLocationIds?.includes(
                        location.codeId
                      )}
                      isShowViewOnMap={true}
                      onClickEmail={() =>
                        sgwWogaa.trackEvent(
                          "Service Details",
                          "email",
                          location.codeId
                        )
                      }
                      onClickPhone={() =>
                        sgwWogaa.trackEvent(
                          "Service Details",
                          "phone",
                          location.codeId
                        )
                      }
                      onClickWebsiteURL={() =>
                        sgwWogaa.trackEvent(
                          "Service Details",
                          "websiteUrl",
                          location.codeId
                        )
                      }
                      style={{ minHeight: "100%", minWidth: "100%" }}
                      location={location}
                      distance={location.distance}
                      onViewServiceLocation={onViewServiceLocation}
                    />
                  )}
                />
              </>
            ) : (
              renderNoSPView()
            )
          ) : filteredLocations.length > 0 ? (
            <Row gutterWidth={mode === "desktop" ? 30 : 0}>
              {filteredLocations.map((it, index) => (
                <Col key={index} md={4} style={{ marginTop: sp._24 }}>
                  <LocationCard
                    elemIds={{
                      locationName: `${ConstElemIds.SERVICE_DETAIL_LOCATION_CARD_NAME}_${index}`,
                      viewInMapButton: `${ConstElemIds.SERVICE_DETAIL_LOCATION_CARD_VIEW_IN_MAP_BUTTON}_${index}`,
                      showMoreDetailButton: `${ConstElemIds.SERVICE_DETAIL_LOCATION_CARD_SHOW_MORE_DETAIL_BUTTON}_${index}`
                    }}
                    isRecommended={recommendedLocationIds?.includes(it.codeId)}
                    isShowViewOnMap={true}
                    onClickEmail={() =>
                      sgwWogaa.trackEvent(
                        `${
                          service.bundleFriendlyIds?.includes(
                            BundleFriendlyIds.CAREGIVING
                          )
                            ? "CG"
                            : ""
                        }Service Details`,
                        "email",
                        it.codeId
                      )
                    }
                    onClickPhone={() =>
                      sgwWogaa.trackEvent(
                        `${
                          service.bundleFriendlyIds?.includes(
                            BundleFriendlyIds.CAREGIVING
                          )
                            ? "CG"
                            : ""
                        }Service Details`,
                        "phone",
                        it.codeId
                      )
                    }
                    onClickWebsiteURL={() =>
                      sgwWogaa.trackEvent(
                        `${
                          service.bundleFriendlyIds?.includes(
                            BundleFriendlyIds.CAREGIVING
                          )
                            ? "CG"
                            : ""
                        }Service Details`,
                        "websiteUrl",
                        it.codeId
                      )
                    }
                    style={{ minHeight: "100%", minWidth: "100%" }}
                    location={it}
                    onViewServiceLocation={onViewServiceLocation}
                  />
                </Col>
              ))}
            </Row>
          ) : (
            renderNoSPView()
          )}
        </ServiceLocationContainer>
      )}
    </Page>
  );
};

export type { SvcDetailsViewProps };
export { SvcDetailsView };

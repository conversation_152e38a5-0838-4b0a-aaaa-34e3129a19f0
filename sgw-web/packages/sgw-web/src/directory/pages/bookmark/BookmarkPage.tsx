import { useContext } from "react";

import { Container } from "commons/components/Container";
import { BookmarkLine, useIconSize } from "commons/components/Icon";
import { withContext } from "commons/hoc/withContext";
import { useI18n } from "commons/hooks/i18n";
import { gradientCl, sp } from "commons/styles";
import { FeatureGate } from "directory/app/featuregate/FeatureGate";
import { path } from "directory/app/router/paths";
import BookmarkEmptyState from "directory/assets/images/BookmarkEmptyState.svg";
import { BackToTop } from "directory/components/BackToTop/BackToTop";
import { Page } from "directory/components/Page";
import { GenericSubtitle } from "directory/composites/CategoryCheckerMasthead/CategoryCheckerMasthead.components";
import { SRBannerButton } from "directory/composites/SRBannerButton";
import { formatQueryParams } from "directory/hooks/useFilters.helpers";
import { Listing } from "directory/pages/categories/components/Listing/Listing";
import { Filters as SvcFilters } from "directory/pages/services/ServiceDetails/Filters/Filters";
import { Filters as SchemesFilters } from "directory/pages/services/ServiceDetails/Filters/SchemeFilters/SchemeFilters";
import { SortBy } from "directory/pages/services/ServiceDetails/SortBy/SortBy";

import { Text } from "../../../commons/components/Text";
import { useViewport } from "../../../commons/hooks/viewport";
import { Masthead } from "../../components/Masthead";
import { StatusBlock } from "../../components/StatusBlock";
import { NoResultsListing } from "../../composites/NoResultsListing";
import { NoListingWrapper } from "../categories/components/Listing/components/Listing.components";
import { desktopStyles, mobileStyles } from "./BookmarkPage.components";
import {
  BookmarkPageContext,
  BookmarkPageContextProvider
} from "./BookmarkPage.context";

const BookmarkPageView = () => {
  const { LL } = useI18n();
  const {
    activeTab,
    setActiveTab,
    allFilters,
    setAllFilters,
    setSortBy,
    sortBy,
    schemes: categorySchemes,
    services,
    setSchemeSortBy,
    schemeSortBy,
    supportCount,
    allSchemesIds,
    allServicesIds,
    svcFilterOptions
  } = useContext(BookmarkPageContext);
  const { mode } = useViewport();

  const isShareable = allSchemesIds.length + allServicesIds.length > 0;
  return (
    <Page
      title={LL.metadata.title.bookmark_page()}
      ogURL={window.location.href}
    >
      <Masthead
        mastheadTitle={<Text typeScale="h3">{LL.bookmark_page.title()}</Text>}
        background={gradientCl.status.orange.lighter}
        shareUrl={
          isShareable
            ? `${window.location.origin}${
                path.shared.index
              }?${formatQueryParams("", {
                serviceIds: allServicesIds,
                schemeIds: allSchemesIds
              })}`
            : undefined
        }
        mastheadSubtitle={
          <GenericSubtitle
            number={supportCount}
            genericSubtitleTextFront={LL.bookmark_page.subtitle_front()}
            genericSubtitleTextBack={LL.bookmark_page.subtitle_back()}
          />
        }
      />

      <Container
        containerStyle={mode === "desktop" ? desktopStyles : mobileStyles}
      >
        <StatusBlock
          description={
            <Text typeScale="cp">
              {
                <ul style={{ paddingLeft: sp._16, margin: 0 }}>
                  <li>
                    {LL.bookmark_page.status_block.support_deleted_after_clear_cache()}
                  </li>
                  <li>
                    {LL.bookmark_page.status_block.shared_link_does_not_reflect_changes()}
                  </li>
                  <li>{LL.bookmark_page.status_block.generate_new_link()}</li>
                </ul>
              }
            </Text>
          }
        ></StatusBlock>

        <Listing
          categorySchemes={categorySchemes}
          services={services}
          activeTab={activeTab}
          showEndOfListingMessage={false}
          renderSchemesFilters={
            <SchemesFilters
              filters={allFilters.schemes}
              onFiltersApplied={(schemeFilters) =>
                setAllFilters({ ...allFilters, schemes: schemeFilters })
              }
              overrideCategories={[
                ...new Set(
                  categorySchemes
                    ?.map((scheme) => scheme.content.categories)
                    .flat(1)
                )
              ]}
            />
          }
          renderSvcFilters={
            <FeatureGate feature={"SVC_FILTERS"}>
              <SvcFilters
                filters={allFilters.services}
                filtersOptions={svcFilterOptions}
                onFiltersApplied={(serviceFilters) => {
                  setAllFilters({ ...allFilters, services: serviceFilters });
                }}
                overrideCategories={[
                  ...new Set(
                    services?.map((service) => service.category).flat(1)
                  )
                ]}
              />
            </FeatureGate>
          }
          renderSortBy={
            <SortBy
              sortBy={sortBy}
              onSortByApplied={setSortBy}
              disableDistance={true}
            ></SortBy>
          }
          renderSchemeSortBy={
            <SortBy
              sortBy={schemeSortBy}
              onSortByApplied={setSchemeSortBy}
              disableDistance={true}
            ></SortBy>
          }
          setActiveTab={setActiveTab}
          renderNoSchemesState={<NoSupportView type="schemes" />}
          renderNoServicesState={<NoSupportView type="services" />}
          svcCountMessageFront={LL.bookmark_page.services_saved_front()}
          svcCountMessageBack={LL.bookmark_page.services_saved_back()}
          schemeCountMessageFront={LL.bookmark_page.schemes_saved_front()}
          schemeCountMessageBack={LL.bookmark_page.schemes_saved_back()}
        />

        <Container containerStyle={{ paddingBottom: sp._40 }}>
          <SRBannerButton
            className="display-none-print"
            to="/eligibility"
            title={LL.banner_button.support_recommender()}
            subtitle={LL.banner_button.find_out_which_support_schemes_might_be_relevant_for_you()}
          />
        </Container>
        <BackToTop />
      </Container>
    </Page>
  );
};

const NoSupportView = ({ type }: { type: "schemes" | "services" }) => {
  const { LL } = useI18n();
  const bookmarkIconSize = useIconSize("s");

  return (
    <NoListingWrapper>
      <NoResultsListing
        title={
          type === "schemes"
            ? LL.bookmark_page.no_schemes_saved_yet()
            : LL.bookmark_page.no_services_saved_yet()
        }
        imageSrc={BookmarkEmptyState}
        alt="Empty bookmark page"
        description={
          <Text typeScale="b1" style={{ display: "block", marginTop: sp._8 }}>
            {type === "schemes"
              ? LL.bookmark_page.empty_bookmark_page_msg_scheme_1()
              : LL.bookmark_page.empty_bookmark_page_msg_service_1()}
            <BookmarkLine
              style={{
                display: "inline-block",
                verticalAlign: "middle",
                height: bookmarkIconSize,
                width: bookmarkIconSize
              }}
            />
            {type === "schemes"
              ? LL.bookmark_page.empty_bookmark_page_msg_scheme_2()
              : LL.bookmark_page.empty_bookmark_page_msg_service_2()}
          </Text>
        }
      />
    </NoListingWrapper>
  );
};

const BookmarkPage = withContext(() => {
  return <BookmarkPageView />;
}, BookmarkPageContextProvider);

export { BookmarkPage, BookmarkPageView };

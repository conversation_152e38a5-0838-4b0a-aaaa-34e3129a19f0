import {
  configure,
  fireEvent,
  render,
  screen,
  waitFor
} from "@testing-library/react";
import { renderHook } from "@testing-library/react-hooks";
import { useForm } from "react-hook-form";

import * as FileV2Api from "application/api/fileV2";
import { formatBytes } from "application/utils";

import { FileUploadAccordion, FileUploadAccordionProps } from "./";

configure({ testIdAttribute: "id" }); // override `data-testid` to `id`

const UPLOAD_ID = "files";
const FILE_CLASS = ".file-upload";
const MOCK_FILE = new File(["hello"], "hello.jpeg", { type: "image/jpeg" });
const MOCK_FILE2 = new File(["hello2"], "hello2.jpeg", { type: "image/jpeg" });

const MOCK_FILE_WRONG_EXT = new File(["hello"], "hello.tiff", {
  type: "image/tiff"
});
const MOCK_FILE_LONG_FILE_NAME = new File(
  ["hello"],
  "teststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststeststesteststests.tiff",
  {
    type: "image/jpeg"
  }
);
const MOCK_FILE_INVALID_FILE_NAME = new File(["hello"], "!@#$%^.jpeg", {
  type: "image/jpeg"
});
const MOCK_PROCESSED_FILE = {
  fileName: "hello.jpeg",
  fileSize: 20,
  attachmentType: "CCAT01"
};

const renderComponent = (
  initialValues?: any,
  otherProps?: Partial<FileUploadAccordionProps<any>>
) => {
  const form = renderHook(() => useForm({ defaultValues: initialValues }))
    .result;

  const props: FileUploadAccordionProps<any> = {
    ...otherProps,
    id: UPLOAD_ID,
    form: form.current,
    schemeCode: "fake-scheme",
    step: FileV2Api.FileScanStep.APPLICATION,
    maxFile: 10,
    maxFileSizeMb: 4,
    attachmentType: "CCAT08",
    isExpand: true,
    title: "NRIC/FIN/BC of household members",
    instructions: "Please upload a copy of:",
    documents: (
      <ol>
        <li>
          Front and back of the NRIC/FIN of all adults in your household, if any
        </li>
        <li>Birth certificate of all children in your household, if any</li>
      </ol>
    ),
    additionalDetails:
      "Your household member’s name and details must be shown clearly.",
    infoBlock: (
      <ul>
        <li>
          I have provided the documents for all of my household members in an
          earlier application; or
        </li>
        <li>
          I am unable to retrieve the documents from all of my household
          members.
        </li>
      </ul>
    )
  };

  const utils = render(<FileUploadAccordion {...props} />);

  const files = utils.container.querySelectorAll(FILE_CLASS);

  return {
    form,
    files,
    ...utils
  };
};

describe("FileUploadAccordion", () => {
  it("should have no uploaded files on render", () => {
    const { files } = renderComponent();
    expect(files).toHaveLength(0);
  });

  it("should have content displayed correctly", () => {
    const { getByText } = renderComponent();
    expect(getByText("NRIC/FIN/BC of household members")).toBeVisible();
    expect(getByText("Please upload a copy of:")).toBeVisible();
    expect(
      getByText(
        "Front and back of the NRIC/FIN of all adults in your household, if any"
      )
    ).toBeVisible();
    expect(
      getByText("Birth certificate of all children in your household, if any")
    ).toBeVisible();
    expect(
      getByText(
        "Your household member’s name and details must be shown clearly."
      )
    ).toBeVisible();
    expect(
      getByText(
        "I have provided the documents for all of my household members in an earlier application; or"
      )
    ).toBeVisible();
    expect(
      getByText(
        "I am unable to retrieve the documents from all of my household members."
      )
    ).toBeVisible();
    expect(getByText("Select file")).toBeVisible();
  });

  it("should display uploaded files", () => {
    const { files } = renderComponent({
      files: [MOCK_PROCESSED_FILE]
    });

    const name = screen.queryByText(MOCK_PROCESSED_FILE.fileName, {
      exact: false,
      ignore:
        '[role="alert"], [role="status"], [aria-live="polite"], [aria-live="assertive"]'
    });
    const size = screen.queryByText(formatBytes(MOCK_PROCESSED_FILE.fileSize), {
      exact: false
    });

    expect(files).toHaveLength(1);
    expect(name).toBeInTheDocument();
    expect(size).toBeInTheDocument();
  });

  it("should remove uploaded file", () => {
    const { container } = renderComponent({
      files: [MOCK_PROCESSED_FILE]
    });

    const removeButton = screen.getByTestId("file-remove-0");
    expect(removeButton).toBeInTheDocument();

    fireEvent.click(removeButton);

    expect(container.querySelectorAll(FILE_CLASS)).toHaveLength(0);
    expect(
      screen.queryByText(MOCK_PROCESSED_FILE.fileName)
    ).not.toBeInTheDocument();
  });

  it("should show error when file has wrong extension", () => {
    const { getByTestId } = renderComponent();
    const input = getByTestId("files-input");

    fireEvent.change(input, { target: { files: [MOCK_FILE_WRONG_EXT] } });
    expect(
      screen.getByText(
        "File format not supported. We only accept PDF, PNG, JFIF, JPE, JPG and JPEG."
      )
    ).toBeInTheDocument();
  });

  it("should show error when uploaded file is a duplicate", () => {
    const { getByTestId } = renderComponent({
      files: [MOCK_PROCESSED_FILE]
    });
    const input = getByTestId("files-input");

    fireEvent.change(input, { target: { files: [MOCK_FILE] } });

    expect(
      screen.getByText(
        "File name is already used. Please rename your file and try again."
      )
    ).toBeInTheDocument();
  });

  it("should show error when file has long file name", () => {
    const { getByTestId } = renderComponent();
    const input = getByTestId("files-input");

    fireEvent.change(input, {
      target: { files: [MOCK_FILE_LONG_FILE_NAME] }
    });

    expect(
      screen.getByText(
        "File name exceeded 250 characters. Reduce the file name length and try again."
      )
    ).toBeInTheDocument();
  });

  it("should show error when file has invalid characters in file name", () => {
    const { getByTestId } = renderComponent();
    const input = getByTestId("files-input");

    fireEvent.change(input, {
      target: { files: [MOCK_FILE_INVALID_FILE_NAME] }
    });

    expect(
      screen.getByText(
        "File name should only contain letters, numbers, spaces and the following characters: + - = . _ @."
      )
    ).toBeInTheDocument();
  });

  it("should show multiple errors simultaneously when file failed various front-end validations", () => {
    const { getByTestId } = renderComponent();
    const input = getByTestId("files-input");

    fireEvent.change(input, {
      target: {
        files: [
          new File(
            ["hello"],
            MOCK_FILE_LONG_FILE_NAME.name + MOCK_FILE_INVALID_FILE_NAME,
            { type: MOCK_FILE_WRONG_EXT.type }
          )
        ]
      }
    });

    expect(
      screen.getByText(
        "File name exceeded 250 characters. Reduce the file name length and try again."
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "File name should only contain letters, numbers, spaces and the following characters: + - = . _ @."
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "File format not supported. We only accept PDF, PNG, JFIF, JPE, JPG and JPEG."
      )
    ).toBeInTheDocument();
  });

  it("should show error when file scan fails", async () => {
    jest.spyOn(FileV2Api, "fileScan").mockResolvedValue({
      isTypeValid: true,
      isSizeValid: true,
      secure: false
    });

    const { getByTestId } = renderComponent();
    const input = getByTestId("files-input");

    fireEvent.change(input, { target: { files: [MOCK_FILE] } });

    await waitFor(() => {
      expect(
        screen.queryByText(
          /File blocked by virus scan. Try uploading a different file./i
        )
      ).toBeInTheDocument();
    });
  });

  it("should be able to select and upload multiple valid files", async () => {
    jest.spyOn(FileV2Api, "fileScan").mockResolvedValue({
      isTypeValid: true,
      isSizeValid: true,
      secure: true
    });

    const { getByTestId } = renderComponent();
    const input = getByTestId("files-input");

    fireEvent.change(input, { target: { files: [MOCK_FILE, MOCK_FILE2] } });

    await waitFor(() => {
      const name = screen.queryByText(MOCK_FILE.name, {
        exact: false,
        ignore:
          '[role="alert"], [role="status"], [aria-live="polite"], [aria-live="assertive"]'
      });
      const name2 = screen.queryByText(MOCK_FILE2.name, {
        exact: false,
        ignore:
          '[role="alert"], [role="status"], [aria-live="polite"], [aria-live="assertive"]'
      });

      expect(name).toBeInTheDocument();
      expect(name2).toBeInTheDocument();
    });
  });

  it("should be able to select and upload multiple valid and non-valid files", async () => {
    jest.spyOn(FileV2Api, "fileScan").mockResolvedValue({
      isTypeValid: true,
      isSizeValid: true,
      secure: true
    });

    const { getByTestId } = renderComponent();
    const input = getByTestId("files-input");

    fireEvent.change(input, {
      target: { files: [MOCK_FILE, MOCK_FILE_INVALID_FILE_NAME] }
    });

    await waitFor(() => {
      const name = screen.queryByText(MOCK_FILE.name, {
        exact: false,
        ignore:
          '[role="alert"], [role="status"], [aria-live="polite"], [aria-live="assertive"]'
      });
      const name2 = screen.queryByText(MOCK_FILE_INVALID_FILE_NAME.name, {
        exact: false,
        ignore:
          '[role="alert"], [role="status"], [aria-live="polite"], [aria-live="assertive"]'
      });

      expect(name).toBeInTheDocument();
      expect(name2).toBeInTheDocument();
    });
  });
});

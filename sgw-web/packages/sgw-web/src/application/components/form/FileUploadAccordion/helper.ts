import {
  fileScan,
  fileScanExternal,
  FileScanStep
} from "application/api/fileV2";
import Compressor from "compressorjs";

import { FileError } from "./FileUpload";
import { fileUploadQueue } from "./FileUploadQueue";
import { UploadedFileSchema, uploadedFileValidator } from "./validator";

export const resizeFile = async (
  strippedFileName: string,
  file: File
): Promise<File> => {
  // add timestamp to images directly uploaded from iOS "take photo" option
  if (strippedFileName === "image") {
    strippedFileName = `${strippedFileName}_${Date.now().toString()}`;
  }
  const modifiedFileName = `${strippedFileName}.jpeg`;
  return await new Promise((resolve, reject) => {
    new Compressor(file, {
      maxHeight: 1440,
      maxWidth: 1440,
      retainExif: true,
      mimeType: "image/jpeg",
      convertTypes: ["image/png", "image/heic"],
      success: (resizedFile) => {
        // resized result can be either File or Blob type: https://github.com/fengyuanchen/compressorjs/issues/146
        if (resizedFile instanceof File)
          return resolve({
            ...resizedFile,
            name: modifiedFileName
          });
        else {
          return resolve(
            new File([resizedFile], modifiedFileName, {
              type: resizedFile.type
            })
          );
        }
      },
      error: (err) => {
        console.log("file resize error: ", err);
        return reject(err);
      }
    });
  });
};

export const validateUniqueName = (
  fileName: string,
  uploadedFileNames: string[],
  selectedFileNames: string[]
) => {
  const fileErrors: FileError[] = [];

  const isFileNameDuplicate =
    [...selectedFileNames, ...uploadedFileNames].filter(
      (name) => stripExtenstion(name) === stripExtenstion(fileName)
    ).length > 1;

  if (isFileNameDuplicate) {
    fileErrors.push({
      name: fileName,
      errorMessage:
        "File name is already used. Please rename your file and try again."
    });
  }

  return fileErrors;
};

export const stripExtenstion = (fileName: string) => {
  return fileName.replace(/\.[^/.]+$/, "");
};

export const validate = (
  file: File,
  uploadedFiles: string[],
  maxFileSizeMb: number
): FileError[] => {
  const uploadedFile: UploadedFileSchema = {
    contentType: file.type,
    fileName: file.name,
    fileSize: file.size
  };

  const fileErrors: FileError[] = [];

  // unique file validation
  if (!isUniqueFileName([...uploadedFiles, uploadedFile.fileName])) {
    fileErrors.push({
      name: uploadedFile.fileName,
      errorMessage:
        "File name is already used. Please rename your file and try again."
    });
  }

  // file name + file size + file extension validations
  fileErrors.push(...validateUploadedFile(uploadedFile, maxFileSizeMb));

  return fileErrors;
};

export const uploadFile = async (
  file: File,
  schemeCode: string,
  step: FileScanStep,
  attachmentType: string,
  abortFileScanController: AbortController,
  external?: { url: string; fileId: string }
): Promise<FileError[]> => {
  const fileScanResult = await fileUploadQueue.add(() =>
    external
      ? fileScanExternal(
          external.url,
          external.fileId,
          file,
          schemeCode,
          step,
          attachmentType,
          abortFileScanController
        )
      : fileScan(
          file,
          schemeCode,
          step,
          attachmentType,
          abortFileScanController
        )
  );

  if (!fileScanResult) {
    return getGenericUploadError(file.name);
  } else {
    const { isSizeValid, isTypeValid, secure } = fileScanResult;
    if (!isSizeValid || !isTypeValid || !secure) {
      return getFileScanError(file.name, isSizeValid, isTypeValid, secure);
    }
  }

  return [];
};

const getFileScanError = (
  name: string,
  isFileSizeValid: boolean,
  isFileTypeValid: boolean,
  secure?: boolean
): FileError[] => {
  const errors: FileError[] = [];

  if (!isFileSizeValid) {
    errors.push({
      name,
      errorMessage:
        "File size exceeded 4 MB. Reduce the file size and try again."
    });
  }

  if (!isFileTypeValid) {
    errors.push({
      name,
      errorMessage:
        "File format error. The file format must match the file extension (.PDF, .PNG, .JFIF, .JPE, .JPG, .JPEG)."
    });
  }

  if (secure === false) {
    errors.push({
      name,
      errorMessage:
        "File blocked by virus scan. Try uploading a different file."
    });
  }

  return errors;
};

const getGenericUploadError = (name: string): FileError[] => {
  return [
    {
      name,
      errorMessage: "File upload error. Try uploading a different file."
    }
  ];
};

const isUniqueFileName = (fileNames: string[]) => {
  if (fileNames && fileNames.length > 1) {
    return new Set(fileNames).size === fileNames.length;
  }
  return true;
};

const validateUploadedFile = (
  uploadedFile: UploadedFileSchema,
  maxFileSizeMb: number
): FileError[] => {
  const error: FileError[] = [];

  const result = uploadedFileValidator(maxFileSizeMb).safeParse(uploadedFile);
  if (!result.success) {
    for (const issue of result.error.issues) {
      error.push({
        name: uploadedFile.fileName,
        errorMessage: issue.message
      });
    }
  }

  return error;
};

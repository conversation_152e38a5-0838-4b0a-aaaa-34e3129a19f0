import { ChangeEvent, useEffect, useRef, useState } from "react";
import { FieldError } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";

import { FileScanStep } from "application/api/fileV2";
import { useFormMetadata } from "application/context/FormMetadata";
import { env } from "commons/app/config";
import { IconButton } from "commons/components/buttons/IconButton";
import { EllipsisText } from "commons/components/EllipsisText";
import ErrorMessage from "commons/components/ErrorMessage";
import { getErrorMsgId } from "commons/components/FieldLabel";
import { TextButton } from "commons/components/TextButton";
import { usePrevious } from "commons/hooks/usePrevious";

import {
  FileStatusesWrapper,
  FilesWrapper,
  FileUploading,
  HiddenInput,
  ProgressBar,
  ValidFile
} from "./FileUpload.styles";
import {
  resizeFile,
  stripExtenstion,
  uploadFile,
  validate,
  validateUniqueName
} from "./helper";
import {
  RESIZE_ALLOWED_EXTENSIONS,
  RESIZE_ALLOWED_MIME_TYPES
} from "./validator";

export interface FileValue {
  fileName: string;
  attachmentType: string;
  fileSize: number;
  fileId?: string; // optional, used for external file upload
}

export interface FileUploadProps {
  id: string;
  schemeCode: string;
  title: string;
  step: FileScanStep;
  maxFile: number;
  maxFileSizeMb: number;
  attachmentType: string;
  onChange: (file: FileValue[]) => void;
  onBlur: () => void;
  value?: FileValue[];
  fieldError?: FieldError;
  disabled?: boolean;
  isCardStyling?: boolean;
}

export interface FileError {
  name?: string;
  errorMessage: string;
}

export const FileUpload = ({
  id,
  schemeCode,
  title,
  step,
  maxFile,
  maxFileSizeMb,
  attachmentType,
  disabled: disabledProp,
  value,
  onChange,
  onBlur,
  fieldError,
  isCardStyling
}: FileUploadProps): JSX.Element => {
  const { isLoading, fileUploadExternal } = useFormMetadata();

  const [files, setFiles] = useState<FileValue[]>([]);
  const [loadingFiles, setLoadingFiles] = useState<string[]>([]);
  const [errors, setErrors] = useState<FileError[]>([]);

  const prevFiles = usePrevious(files);
  const prevValue = usePrevious(value);
  useEffect(() => {
    if (value && prevValue === undefined && files.length === 0) {
      setFiles(value);
    } else if (prevFiles && prevFiles !== files && files !== value) {
      onChange(files);
    }
  }, [files, prevFiles, onChange, value, prevValue]);

  const disabled = disabledProp || isLoading;
  const uId = id + "-upload";
  const iId = id + "-input";

  const openDialog = (): void => {
    document.getElementById(iId)?.click();
  };

  // cancel file scan API when component is unmounted
  const abortFileScanController = useRef(new AbortController());
  useEffect(() => {
    const abortController = abortFileScanController.current;
    return () => {
      abortController.abort();
    };
  }, []);

  const onUpload = async (
    event: ChangeEvent<HTMLInputElement>
  ): Promise<void> => {
    setErrors([]);
    onBlur(); // this is to trigger validation when react-hook-form mode is "onTouched", without this, validation will not be run

    const selectedFiles = Object.values(event.target.files || {});

    if (selectedFiles.length > 0) {
      if (files.length + selectedFiles.length > maxFile) {
        setErrors([
          {
            errorMessage: `Reached limit of ${maxFile} files. Try uploading the files elsewhere or reducing the number of files.`
          }
        ]);
        return;
      }

      selectedFiles.forEach(async (file) => {
        const fileErrors: FileError[] = [];
        const strippedFileName = stripExtenstion(file.name);

        const selectedFileNames = selectedFiles.map((f) => f.name);
        const uploadedFileNames = files.map((f) => f.fileName);

        setLoadingFiles((prevFiles) => [...prevFiles, strippedFileName]);

        if (env.ENABLE_FILE_RESIZE) {
          fileErrors.push(
            ...validateUniqueName(
              file.name,
              uploadedFileNames,
              selectedFileNames
            )
          );

          if (fileErrors.length === 0) {
            const fileExt = file.name.split(".").pop()?.toLowerCase();
            if (
              fileExt &&
              RESIZE_ALLOWED_EXTENSIONS.includes(fileExt) &&
              RESIZE_ALLOWED_MIME_TYPES.includes(file.type)
            ) {
              try {
                file = await resizeFile(strippedFileName, file);
              } catch {
                console.log("failed resize");
              }
            }
          }
        }

        if (fileErrors.length === 0) {
          fileErrors.push(...validate(file, uploadedFileNames, maxFileSizeMb));
        }

        const fileId = uuidv4();
        if (fileErrors.length === 0) {
          const uploadErrors = await uploadFile(
            file,
            schemeCode,
            step,
            attachmentType,
            abortFileScanController.current,
            fileUploadExternal && {
              url: fileUploadExternal.url,
              fileId
            }
          );
          fileErrors.push(...uploadErrors);
        }

        setLoadingFiles((prevFiles) =>
          prevFiles.filter((f) => f !== strippedFileName)
        );

        if (fileErrors.length === 0) {
          setFiles((prevFiles) => [
            ...prevFiles,
            {
              fileName: file.name,
              attachmentType,
              fileSize: file.size,
              fileId
            }
          ]);
        } else {
          setErrors((prevErrors) => [...prevErrors, ...fileErrors]);
        }
      });
    }
  };

  const onRemove = (name: string) => {
    setFiles((prevFiles) => prevFiles.filter((f) => f.fileName !== name));
    setErrors([]);
    onBlur(); // this is to trigger validation when react-hook-form mode is "onTouched", without this, validation will not be run
  };

  return (
    <>
      <FilesWrapper isCardStyling={isCardStyling}>
        {(files.length > 0 || loadingFiles.length > 0) && (
          <FileStatusesWrapper>
            {files.map((file, index) => (
              <ValidFile
                disabled={disabled}
                key={`${id}/${file.fileName}-valid`}
              >
                <span
                  className="visually-hidden"
                  role="alert"
                  aria-live="polite"
                >
                  Upload {file.fileName} successfully
                </span>
                <EllipsisText
                  content={file.fileName}
                  fileSize={file.fileSize}
                  isMiddleEllipsis={true}
                />
                <IconButton
                  aria-label={`Remove ${file.fileName}`}
                  icon={{
                    name: "closeRounded-grey",
                    size: "s",
                    desktopSize: "m"
                  }}
                  id={`file-remove-${index}`}
                  data-testid={`${id}-remove`}
                  disabled={disabled}
                  onClick={() => onRemove(file.fileName)}
                />
              </ValidFile>
            ))}

            {loadingFiles.map((fileName) => (
              <FileUploading
                key={`${id}/${fileName}-loading`}
                role="alert"
                aria-live="polite"
                aria-label="Uploading file"
              >
                <EllipsisText content={fileName} isMiddleEllipsis={true} />
                {ProgressBar}
              </FileUploading>
            ))}
          </FileStatusesWrapper>
        )}

        {loadingFiles.length === 0 && fieldError?.message && (
          <ErrorMessage id={getErrorMsgId(id)}>
            {fieldError.message}
          </ErrorMessage>
        )}

        {errors.length > 0 &&
          errors.map((error, index) => {
            return (
              <ErrorMessage
                title={error.name}
                titleMiddleEllipsis={true}
                key={index}
                id={getErrorMsgId(id, index)}
                aria-label={`Error: ${error.name}, ${error.errorMessage}`}
                role="alert"
                aria-live="polite"
              >
                {error.errorMessage}
              </ErrorMessage>
            );
          })}
      </FilesWrapper>

      <TextButton
        className="file-upload-button"
        id={uId}
        onClick={openDialog}
        disabled={loadingFiles.length > 0 || disabled}
        variant="full-width"
        type="button"
        aria-label={`Select file to upload for ${title}`}
        aria-describedby={`${getErrorMsgId(id)} ${errors
          .map((err, index) => `${getErrorMsgId(id, index)}`)
          .join(" ")}`}
      >
        Select file
      </TextButton>

      <HiddenInput
        id={iId}
        type="file"
        onChange={onUpload}
        value=""
        multiple
        accept="image/png, image/jpeg, image/jpg, image/heic, application/pdf"
      />
    </>
  );
};

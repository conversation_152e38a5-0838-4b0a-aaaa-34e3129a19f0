import { ApplicationSchema } from "application/templates/MultistepApplication/GenericApplication.helper";
import { TemplateId } from "application/templates/MultistepApplication/AppDashboard/modalTemplates";
import { Scheme } from "application/components/Acknowledgement";

import axios from "axios";

import { appendCSRF } from "./helper";

const SGW_ENDPOINT = `/sgw`;

export interface PostApplicationResponse {
  email?: string;
  schemes: Scheme[];
}

export const postApplication = async (
  schemeCode: string,
  schemaId: string,
  values: object
): Promise<PostApplicationResponse> => {
  const res = await axios.post<PostApplicationResponse>(
    `${SGW_ENDPOINT}/${schemeCode}/submit/${schemaId}`,
    values,
    appendCSRF()
  );
  return res.data;
};

interface GetSchemaResponse {
  schema: ApplicationSchema;
}

export const getSchema = async (
  schemeCode: string
): Promise<GetSchemaResponse> => {
  const res = await axios.get<GetSchemaResponse>(
    `${SGW_ENDPOINT}/${schemeCode}`
  );
  return res.data;
};

/**
 * Fetches a merged schema for multiple schemes from the /saf endpoint.
 * @param schemeCodes - An array of scheme codes to be merged.
 */
export const getMergedSchema = async (
  schemeCodes: string[]
): Promise<GetSchemaResponse> => {
  const res = await axios.get<GetSchemaResponse>("/saf/schema", {
    params: {
      codes: schemeCodes.join(",")
    }
  });
  return res.data;
};

export interface SchemeAppState {
  schemeCode: string;
  applicationState: ApplicationState;
}

export type GetAppStateResponse = SchemeAppState[];

/**
 * Fetches a schemes the user is eligible for from the /saf endpoint.
 */
export const getSafAppState = async (): Promise<GetAppStateResponse> => {
  const res = await axios.get<GetAppStateResponse>("/saf/app-state");
  return res.data;
};

/**
 * Fetches prefill data for a specific scheme and schema ID.
 * If bundled scheme codes are applicable (for SAF) and provided, they will be included as a query parameter.
 * This function is used to retrieve prefilled data for applications based on the scheme and schema ID.
 * It constructs the URL dynamically based on the provided parameters and makes a GET request to the SGW endpoint.
 * The response is expected to be an object where keys are IDs and values are the corresponding to prefill data.
 * If the request fails, an error is thrown.
 * @param schemeCode - The scheme code for which to fetch prefill data.
 * @param schemaId - The schema ID for which to fetch prefill data.
 * @param bundledSchemeCodes - An array of bundled scheme codes to include in the request.
 * @returns A promise that resolves to an object containing the prefill data.
 * @throws Error if the request fails.
 */
export const getPrefillData = async (
  schemeCode: string,
  schemaId: string,
  bundledSchemeCodes: string[]
) => {
  try {
    let url = `${SGW_ENDPOINT}/${schemeCode}/prefill/${schemaId}`;
    if (bundledSchemeCodes.length > 0) {
      const codesParam = bundledSchemeCodes.join(",");
      url += `?codes=${encodeURIComponent(codesParam)}`;
    }
    const res = await axios.get<{ [id: string]: any }>(url);
    return res.data;
  } catch {
    throw Error("Error retrieving agency prefill data. Please try again.");
  }
};

export const getOptionsData = async (schemeCode: string, refId: string) => {
  const res = await axios.get<{ [id: string]: any }>(
    `${SGW_ENDPOINT}/${schemeCode}/options/${refId}`
  );
  return res.data;
};

export type SgwStatus =
  | "Submitted"
  | "Consent Required"
  | "Received"
  | "Processing"
  | "Approved"
  | "Assistance Received"
  | "Rejected"
  | "Withdrawn"
  | "Completed"
  | "Ongoing"
  | "Pending Documents"
  | "Documents Submitted"
  | "Draft"
  | "Suspended"
  | "Payout Failed"
  | "Assistance Refunded";
export interface SgwAppStatus {
  refId: string;
  statusCode: string;
  status: SgwStatus;
  appliedDateTime?: string;
  updatedDateTime?: string;
  remarks?: string;
  detail?: ApplicationDetail[];
  deadline?: string;
  documentsType?: string;
  schemeCode?: string;
  schemeName?: string;
  appliedForNames?: string[];
}

export type ApplicationState =
  | "allow"
  | "disallow"
  | "maintenance"
  | "ineligible"
  | "unavailable"
  | "timeout";

interface ConsentRequired {
  refId: string;
  isRequired: boolean;
}

interface ChildInfo {
  name: string;
  nric: string;
}

export interface TemplateData {
  children?: ChildInfo[];
}

interface ApplyTemplate {
  template: TemplateId;
  data?: TemplateData;
}

export type ApplyInfo = Record<string, ApplyTemplate>;

export interface SgwAppStatusResponse {
  status: SgwAppStatus[];
  agencyUnavailable: boolean;
  applicationState: ApplicationState;
  consentsRequired: ConsentRequired[];
  maintenanceMsg?: string;
  ineligibleCode?: string;
  applyInfo?: ApplyInfo;
  isExternalForm: boolean;
}

export interface ApplicationDetail {
  title: string;
  category: string;
  assistance?: string;
  status?: string;
  description?: string;
  period?: string;
  paymentMode?: string;
  disbursement?: string;
  remarks?: string;
}

export const getSgwApplicationStatus = async (schemeCode: string) => {
  const res = await axios.get<SgwAppStatusResponse>(
    `${SGW_ENDPOINT}/${schemeCode}/status`
  );
  return res.data;
};

export const getMergedSgwApplicationStatus = async (
  schemeCodes: string[]
): Promise<SgwAppStatusResponse> => {
  const res = await axios.get<SgwAppStatusResponse>(`/saf/status`, {
    params: {
      codes: schemeCodes.join(",")
    }
  });
  return res.data;
};

interface SgwConsentStatusResponse {
  applicantName?: string;
}

export const getSgwConsentStatus = async (
  schemeCode: string,
  refId: string
) => {
  const res = await axios.get<SgwConsentStatusResponse>(
    `${SGW_ENDPOINT}/${schemeCode}/consent/${refId}`
  );
  return res.data;
};

interface SubmitSgwOutstandingDocumentsResponse {
  email: string;
}

export const submitSgwOutstandingDocuments = async (
  schemeCode: string,
  refId: string,
  values: object
) => {
  const res = await axios.post<SubmitSgwOutstandingDocumentsResponse>(
    `${SGW_ENDPOINT}/${schemeCode}/outstanding-documents/${refId}`,
    values,
    appendCSRF()
  );

  return res.data;
};

interface SubmitSgwConsentResponse {
  email: string;
}

export const submitSgwConsent = async (
  schemeCode: string,
  refId: string,
  values: object
) => {
  const res = await axios.post<SubmitSgwConsentResponse>(
    `${SGW_ENDPOINT}/${schemeCode}/consent/${refId}`,
    values,
    appendCSRF()
  );

  return res.data;
};

export interface GetTokenResponse {
  token: string;
  sqServiceId: string;
}

export const getSqAuthDetails = async (
  schemeCode: string
): Promise<GetTokenResponse> => {
  const res = await axios.get<GetTokenResponse>(
    `${SGW_ENDPOINT}/${schemeCode}/sso/auth`
  );
  return res.data;
};

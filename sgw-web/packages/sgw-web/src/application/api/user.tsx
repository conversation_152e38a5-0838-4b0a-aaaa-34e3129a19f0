import axios from "axios";

import { SgwAppStatus } from "application/api/sgw";
import { env } from "commons/app/config";

import { appendCSRF } from "./helper";

const USER_ENDPOINT = `/user`;

export interface User {
  nric: string;
  wogaaUserId: string;
  name: string;
  residentialStatus: ResidentialStatus;
  lastLogin?: string;
}

export interface GetUserApplicationsResponse {
  application: SgwAppStatus[];
  hasTodoItems?: boolean;
  agencyUnavailable?: boolean;
}

export type ResidentialStatus =
  | ""
  | "UNKNOWN"
  | "ALIEN"
  | "CITIZEN"
  | "PR"
  | "NOT APPLICABLE";

export interface MyInfoPersonResponse {
  data?: Record<string, any>;
  options?: Record<string, any>;
}

/**
 * Fetches MyInfo person data based on the provided scheme code, schema ID, and bundled scheme codes.
 * If bundled scheme codes are application (for SAF) and provided, they will be included as a query parameter.
 * If the request fails, an error is thrown.
 * @param schemeCode - The scheme code to fetch MyInfo data for.
 * @param schemaId - The schema ID to fetch MyInfo data for.
 * @param bundledSchemeCodes - An array of bundled scheme codes to include in the request.
 * @returns A promise that resolves to the MyInfo person data.
 * @throws Error if the request fails.
 */
export const getMyInfoPerson = async (
  schemeCode: string,
  schemaId: string,
  bundledSchemeCodes: string[]
) => {
  try {
    let url = `${env.MYINFO_PERSON_URL}/${schemeCode}/${schemaId}`;
    if (bundledSchemeCodes.length > 0) {
      const codesParam = bundledSchemeCodes.join(",");
      url += `?codes=${encodeURIComponent(codesParam)}`;
    }
    const res = await axios.get<MyInfoPersonResponse>(url);
    return res.data;
  } catch {
    throw Error("Error retrieving MyInfo. Please try again.");
  }
};

export const getUser = async (): Promise<User> => {
  const res = await axios.get<User>(env.MYINFO_USER_URL, appendCSRF());
  return res.data;
};

export const getUserApplications = async () => {
  const res = await axios.get<GetUserApplicationsResponse>(
    `${USER_ENDPOINT}/application`
  );
  return res.data;
};

export const getUserApplicationsBySchemeCode = async (schemeCode?: string) => {
  const res = await axios.get<GetUserApplicationsResponse>(
    `${USER_ENDPOINT}/application/${schemeCode}`
  );
  return res.data;
};

export const getUserTodoApplications = async () => {
  const res = await axios.get<GetUserApplicationsResponse>(
    `${USER_ENDPOINT}/application/todo`
  );
  return res.data;
};

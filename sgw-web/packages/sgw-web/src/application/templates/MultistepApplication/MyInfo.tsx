import { useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { UseFormReturn } from "react-hook-form";

import { InputText } from "application/components/InputText";
import { presetFieldDefaultValues } from "application/composites/presetFields";
import { setValues } from "application/utils";
import { useOverlay } from "commons/app/context/overlay";
import { useToast } from "commons/app/context/toast";
import { RetrieveMyInfoButton } from "commons/components/RetrieveMyInfoButton";
import { TextButton } from "commons/components/TextButton";
import { toastMyInfoError } from "commons/toast/toast";

import { useMyInfoPersonQuery } from "./helper";
import { MyInfoContainer } from "./MyInfo.styles";
import { ApplicationSchema } from "./GenericApplication.helper";

function getFieldId(id: string) {
  const splitId = id.split(".");
  return splitId[splitId.length - 1];
}

interface MyInfoProps {
  schemeCode: string;
  form: UseFormReturn;
  schemaId: string;
  applicationSchema: ApplicationSchema;
}

export const MyInfo = ({
  schemeCode,
  form,
  schemaId,
  applicationSchema
}: MyInfoProps) => {
  const queryClient = useQueryClient();
  const { isOverlay, setIsOverlay } = useOverlay();
  const { setToast } = useToast();

  const {
    clearableMyInfoIds,
    myInfoOptionsIds,
    bundledSchemeCodes
  } = applicationSchema;

  const { fetchStatus, refetch } = useMyInfoPersonQuery(
    schemeCode,
    schemaId,
    bundledSchemeCodes,
    {
      enabled: false
    }
  );

  const prefillWithMyInfo = async () => {
    try {
      const response = await refetch();

      if (response.isError) {
        // error toast for will be shown elsewhere i.e. where useMyInfoPersonQuery is used
        return;
      }

      const data = response.data?.data;
      if (!data) {
        throw new Error("No data returned from MyInfo query");
      }

      const newValues = Object.entries(data).map(([id, value]) => ({
        id,
        value
      }));

      setValues(form.setValue, newValues, {
        shouldDirty: true,
        shouldValidate: true
      });

      // trigger validation for all sections of the form that were prefilled, skip "consent" as there's no section for it
      if (schemaId !== "consent") {
        const prefilledSections = [
          ...new Set(clearableMyInfoIds?.map((id) => id.split(".")[0]))
        ];
        form.trigger(prefilledSections);
      }

      form.setValue("myInfoUsed", true);
      setToast("Retrieved latest Myinfo");
    } catch (err) {
      toastMyInfoError();
    }
  };

  useEffect(() => {
    if (fetchStatus === "fetching") {
      setIsOverlay(true);
    } else {
      setIsOverlay(false);
    }
  }, [fetchStatus, setIsOverlay]);

  const performClear = (): void => {
    const hasClearableFields =
      form.getValues("myInfoUsed") && (clearableMyInfoIds || myInfoOptionsIds);

    if (!hasClearableFields) return;

    const getMemberIdsToClear = (myInfoOptionsIds?: string[]) => {
      return (
        myInfoOptionsIds?.flatMap((id) => {
          if (!["child", "family"].includes(getFieldId(id))) return [];

          const isMultiValue = id.includes("[index].");
          if (!isMultiValue) {
            return form.getValues(`${id}.optionType`) === "myinfo" ? [id] : [];
          }

          const [namespace] = id.split("[index].");
          const memberMultiValue = form.getValues(namespace) || [];

          return (memberMultiValue as any[])
            .map((_, index) => {
              const memberId = id.replace("index", `${index}`);
              return form.getValues(`${memberId}.optionType`) === "myinfo"
                ? memberId
                : "";
            })
            .filter(Boolean);
        }) || []
      );
    };
    const memberIdsToClear = getMemberIdsToClear(myInfoOptionsIds);

    const newValues = [
      ...(clearableMyInfoIds?.map((id) => ({
        id,
        value: presetFieldDefaultValues[getFieldId(id)] || ""
      })) || []),
      ...(memberIdsToClear?.map((id) => ({
        id,
        value: presetFieldDefaultValues[getFieldId(id)] ?? ""
      })) || []),
      { id: "myInfoUsed", value: false }
    ];
    setValues(form.setValue, newValues, {
      shouldDirty: true,
      shouldValidate: true
    });
    queryClient.resetQueries(["myInfoPerson", schemeCode]);
    setToast("Cleared Myinfo");
  };

  return (
    <MyInfoContainer>
      <RetrieveMyInfoButton
        onClick={() => prefillWithMyInfo()}
        disabled={isOverlay}
      />
      <TextButton onClick={performClear} disabled={isOverlay} typeScale="bt1">
        Clear Myinfo
      </TextButton>
      <InputText id="myInfoUsed" form={form} isHide />
    </MyInfoContainer>
  );
};

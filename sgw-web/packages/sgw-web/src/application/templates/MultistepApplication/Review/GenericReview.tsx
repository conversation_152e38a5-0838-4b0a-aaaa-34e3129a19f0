import { useState, useRef } from "react";
import { UseFormReturn } from "react-hook-form";
import { useMutation } from "@tanstack/react-query";
import { useHistory } from "react-router-dom";
import { z } from "zod";

import { postApplication, PostApplicationResponse } from "application/api/sgw";
import { CenteredGridContainer } from "application/components/CenteredGridContainer";
import { useFormMetadata } from "application/context/FormMetadata";
import { TriggerMap } from "application/utils/formTriggers";
import { useOverlay } from "commons/app/context/overlay";
import { Breadcrumbs } from "commons/components/Breadcrumbs";
import { Button } from "commons/components/buttons/Button";
import ErrorMessage from "commons/components/ErrorMessage";
import { cl } from "commons/styles";

import { ApplicationSchema, SectionSchema } from "../GenericApplication.helper";
import { generateBreadcrumbLinks, getAppPath } from "../helper";
import { GenericSuccess } from "../Success";
import { GenericReviewList } from "./GenericReviewList";
import {
  <PERSON><PERSON>r<PERSON>rap<PERSON>,
  Header,
  NavigationButtonsContainer,
  NavigationContainer
} from "./GenericReview.styles";

export interface ReviewProps {
  schemaId: string;
  schemeCode: string;
  applicationSchema: ApplicationSchema;
  form: UseFormReturn;
  sections: SectionSchema[];
  triggerMap: TriggerMap;
  setSubmitResponse: (submitResponse: PostApplicationResponse) => void;
}

export const Review = ({
  schemaId,
  schemeCode,
  applicationSchema,
  form,
  sections,
  triggerMap,
  setSubmitResponse
}: ReviewProps): JSX.Element => {
  const path = getAppPath(applicationSchema);
  const { isOverlay, setIsOverlay } = useOverlay();
  const { push } = useHistory();
  const { setDraft } = useFormMetadata();
  const [errorMessage, setErrorMessage] = useState("");
  const isSubmitting = useRef(false);
  const application = useMutation(
    () => postApplication(schemeCode, schemaId, form.getValues()),
    {
      onMutate: () => {
        setIsOverlay(true);
      },
      onSuccess: (data) => {
        setSubmitResponse(data);
        setDraft(undefined);
      },
      onError: (error: any) => {
        if (error.response?.data?.message) {
          setErrorMessage(error.response?.data?.message);
        } else {
          setErrorMessage(
            "Our system is experiencing some issues. Please try again later."
          );
        }
      },
      onSettled: () => {
        setIsOverlay(false);
      }
    }
  );

  const submitApplication = async () => {
    if (isSubmitting.current) return;
    isSubmitting.current = true;
    try {
      await application.mutateAsync();
    } finally {
      isSubmitting.current = false;
    }
  };

  const handleSubmitClick = async () => {
    setIsOverlay(true);

    try {
      triggerMap.formSubmit.forEach(({ action }) => action(form));

      const isValid = await form.trigger();
      if (!isValid) {
        setIsOverlay(false);
        return;
      }

      form.handleSubmit(submitApplication)();
    } catch {
      setIsOverlay(false);
    }
  };

  return (
    <>
      <CenteredGridContainer backgroundColor={cl.white}>
        <Header>Review your application</Header>
        <GenericReviewList form={form} sections={sections} />
      </CenteredGridContainer>

      <NavigationContainer>
        {errorMessage && (
          <ErrorWrapper>
            <ErrorMessage>{errorMessage}</ErrorMessage>
          </ErrorWrapper>
        )}
        <NavigationButtonsContainer>
          <Button
            buttonText="Submit application"
            onClick={handleSubmitClick}
            buttonScheme="first"
            disabled={isOverlay}
          />
          <Button
            buttonText="Back to overview"
            onClick={() => {
              push(path.overview);
            }}
            buttonScheme="second"
            disabled={isOverlay}
          />
        </NavigationButtonsContainer>
      </NavigationContainer>
    </>
  );
};

export interface GenericReviewProps {
  schemaId: string;
  schemeCode: string;
  applicationSchema: ApplicationSchema;
  form: UseFormReturn;
  sections: SectionSchema[];
  validationSchema: z.ZodTypeAny;
  triggerMap: TriggerMap;
}

export const GenericReview = ({
  schemaId,
  schemeCode,
  applicationSchema,
  form,
  sections,
  validationSchema,
  triggerMap
}: GenericReviewProps): JSX.Element => {
  const path = getAppPath(applicationSchema);
  const { replace } = useHistory();
  const [submitResponse, setSubmitResponse] = useState<
    PostApplicationResponse | undefined
  >(undefined);

  if (submitResponse) {
    return (
      <GenericSuccess
        schemeCode={schemeCode}
        applicationSchema={applicationSchema}
        form={form}
        schemes={submitResponse.schemes}
        email={submitResponse.email}
        ReviewList={
          <GenericReviewList form={form} sections={sections} isExpandAll />
        }
      />
    );
  }

  const result = validationSchema.safeParse(form.getValues());
  if (!result.success) {
    replace(path.applyDashboard);
    return <></>;
  }

  return (
    <>
      <Breadcrumbs
        links={generateBreadcrumbLinks(applicationSchema, "review", "Review")}
      />
      <Review
        schemaId={schemaId}
        schemeCode={schemeCode}
        applicationSchema={applicationSchema}
        sections={sections}
        form={form}
        setSubmitResponse={setSubmitResponse}
        triggerMap={triggerMap}
      />
    </>
  );
};

import { <PERSON>a, StoryObj } from "@storybook/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import {
  StorybookApplicationPage,
  useStorybookForm
} from "application/components/form/storybookUtils";
import {
  ApplicationSchema,
  generateApplicationSchema
} from "application/templates/MultistepApplication/GenericApplication.helper";
import { ComponentProps } from "react";

import { GenericReview as GenericReviewComponent } from "./GenericReview";
import { TriggerMap } from "../../../utils/formTriggers";

export default {
  title: "Application Generator/Template/Generic Review",
  component: GenericReviewComponent,
  argTypes: {
    schemaId: {
      table: {
        disable: true
      }
    },
    schemeCode: {
      table: {
        disable: true
      }
    },
    form: {
      table: {
        disable: true
      }
    },
    validationSchema: {
      table: {
        disable: true
      }
    },
    sections: {
      table: {
        disable: true
      }
    }
  }
} as Meta;

type Story = StoryObj<typeof GenericReviewComponent>;

const MOCK_SCHEMA: ApplicationSchema = {
  schemeName: "Mother's day assistance",
  subtitle: "Supported by Ministry of Social and Family Development",
  schemeCode: "mda",
  schemeDetailsLink: "/schemes/CRG/covid-19-recovery-grant",
  dashboard: {
    applicationStatusNote: "",
    applicationGuidance:
      "If you had recently submitted an application, please do not press “Apply now” as it may delay the processing of your application."
  },
  contacts: [
    {
      faqLink: "https://www.google.com.sg/",
      email: "<EMAIL>",
      hotlineNumber: "1800 222 0000",
      locationLink: "https://goo.gl/maps/RDrj6Cu15j6Png7r7",
      helpExtra: [
        "You may also visit your nearest Social Service Office if you require urgent help"
      ]
    }
  ],
  nextSteps:
    "Your application has been submitted, please wait 2 - 3 working days for it to be processed.",
  schema: [
    {
      id: "main",
      section: [
        {
          title: "Profile",
          subtitle: "This is profile subtitle",
          id: "profile",
          member: [
            {
              id: "details",
              title: "Details",
              type: "CUSTOM_GROUP",
              subType: "BLANK",
              member: [
                {
                  id: "text",
                  type: "CUSTOM_FIELD",
                  subType: "DATE",
                  title: "text field",
                  optional: true
                },
                {
                  type: "DECORATOR",
                  subType: "HEADER",
                  title: "Group level header"
                },
                {
                  id: "text-number",
                  type: "CUSTOM_FIELD",
                  subType: "TEXT_NUMBER",
                  title: "number field"
                }
              ]
            },
            {
              id: "text",
              type: "CUSTOM_FIELD",
              subType: "TEXT",
              title: "section-level text field"
            },
            {
              id: "family",
              title: "Family",
              type: "CUSTOM_GROUP",
              subType: "BLANK",
              member: [
                {
                  id: "name",
                  type: "PRESET_FIELD",
                  subType: "nric"
                },
                {
                  id: "date",
                  type: "PRESET_FIELD",
                  subType: "dob",
                  optional: true
                },
                {
                  id: "myaddress",
                  type: "PRESET_FIELD",
                  subType: "address"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};
const MOCK_FORM_DATA = {
  myInfoUsed: true,
  profile: {
    details: { text: "2025-01-29", "text-number": "123" },
    text: "abc",
    family: {
      name: "*********",
      date: "2025-01-28",
      myaddress: {
        isLocal: true,
        country: "SG",
        postalCode: "138577",
        block: "1",
        street: "FUSIONOPOLIS VIEW",
        level: "1",
        unit: "1",
        building: "ECLIPSE"
      }
    }
  }
};
const MOCK_TRIGGER_MAP: TriggerMap = {
  formInit: [],
  formSubmit: [],
  valueChange: new Map() // To store multiple actions that might be triggered for each dependent field
};

const Template = () => {
  const form = useStorybookForm(undefined, MOCK_FORM_DATA);
  const validationSchema = generateApplicationSchema(MOCK_SCHEMA.schema[0]);
  return (
    <QueryClientProvider client={new QueryClient()}>
      <StorybookApplicationPage>
        <GenericReviewComponent
          schemaId={MOCK_SCHEMA.schema[0].id}
          schemeCode={MOCK_SCHEMA.schemeCode}
          form={form}
          sections={MOCK_SCHEMA.schema[0].section}
          applicationSchema={MOCK_SCHEMA}
          validationSchema={validationSchema}
          triggerMap={MOCK_TRIGGER_MAP}
        />
      </StorybookApplicationPage>
    </QueryClientProvider>
  );
};

export const GenericReview: Story = {
  render: () => <Template />
};

import {
  QueryClient,
  QueryClientProvider,
  useQuery
} from "@tanstack/react-query";
import { useState, useMemo } from "react";
import { Redirect, Route, Switch, useHistory } from "react-router-dom";

import { getDraft } from "application/api/draft";
import { ApplicationState, SgwAppStatus, ApplyInfo } from "application/api/sgw";
import { ApplicationPage } from "application/composites/ApplicationPage";
import { FormMetadataProvider } from "application/context/FormMetadata";
import { parseSchema } from "application/utils/formTriggers";
import { Spinner } from "commons/components/Spinner";

import { ApplicationSchema } from "./GenericApplication.helper";
import {
  getAppPath,
  useAppStatusQuery,
  useAppSchemaQuery,
  useSchemeApplicationParams,
  getBaseAppPath
} from "./helper";
import { ApplyDashboard } from "./AppDashboard";
import { ApplyForm } from "application/templates/MultistepApplication/ApplyForm";

interface ApplicationProps {
  applications: SgwAppStatus[];
  applicationSchema: ApplicationSchema;
  applicationState: ApplicationState;
  maintenanceMsg?: string;
  ineligibleCode?: string;
  applyInfo?: ApplyInfo;
  agencyUnavailable?: boolean;
  isExternalForm?: boolean;
}

export const ApplyApplication = ({
  applications,
  applicationSchema,
  applicationState,
  maintenanceMsg,
  ineligibleCode,
  applyInfo,
  agencyUnavailable,
  isExternalForm
}: ApplicationProps) => {
  const schemeCode = applicationSchema.schemeCode;

  // `path` is for navigation, `basePath` is for routing
  const path = getAppPath(applicationSchema);
  const basePath = getBaseAppPath(schemeCode);

  const { push } = useHistory();
  const latestApplication = applications[0];

  const [selectedSchema, setSelectedSchema] = useState(
    applicationSchema.schema.length === 1
      ? applicationSchema.schema[0]
      : undefined
  );

  const triggerMap = useMemo(() => {
    return parseSchema(selectedSchema);
  }, [selectedSchema]);

  const { data: draft, isLoading } = useQuery(
    ["draft", schemeCode],
    () => getDraft(schemeCode),
    { enabled: applicationState === "allow" }
  );

  const ineligibleReason = ineligibleCode
    ? applicationSchema.ineligibleCodeToReason?.[ineligibleCode]
    : undefined;

  const renderOverview = () => {
    if (!selectedSchema) return <Redirect to={path.applyDashboard} />;
    if (isLoading) return <Spinner />;
    if (isExternalForm)
      return <Redirect to={`/grants/${schemeCode}/redirect`} />;

    return (
      <ApplyForm
        applicationSchema={applicationSchema}
        schema={selectedSchema}
        draft={draft?.content}
        triggerMap={triggerMap}
      />
    );
  };

  return (
    <FormMetadataProvider
      draft={draft}
      fileUploadExternal={applicationSchema.fileUploadExternal}
    >
      <Switch>
        <Route exact path={basePath.applyDashboard}>
          <ApplyDashboard
            schemeCode={schemeCode}
            schema={applicationSchema}
            application={latestApplication}
            applicationState={applicationState}
            ineligibleReason={ineligibleReason}
            onSelectedSchemaId={(id) => {
              const schema = applicationSchema.schema.find(
                (schema) => schema.id === id
              );
              if (schema) {
                setSelectedSchema(schema);
              }
              // HERE
              push(path.overview);
            }}
            maintenanceMsg={maintenanceMsg}
            applyInfo={applyInfo}
            agencyUnavailable={agencyUnavailable}
          />
        </Route>

        {applicationState !== "allow" && (
          <Route>
            <Redirect to={path.applyDashboard} />
          </Route>
        )}

        <Route path={basePath.overview}>{renderOverview()}</Route>

        <Route>
          <Redirect to={path.notFound} />
        </Route>
      </Switch>
    </FormMetadataProvider>
  );
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
      refetchOnReconnect: false,
      networkMode: "always"
    },
    mutations: {
      retry: false,
      networkMode: "always"
    }
  }
});

const GenericApplyApplication = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ApplicationGenerator />
    </QueryClientProvider>
  );
};

const ApplicationGenerator = () => {
  // Extracting scheme parameters from the URL
  const { contextSchemeCode, apiSchemeCodes } = useSchemeApplicationParams();

  const { appSchema, isFetchingAppSchema } = useAppSchemaQuery(
    contextSchemeCode,
    apiSchemeCodes
  );
  const {
    isLoading: isLoadingAppStatus,
    data: appStatus,
    isError
  } = useAppStatusQuery(contextSchemeCode, apiSchemeCodes);

  const path = getAppPath(appSchema);

  const {
    status: applications = [],
    applicationState = "allow",
    maintenanceMsg,
    ineligibleCode,
    applyInfo,
    agencyUnavailable = false,
    isExternalForm
  } = appStatus || {};

  if (isFetchingAppSchema) {
    return <Spinner />;
  }

  if (!appSchema) {
    return <Redirect to={path.notFound} />;
  }

  if (isError) {
    throw new Error("Error fetching application status"); // to be caught by react error boundary
  }

  return (
    <ApplicationPage
      title={appSchema.schemeName}
      subtitle={appSchema.subtitle}
      buttonText={appSchema.schemeDetailsLink ? "Scheme details" : "Home"}
      onBackButtonClick={() =>
        (window.location.href = appSchema.schemeDetailsLink || "/")
      }
    >
      {isLoadingAppStatus ? (
        <Spinner />
      ) : (
        <ApplyApplication
          applications={applications}
          applicationSchema={appSchema}
          applicationState={applicationState}
          maintenanceMsg={maintenanceMsg}
          ineligibleCode={ineligibleCode}
          applyInfo={applyInfo}
          agencyUnavailable={agencyUnavailable}
          isExternalForm={isExternalForm}
        />
      )}
    </ApplicationPage>
  );
};

export default GenericApplyApplication;

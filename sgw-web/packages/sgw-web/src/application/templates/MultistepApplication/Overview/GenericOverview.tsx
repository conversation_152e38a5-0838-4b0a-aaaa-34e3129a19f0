import { UseFormReturn } from "react-hook-form";
import {
  Redirect,
  useHistory,
  useLocation,
  useRouteMatch
} from "react-router-dom";
import { z } from "zod";

import { CenteredGridContainer } from "application/components/CenteredGridContainer";
import { SectionButton } from "application/components/SectionButton";
import { ButtonArrowRight } from "application/composites/ButtonArrowRight";
import { ContactInfoList } from "application/composites/ContactInfoList";
import { useOverlay } from "commons/app/context/overlay";
import { Breadcrumbs } from "commons/components/Breadcrumbs";

import { ApplicationSchema, SectionSchema } from "../GenericApplication.helper";
import {
  useAppStatusQuery,
  getAppPath,
  generateBreadcrumbLinks
} from "../helper";
import {
  SectionDescription,
  ButtonWrapper,
  ErrorWrapper,
  SectionFooter,
  SectionHeader,
  SectionMain
} from "./GenericOverview.styles";
import { MyInfo } from "application/templates/MultistepApplication/MyInfo";

interface GenericOverviewProps {
  schemaId: string;
  schemeCode: string;
  applicationSchema: ApplicationSchema;
  sections: SectionSchema[];
  validationSchema: z.ZodObject<z.ZodRawShape>;
  form: UseFormReturn;
}

export const GenericOverview = ({
  schemaId,
  schemeCode,
  applicationSchema,
  sections,
  validationSchema,
  form
}: GenericOverviewProps) => {
  const routeMatch = useRouteMatch();
  const { isOverlay } = useOverlay();
  const history = useHistory();
  const location = useLocation();

  const { contacts, bundledSchemeCodes } = applicationSchema;

  const path = getAppPath(applicationSchema);
  const { data, isLoading } = useAppStatusQuery(schemeCode, bundledSchemeCodes);

  const result = validationSchema.safeParse(form.getValues());
  const isCompletedForm = result.success;

  if (!isLoading && data?.applicationState !== "allow") {
    return (
      <Redirect
        to={{ pathname: path.applyDashboard, search: location.search }}
      />
    );
  }

  return (
    <>
      <Breadcrumbs
        links={generateBreadcrumbLinks(applicationSchema, "overview")}
      />
      <CenteredGridContainer>
        <SectionHeader>Application overview</SectionHeader>
        <SectionDescription>
          Complete the sections below in any order of your preference.
        </SectionDescription>
        <MyInfo
          schemeCode={schemeCode}
          form={form}
          schemaId={schemaId}
          applicationSchema={applicationSchema}
        />
        <SectionMain>
          {sections.map((section) => {
            const values = form.getValues(section.id);
            let isComplete = false;
            let errorCount = 0;

            if (values) {
              const result = validationSchema.shape[section.id].safeParse(
                values
              );
              isComplete = result.success;

              if (!result.success) {
                errorCount = new Set(
                  result.error.errors.map((error) => error.path.join("-"))
                ).size;
              }
            }
            return (
              <SectionButton
                key={section.id}
                title={section.title}
                icon={
                  isComplete ? "timelineMarker-green" : "timelineMarker-grey"
                }
                error={errorCount > 0 ? `${errorCount} field(s) left` : ""}
                onClick={() =>
                  history.push(
                    `${routeMatch.path}/${section.id}${location.search}`
                  )
                }
              />
            );
          })}
        </SectionMain>

        <SectionFooter>
          {!isCompletedForm && (
            <ErrorWrapper>Complete all mandatory fields.</ErrorWrapper>
          )}

          <ButtonWrapper>
            <ButtonArrowRight
              disabled={!isCompletedForm || isOverlay}
              buttonText="Review and submit"
              onClick={() =>
                history.push(`${routeMatch.path}/review${location.search}`)
              }
            />
          </ButtonWrapper>
        </SectionFooter>

        <ContactInfoList contacts={contacts} />
      </CenteredGridContainer>
    </>
  );
};

import { isFieldDisabled } from "./helper";
const testingData = [
  // nric prefill by session
  {
    fieldFullId: "profile.nric",
    prefillSource: "myInfo.nric",
    isDisabled: true
  },
  // myInfo prefill
  {
    fieldFullId: "profile.name",
    prefillSource: "myInfo.name",
    editable: true,
    isMyInfoUsed: true,
    isDisabled: false
  },
  {
    fieldFullId: "profile.name",
    prefillSource: "myInfo.name",
    editable: true,
    isMyInfoUsed: false,
    isDisabled: false
  },
  {
    fieldFullId: "profile.name",
    prefillSource: "myInfo.name",
    editable: false,
    isMyInfoUsed: true,
    isDisabled: true
  },
  {
    fieldFullId: "profile.name",
    prefillSource: "myInfo.name",
    editable: false,
    isMyInfoUsed: false,
    isDisabled: false
  },
  // no prefill
  {
    fieldFullId: "profile.name",
    isDisabled: false
  },
  {
    fieldFullId: "profile.name",
    editable: true,
    isDisabled: false
  },
  {
    fieldFullId: "profile.name",
    editable: false,
    isDisabled: false
  },
  // prefill from agency
  {
    fieldFullId: "profile.name",
    prefillSource: "gsg.name",
    editable: true,
    prefillFieldIds: ["profile.name", "profile.nric"],
    isDisabled: false
  },
  {
    fieldFullId: "profile.name",
    prefillSource: "gsg.name",
    editable: true,
    prefillFieldIds: [],
    isDisabled: false
  },
  {
    fieldFullId: "profile.name",
    prefillSource: "gsg.name",
    editable: false,
    prefillFieldIds: ["profile.name", "profile.nric"],
    isDisabled: true
  },
  {
    fieldFullId: "profile.name",
    prefillSource: "gsg.name",
    editable: false,
    prefillFieldIds: [],
    isDisabled: false
  },
  // edge case
  {
    fieldFullId: "profile.name",
    prefillSource: "gsg.name",
    editable: false,
    isDisabled: false
  },
  // defaultValue cases
  {
    fieldFullId: "profile.relationship",
    defaultValue: "Parent",
    isDisabled: true
  },
  {
    fieldFullId: "profile.relationship",
    defaultValue: "Parent",
    editable: true,
    isDisabled: true
  },
  {
    fieldFullId: "profile.relationship",
    defaultValue: "Parent",
    editable: false,
    isDisabled: true
  }
];

describe("isFieldDisabled", () => {
  testingData.forEach(
    ({
      fieldFullId,
      prefillSource,
      editable,
      prefillFieldIds,
      isMyInfoUsed,
      defaultValue,
      isDisabled
    }) => {
      it(`should field be disabled ${isDisabled} when the parameter are fieldFullId: ${fieldFullId}, prefillSource: ${prefillSource}, editable: ${editable}, isMyInfoUsed: ${isMyInfoUsed}, prefillFieldIds: ${prefillFieldIds}, defaultValue: ${defaultValue}`, () => {
        const testResult = isFieldDisabled(
          fieldFullId,
          prefillSource,
          editable,
          prefillFieldIds,
          isMyInfoUsed,
          defaultValue
        );
        expect(testResult).toEqual(isDisabled);
      });
    }
  );
});

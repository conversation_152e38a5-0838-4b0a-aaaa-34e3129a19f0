import { useQuery } from "@tanstack/react-query";
import z from "zod";

import {
  getPrefillData,
  getSgwApplicationStatus,
  getSchema,
  getOptionsData,
  getMergedSgwApplicationStatus,
  getMergedSchema
} from "application/api/sgw";
import { getMyInfoPerson } from "application/api/user";
import { fileUploadQueue } from "application/components/form/FileUploadAccordion";
import { isPrefillSourceMyInfoNric } from "application/composites/presetFields";
import { Breadcrumb } from "commons/components/Breadcrumbs";
import { toastMyInfoError } from "commons/toast/toast";
import { useEffect, useState, useMemo } from "react";
import {
  useHistory,
  useParams,
  useRouteMatch,
  useLocation
} from "react-router-dom";
import { useBeforeUnload } from "react-use";

import { application, ApplicationSchema } from "./GenericApplication.helper";

/**
 * Reconciles single-scheme and multi-scheme URL formats into a consistent object.
 * @returns An object with the context code, the codes for API calls, and a bundle flag.
 */
export const useSchemeApplicationParams = () => {
  const { schemeCode } = useParams<{ schemeCode: string }>();
  const location = useLocation();

  return useMemo(() => {
    const searchParams = new URLSearchParams(location.search);
    const schemesQuery = searchParams.get("schemes");

    const isBundle = !!schemesQuery;

    // The code(s) to be used for fetching data from the API
    const apiSchemeCodes = isBundle
      ? schemesQuery.split(",").sort()
      : [schemeCode];

    // sanitise query params to ensure they are valid
    z.string()
      .regex(/^[a-z0-9]+$/) // alphanumeric characters only
      .array()
      .parse(apiSchemeCodes);

    return {
      /** The code from the URL path, used for UI context and routing (e.g., 'saf' or 'smta'). */
      contextSchemeCode: schemeCode,
      /** The actual scheme codes to be sent to the API. */
      apiSchemeCodes: apiSchemeCodes,
      /** A boolean flag indicating if this is a multi-scheme bundle. */
      isBundle: isBundle
    };
  }, [location.search, schemeCode]);
};

/**
 * Fetches the application schema, supporting both single and multi-scheme bundles.
 * @param contextSchemeCode - The scheme code from the URL path, used for query key context.
 * @param apiSchemeCodes - The list of actual scheme codes to fetch.
 */
export const useAppSchemaQuery = (
  contextSchemeCode: string,
  apiSchemeCodes?: string[]
) => {
  const safeApiSchemeCodes = apiSchemeCodes ?? [];

  const query = useQuery({
    queryKey: ["getSchema", contextSchemeCode, safeApiSchemeCodes.join(",")],
    queryFn: () => {
      if (safeApiSchemeCodes.length > 1) {
        return getMergedSchema(safeApiSchemeCodes);
      }
      return getSchema(contextSchemeCode);
    }
  });

  // Derive the validated schema from the query data using useMemo
  const appSchema = useMemo(() => {
    const schemaData = query.data?.schema || query.data;
    const result = application.safeParse(schemaData);
    return result.success ? result.data : undefined;
  }, [query.data]);

  return {
    // Return the validated schema and the query's fetching status directly
    appSchema: appSchema,
    isFetchingAppSchema: query.isFetching
  };
};

/**
 * Fetches the application status, supporting both single and multi-scheme bundles.
 * @param contextSchemeCode - The scheme code from the URL path, for query key context.
 * @param apiSchemeCodes - The list of actual scheme codes to fetch.
 * @param enabled - Standard react-query 'enabled' flag.
 */
export const useAppStatusQuery = (
  contextSchemeCode: string,
  apiSchemeCodes?: string[],
  enabled = true
) => {
  const safeApiSchemeCodes = apiSchemeCodes ?? [contextSchemeCode];

  return useQuery({
    queryKey: [
      "applicationStatus",
      contextSchemeCode,
      safeApiSchemeCodes.join(",")
    ],
    queryFn: () => {
      if (safeApiSchemeCodes.length > 1) {
        return getMergedSgwApplicationStatus(safeApiSchemeCodes);
      }
      return getSgwApplicationStatus(contextSchemeCode);
    },
    enabled,

    // are the below necessary? We shouldn't mount this query so often that we need to set stale/cache time
    staleTime: 15 * 60 * 1000,
    cacheTime: 15 * 60 * 1000
  });
};

/**
 * Custom React Query hook for fetching MyInfo person data.
 * Handles the query execution and displays a toast notification on error.
 */
export const useMyInfoPersonQuery = (
  schemeCode: string,
  schemaId: string,
  bundledSchemeCodes: string[] = [],
  options: { enabled: boolean }
) => {
  const query = useQuery(
    ["myInfoPerson", schemeCode, schemaId],
    () => getMyInfoPerson(schemeCode, schemaId, bundledSchemeCodes),
    options
  );

  useEffect(() => {
    if (query.isError) {
      toastMyInfoError();
    }
  }, [query.isError]);

  return query;
};

/**
 * Custom React Query hook for fetching prefill data.
 * It retrieves prefill data based on the scheme code, schema ID, and optional bundled scheme codes.
 * The query is enabled based on the provided options.
 */
const usePrefillQuery = (
  schemeCode: string,
  schemaId: string,
  bundledSchemeCodes: string[] = [],
  options: { enabled: boolean }
) =>
  useQuery(
    ["prefill", schemeCode, schemaId],
    () => getPrefillData(schemeCode, schemaId, bundledSchemeCodes),
    options
  );

export const useOptionsQuery = (
  schemeCode: string,
  refId: string,
  enabled: boolean
) =>
  useQuery(
    ["options", schemeCode, refId],
    () => getOptionsData(schemeCode, refId),
    { enabled }
  );

/**
 * Custom hook to fetch prefill data for an application.
 * It combines MyInfo and prefill queries, managing the loading state and prefill data.
 * The hook also tracks whether prefill data has been successfully retrieved.
 * It returns an object containing the prefill data, loading state, and queries for MyInfo and prefill.
 * @param schemeCode - The scheme code for the application.
 * @param schemaId - The schema ID for the application.
 * @param bundledSchemeCodes - An optional array of bundled scheme codes for multi-scheme applications.
 * @param enablePrefillQuery - A boolean flag to enable the prefill query.
 * @param enableMyInfoQuery - A boolean flag to enable the MyInfo query (default is true).
 * @returns An object containing:
 * - `hasPrefilled`: A boolean indicating if prefill data has been retrieved.
 * - `prefillData`: The prefill data object, or undefined if still loading.
 * - `myInfoQuery`: The query object for MyInfo data.
 * - `prefillQuery`: The query object for prefill data.
 */
export const usePrefillData = (
  schemeCode: string,
  schemaId: string,
  bundledSchemeCodes: string[] = [],
  enablePrefillQuery: boolean,
  enableMyInfoQuery = true
) => {
  const [hasPrefilled, setHasPrefilled] = useState(false);

  const myInfoQuery = useMyInfoPersonQuery(
    schemeCode,
    schemaId,
    bundledSchemeCodes,
    {
      enabled: !hasPrefilled && enableMyInfoQuery
    }
  );

  const prefillQuery = usePrefillQuery(
    schemeCode,
    schemaId,
    bundledSchemeCodes,
    {
      enabled: !hasPrefilled && enablePrefillQuery
    }
  );

  const isLoading = myInfoQuery.isLoading || prefillQuery.isLoading;

  const prefillData = useMemo(() => {
    if (!isLoading) {
      setHasPrefilled(true);
      const myInfoData = myInfoQuery.data?.data;

      let prefillFieldIds: string[] = [];
      if (prefillQuery.data !== undefined) {
        prefillFieldIds = Object.keys(prefillQuery.data);
      }

      return {
        ...myInfoData,
        myInfoUsed: !!myInfoData,
        ...prefillQuery.data,
        prefillFieldIds
      };
    }

    return undefined;
  }, [isLoading, myInfoQuery.data, prefillQuery.data]);

  return { hasPrefilled, prefillData, myInfoQuery, prefillQuery };
};

/**
 * Generates application paths based on the application schema and reference ID.
 * This function supports both single-scheme and multi-scheme applications.
 * It constructs paths for various application-related pages, including query parameters for bundled schemes.
 * @param application - The application schema object containing scheme codes and bundled scheme codes.
 * @param refId - An optional reference ID for the application, used to generate specific paths.
 * @returns An object containing the full paths for different application pages, with query parameters if applicable.
 *          The keys are descriptive names for each path, and the values are the corresponding URLs.
 */
export const getAppPath = (application?: ApplicationSchema, refId?: string) => {
  const schemeCode = application?.schemeCode;
  const basePaths = getBaseAppPath(schemeCode, refId);

  // support for bundled schemes pages' URL (<basePath>?schemes=code1,code2)
  const queryParams = application?.bundledSchemeCodes
    ? { schemes: application.bundledSchemeCodes }
    : undefined;

  const fullAppPaths = {};
  for (const key in basePaths) {
    const basePath = basePaths[key];
    fullAppPaths[key] = buildUrl(basePath, queryParams);
  }
  return fullAppPaths as typeof basePaths;
};

/**
 * Builds a URL with query parameters from a base path and an optional params object.
 * @param basePath - The base path to which query parameters will be appended.
 * @param params - An optional object containing key-value pairs for query parameters.
 * @returns A string representing the full URL with query parameters.
 */
export const buildUrl = (
  basePath: string,
  params?: Record<string, string | string[] | undefined>
): string => {
  if (!params) return basePath;
  const searchParams = new URLSearchParams();
  for (const [key, value] of Object.entries(params)) {
    if (value) {
      searchParams.set(key, Array.isArray(value) ? value.join(",") : value);
    }
  }
  const queryString = searchParams.toString();
  return queryString ? `${basePath}?${queryString}` : basePath;
};

/**
 * This function is to generate base application paths based on the scheme code and reference ID.
 * IMPORTANT NOTE: To use the `getAppPath` as much as possible, this function is ONLY to be used for cases
 * where `ApplicationSchema` is UNAVAILABLE (e.g. My Application dashboard page).
 * @param schemeCode - The scheme code for the application, used to construct the base path.
 * @param refId - The reference ID for the application, used to construct specific paths.
 */
export const getBaseAppPath = (schemeCode?: string, refId?: string) => ({
  applyDashboard: `/grants/${schemeCode}/apply`,
  dashboard: `/grants/${schemeCode}/${refId}`,

  get overview() {
    return this.applyDashboard + "/application";
  },
  get section() {
    return this.applyDashboard + "/application/:sectionId";
  },
  get review() {
    return this.applyDashboard + "/application/review";
  },
  get documents() {
    return this.dashboard + "/documents";
  },
  get consent() {
    return this.dashboard + "/consent";
  },
  get safSelection() {
    return "/grants/saf/selection";
  },
  get myApplications() {
    return "/my-applications";
  },
  get notFound() {
    return "/404";
  }
});

// Defines the sequence of breadcrumbs for each page.
const BREADCRUMB_HIERARCHY: Record<string, string[]> = {
  overview: ["APPLY_DASHBOARD", "OVERVIEW"],
  section: ["APPLY_DASHBOARD", "OVERVIEW"],
  review: ["APPLY_DASHBOARD", "OVERVIEW"],
  documents: ["DASHBOARD"]
};

/**
 * Generates a breadcrumb trail for a given location using the getAppPath helper.
 * @param application - The full application schema object.
 * @param location - The key for the current page location (e.g., "overview").
 * @param title - The title of the current (active) page.
 * @param refId - An optional reference ID for path generation.
 * @returns An array of Breadcrumb objects with final paths.
 */
export const generateBreadcrumbLinks = (
  application: ApplicationSchema,
  location: string,
  title = "",
  refId?: string
): Breadcrumb[] => {
  // Get all paths (with query strings if applicable)
  const finalPaths = getAppPath(application, refId);

  // Define the link objects using the fully-formed paths
  const linkTemplates = {
    APPLY_DASHBOARD: { label: "Dashboard", path: finalPaths.applyDashboard },
    DASHBOARD: { label: "Dashboard", path: finalPaths.dashboard },
    OVERVIEW: { label: "Overview", path: finalPaths.overview }
  };

  // Determine the base path from the hierarchy map
  const pathKeys = BREADCRUMB_HIERARCHY[location] || [];
  const links = pathKeys.map((key) => linkTemplates[key]);

  // Add the final "active" page breadcrumb if needed
  if (["section", "review", "documents"].includes(location)) {
    links.push({ label: title, path: "" });
  }

  return links;
};

export const useLoseChangesGuard = (hasUnsavedChanges: boolean) => {
  const LOSE_CHANGES_MESSAGE = "Proceed? You will lose your changes.";
  const { block } = useHistory();
  const { url } = useRouteMatch();

  // browser navigation guard (refresh, redirect etc.)
  useBeforeUnload(
    () => hasUnsavedChanges || fileUploadQueue.isUploading(),
    LOSE_CHANGES_MESSAGE //required for chrome and safari
  );

  // react navigation guard (change of component)
  useEffect(() => {
    const unblock = block((destUrl) => {
      if (hasUnsavedChanges && !destUrl.pathname.includes(url)) {
        return LOSE_CHANGES_MESSAGE;
      } else if (fileUploadQueue.isUploading()) {
        if (!window.confirm(LOSE_CHANGES_MESSAGE)) {
          return false;
        } else {
          fileUploadQueue.clear();
        }
      }
    });

    return () => unblock();
  }, [block, hasUnsavedChanges, url]);
};

export const isFieldDisabled = (
  fieldFullId: string,
  prefillSource?: string,
  editable?: boolean,
  prefillFieldIds?: string[],
  isMyInfoUsed?: boolean,
  defaultValue?: string
) => {
  if (defaultValue) {
    return true;
  }

  if (prefillSource) {
    // MyInfo prefill: check NRIC, or isMyInfoUsed and not editable
    if (prefillSource.startsWith("myInfo")) {
      if (isPrefillSourceMyInfoNric(prefillSource)) {
        return true;
      }

      return isMyInfoUsed && !editable;
    }

    // Agency prefill: disabled if field is in prefillFieldIds and not editable
    if (Array.isArray(prefillFieldIds)) {
      return prefillFieldIds.includes(fieldFullId) && !editable;
    }
  }

  return false;
};

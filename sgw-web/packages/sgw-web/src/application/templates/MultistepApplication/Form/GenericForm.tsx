import { get } from "lodash";
import { useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { Redirect, useHistory, useLocation, useParams } from "react-router-dom";
import { z } from "zod";

import { FileScanStep } from "application/api/fileV2";
import { CenteredGridContainer } from "application/components/CenteredGridContainer";
import { SectionHeader } from "application/components/SectionHeader";
import { customFieldSelector } from "application/composites/customFields/helper";
import { FileUpload } from "application/composites/customGroups";
import { Tnc } from "application/composites/customGroups/TermsAndConditions";
import { Header, InfoBlock } from "application/composites/decorators";
import {
  GroupConditional,
  GlobalGroupConditional,
  MultiValue,
  SectionConditional,
  GlobalSectionConditional
} from "application/composites/dynamicFields";
import { presetFieldSelector } from "application/composites/presetFields";
import { SectionNavigation } from "application/composites/SectionNavigation";
import { useDraft } from "application/context/FormMetadata";
import {
  generateBreadcrumbLinks,
  getAppPath,
  isFieldDisabled
} from "application/templates/MultistepApplication/helper";
import { Accordion, AccordionItem } from "commons/components/Accordion";
import { Breadcrumbs } from "commons/components/Breadcrumbs";
import { cl } from "commons/styles";

import {
  CustomFieldSchema,
  CustomGroupSubType,
  DecoratorSubType,
  GroupConditionalSchema,
  GroupMemberSubType,
  MultiValueSchema,
  PresetFieldAddressSchema,
  PresetFieldEmailSchema,
  PresetFieldSchema,
  PresetFieldSelectSchema,
  SectionConditionalSchema,
  SectionMemberSubType,
  SectionSchema,
  generateGroupMemberSchema,
  PresetFieldNricSchema,
  PresetFieldMsfBankRefundSchema,
  PresetFieldMsfLetterOfUndertakingSchema,
  PresetFieldFamilySchema,
  PresetFieldMsfPaynowRefundSchema,
  PresetFieldMsfOmnibusConsentSchema,
  ApplicationSchema
} from "../GenericApplication.helper";
import { useAppStatusQuery } from "../helper";
import { GroupMemberSpacing, SectionMemberSpacing } from "./GenericForm.styles";

interface GenericSectionProps {
  schemeCode: string;
  applicationSchema: ApplicationSchema;
  sections: SectionSchema[];
  form: UseFormReturn;
  validationSchema: z.ZodObject<z.ZodRawShape>;
  myInfoOptions?: Record<string, any>;
}

export const GenericSection = ({
  schemeCode,
  applicationSchema,
  sections,
  form,
  validationSchema,
  myInfoOptions
}: GenericSectionProps) => {
  const path = getAppPath(applicationSchema);
  const { sectionId } = useParams<{ sectionId: string }>();
  const history = useHistory();
  const draft = useDraft(form, schemeCode);
  const { data } = useAppStatusQuery(
    schemeCode,
    applicationSchema.bundledSchemeCodes
  );

  if (data?.applicationState !== "allow") {
    return <Redirect to={path.applyDashboard} />;
  }

  const { section, nextSection } = getSection(sections, sectionId);
  if (!section) {
    return <Redirect to={path.notFound} />;
  }

  const handlePageChange = (nextSectionId?: string) => {
    const nextPage = nextSectionId
      ? path.section.replace(":sectionId", nextSectionId)
      : path.overview;

    if (
      !["emps", "h2w", "pwdr"].includes(schemeCode) &&
      form.formState.isDirty
    ) {
      draft.mutate(undefined, {
        onSettled: () => history.push(nextPage)
      });
    } else {
      history.push(nextPage);
    }
  };

  return (
    <>
      <Breadcrumbs
        links={generateBreadcrumbLinks(
          applicationSchema,
          "section",
          section.title
        )}
      />

      <CenteredGridContainer backgroundColor={cl.white}>
        <SectionHeader
          title={section.title}
          subtitle={section.subtitle}
          style={{ marginBottom: 0 }}
        />
        <Section
          schemeCode={schemeCode}
          section={section}
          form={form}
          sectionSchema={validationSchema.shape[section.id]}
          myInfoOptions={myInfoOptions}
        />
      </CenteredGridContainer>

      <SectionNavigation
        nextSectionName={nextSection?.title}
        toNextSection={() => handlePageChange(nextSection?.id)}
        toOverview={() => handlePageChange()}
      />
    </>
  );
};

type SectionProps = {
  schemeCode: string;
  section: SectionSchema;
  form: UseFormReturn;
  sectionSchema: any;
  myInfoOptions?: Record<string, any>;
};

export const Section = ({
  section,
  form,
  sectionSchema,
  schemeCode,
  myInfoOptions
}: SectionProps) => {
  const { pathname } = useLocation();

  const firstGroupIndex = section.member.findIndex(
    (item) => item.type === "CUSTOM_GROUP"
  );

  useEffect(() => {
    return () => {
      form.trigger(section.id);
    };
  }, [pathname, form, section.id]);

  return (
    <SectionMemberSpacing>
      {section.member.map((member, index) => {
        const isExpandGroup = firstGroupIndex === index;

        return generateSectionMember(
          member,
          form,
          section.id,
          sectionSchema,
          schemeCode,
          isExpandGroup,
          undefined,
          myInfoOptions
        );
      })}
    </SectionMemberSpacing>
  );
};

export const generateSectionMember = (
  member: SectionMemberSubType,
  form: UseFormReturn,
  namespace: string,
  sectionSchema: any, // used by accordion to determine its error or success state
  schemeCode: string,
  isExpandGroup: boolean,
  step?: FileScanStep, // required for file scanning during file upload. Appgen file upload field sets the step to 'application' by default. Set step value when not application flow.
  myInfoOptions?: Record<string, any>,
  sgwOptions?: Record<string, any>
) => {
  switch (member.type) {
    case "DECORATOR":
      return generateDecorator(member);
    case "CUSTOM_GROUP":
      return generateCustomGroup(
        member,
        form,
        namespace,
        sectionSchema.shape[member.id],
        schemeCode,
        isExpandGroup,
        step,
        myInfoOptions,
        sgwOptions
      );
    case "MULTI_VALUE":
      return generateMultiValue(
        member,
        form,
        namespace,
        schemeCode,
        sectionSchema.shape[member.id].shape[member.group.id],
        step,
        myInfoOptions,
        sgwOptions
      );
    case "SECTION_CONDITIONAL":
      return generateSectionConditional(
        member,
        form,
        namespace,
        schemeCode,
        step,
        myInfoOptions,
        sgwOptions
      );
    case "GLOBAL_SECTION_CONDITIONAL":
      return (
        <GlobalSectionConditional
          schema={member}
          form={form}
          namespace={namespace}
          schemeCode={schemeCode}
          step={step}
          myInfoOptions={myInfoOptions}
          sgwOptions={sgwOptions}
        />
      );
    case "CUSTOM_FIELD":
      return generateField(member, form, namespace, schemeCode, step);
    case "PRESET_FIELD":
      return generateField(
        member,
        form,
        namespace,
        schemeCode,
        step,
        myInfoOptions,
        sgwOptions
      );
    default:
      throw new Error("no such section member type");
  }
};

const generateDecorator = (decorator: DecoratorSubType) => {
  if (decorator.subType === "HEADER") {
    return (
      <Header title={decorator.title} description={decorator.description} />
    );
  } else if (decorator.subType === "INFO_BLOCK") {
    return <InfoBlock description={decorator.title} />;
  }
};

export const generateGroupMember = (
  member: GroupMemberSubType,
  form: UseFormReturn,
  namespace: string,
  schemeCode: string,
  step?: FileScanStep,
  myInfoOptions?: Record<string, any>,
  sgwOptions?: Record<string, any>
) => {
  if (member.type === "DECORATOR") {
    return generateDecorator(member);
  } else if (member.type === "CUSTOM_FIELD") {
    return generateField(member, form, namespace, schemeCode, step);
  } else if (member.type === "PRESET_FIELD") {
    return generateField(
      member,
      form,
      namespace,
      schemeCode,
      step,
      myInfoOptions,
      sgwOptions
    );
  } else if (member.type === "GROUP_CONDITIONAL") {
    return generateGroupConditional(
      member,
      form,
      namespace,
      schemeCode,
      step,
      myInfoOptions,
      sgwOptions
    );
  } else if (member.type === "GLOBAL_GROUP_CONDITIONAL") {
    return (
      <GlobalGroupConditional
        schema={member}
        form={form}
        namespace={namespace}
        schemeCode={schemeCode}
        step={step}
        myInfoOptions={myInfoOptions}
        sgwOptions={sgwOptions}
      />
    );
  }
};

export const generateCustomGroup = (
  group: CustomGroupSubType,
  form: UseFormReturn,
  namespace: string,
  groupSchema: any, // used by accordion to determine its error or success state
  schemeCode: string,
  isExpand: boolean,
  step?: FileScanStep,
  myInfoOptions?: Record<string, any>,
  sgwOptions?: Record<string, any>
) => {
  if (group.subType === "BLANK") {
    const memberSchema = generateGroupMemberSchema(group.member);

    return (
      <Accordion
        key={`${namespace}.${group.id}`}
        accordionTitle={group.title}
        type={getAccordionType(namespace, group.id, form, memberSchema)}
        isInitialExpand={isExpand}
      >
        <AccordionItem>
          <GroupMemberSpacing>
            {group.member.map((member) => {
              return generateGroupMember(
                member,
                form,
                `${namespace}.${group.id}`,
                schemeCode,
                step,
                myInfoOptions,
                sgwOptions
              );
            })}
          </GroupMemberSpacing>
        </AccordionItem>
      </Accordion>
    );
  } else if (group.subType === "FILE_UPLOAD") {
    return (
      <FileUpload
        key={`${namespace}.${group.id}`}
        form={form}
        namespace={namespace}
        schemeCode={schemeCode}
        group={group}
        isInitialExpand={isExpand}
        step={step}
      />
    );
  } else if (group.subType === "TNC") {
    return (
      <Tnc
        key={`${namespace}.${group.id}`}
        form={form}
        sectionId={namespace}
        group={group}
        groupSchema={groupSchema}
        isInitialExpand={isExpand}
      />
    );
  }
};

export const generateField = (
  field:
    | CustomFieldSchema
    | PresetFieldSchema
    | PresetFieldNricSchema
    | PresetFieldEmailSchema
    | PresetFieldFamilySchema
    | PresetFieldMsfBankRefundSchema
    | PresetFieldMsfPaynowRefundSchema
    | PresetFieldMsfLetterOfUndertakingSchema
    | PresetFieldMsfOmnibusConsentSchema
    | PresetFieldAddressSchema
    | PresetFieldSelectSchema,
  form: UseFormReturn,
  namespace: string,
  schemeCode: string,
  step?: FileScanStep,
  myInfoOptions?: Record<string, any>,
  sgwOptions?: Record<string, any>
) => {
  const myInfoUsed = form.getValues("myInfoUsed");
  const prefillFieldIds = form.getValues("prefillFieldIds");
  const isDisabled = isFieldDisabled(
    `${namespace}.${field.id}`,
    field.prefillSource,
    field.editable,
    prefillFieldIds,
    myInfoUsed,
    field.globalAction?.defaultValue
  );

  if (field.type === "CUSTOM_FIELD") {
    return customFieldSelector(
      field,
      `${namespace}.${field.id}`,
      form,
      schemeCode,
      step,
      isDisabled
    );
  } else if (field.type === "PRESET_FIELD") {
    return presetFieldSelector(
      field,
      `${namespace}.${field.id}`,
      form,
      schemeCode,
      step,
      isDisabled,
      myInfoUsed ? myInfoOptions : undefined,
      sgwOptions
    );
  }
};

const generateMultiValue = (
  multiValue: MultiValueSchema,
  form: UseFormReturn,
  namespace: string,
  schemeCode: string,
  groupSchema: any,
  step?: FileScanStep,
  myInfoOptions?: Record<string, any>,
  sgwOptions?: Record<string, any>
) => {
  return (
    <MultiValue
      key={`${namespace}.${multiValue.id}`}
      form={form}
      namespace={namespace}
      schemeCode={schemeCode}
      multiValue={multiValue}
      groupSchema={groupSchema}
      step={step}
      myInfoOptions={myInfoOptions}
      sgwOptions={sgwOptions}
    />
  );
};

const generateSectionConditional = (
  sectionConditional: SectionConditionalSchema,
  form: UseFormReturn,
  namespace: string,
  schemeCode: string,
  step?: FileScanStep,
  myInfoOptions?: Record<string, any>,
  sgwOptions?: Record<string, any>
) => {
  return (
    <SectionConditional
      key={`${namespace}.${sectionConditional.id}`}
      form={form}
      namespace={namespace}
      sectionConditional={sectionConditional}
      schemeCode={schemeCode}
      step={step}
      myInfoOptions={myInfoOptions}
      sgwOptions={sgwOptions}
    />
  );
};

const generateGroupConditional = (
  groupConditional: GroupConditionalSchema,
  form: UseFormReturn,
  namespace: string,
  schemeCode: string,
  step?: FileScanStep,
  myInfoOptions?: Record<string, any>,
  sgwOptions?: Record<string, any>
) => {
  return (
    <GroupConditional
      key={`${namespace}.${groupConditional.id}`}
      form={form}
      namespace={namespace}
      schemeCode={schemeCode}
      groupConditional={groupConditional}
      step={step}
      myInfoOptions={myInfoOptions}
      sgwOptions={sgwOptions}
    />
  );
};

const getSection = (sections: SectionSchema[], sectionId: string) => {
  let section: SectionSchema | undefined;
  let nextSection: SectionSchema | undefined;

  for (let i = 0; i < sections.length; i++) {
    if (sections[i].id === sectionId) {
      section = sections[i];
      nextSection = i + 1 === sections.length ? undefined : sections[i + 1];
    }
  }

  return { section, nextSection };
};

const getAccordionType = (
  namespace: string,
  groupId: string,
  form: UseFormReturn,
  groupSchema: any
) => {
  const values = form.getValues(`${namespace}.${groupId}`);
  const errors = get(form.formState.errors, `${namespace}.${groupId}`);
  const hasRequiredFields =
    groupSchema.shape &&
    Object.values(groupSchema.shape).some(
      (field: any) => field.isOptional() === false
    );

  const isCompleted = groupSchema.safeParse(values).success;

  if (isCompleted && hasRequiredFields) {
    return "Success";
  }
  if (errors && Object.keys(errors).length > 0) {
    return "Error";
  }
  return "Default";
};

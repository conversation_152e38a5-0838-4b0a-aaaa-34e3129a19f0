import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useState, useRef } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { Redirect } from "react-router-dom";
import { z } from "zod";

import { getSgwConsentStatus, submitSgwConsent } from "application/api/sgw";
import { CenteredGridContainer } from "application/components/CenteredGridContainer";
import { SectionHeader } from "application/components/SectionHeader";
import { useOverlay } from "commons/app/context/overlay";
import { Button } from "commons/components/buttons/Button";
import ErrorMessage from "commons/components/ErrorMessage";
import { cl } from "commons/styles";

import { Section } from "../Form/GenericForm";
import {
  ApplicationSchema,
  generateApplicationSchema,
  SectionSchema
} from "../GenericApplication.helper";
import { getAppPath, useLoseChangesGuard } from "../helper";
import { MyInfo } from "../MyInfo";
import { GenericSuccess } from "../Success";
import { ButtonContainer, ErrorWrapper } from "./GenericConsent.styles";
import { zodResolver } from "@hookform/resolvers/zod";
import { getObjectFromPath } from "application/utils";
import { Spinner } from "commons/components/Spinner";

interface GenericConsentProps {
  refId: string;
  schemeCode: string;
  applicationSchema: ApplicationSchema;
  myInfoOptions?: Record<string, any>;
  prefillData?: Record<string, any>;
}

export const GenericConsent = ({
  refId,
  schemeCode,
  applicationSchema,
  myInfoOptions,
  prefillData
}: GenericConsentProps) => {
  const { schemeName, consentSchema: schema } = applicationSchema;

  if (!schema) {
    throw new Error("Consent schema is required for GenericConsent");
  }

  const validationSchema = generateApplicationSchema(schema);
  const resolver = zodResolver(validationSchema);
  const form = useForm({
    mode: "onTouched",
    resolver,
    defaultValues: prefillData ? getObjectFromPath(prefillData) : undefined
  });

  const [isSuccess, setIsSuccess] = useState(false);
  const [email, setEmail] = useState("");
  const path = getAppPath(applicationSchema, refId);

  const { data, isLoading, isError, error } = useQuery(
    ["applicationConsent", schemeCode],
    () => getSgwConsentStatus(schemeCode, refId)
  );

  const { isDirty } = form.formState;
  useLoseChangesGuard(!isSuccess && isDirty);

  if (isLoading) return <Spinner />;

  if (isError) {
    throw new Error("could not get consent status" + error); // let react error boundary catch this error and display error page
  }

  if (!data.applicantName) {
    return <Redirect to={path.myApplications} />;
  }

  if (isSuccess) {
    return (
      <GenericSuccess
        form={form}
        applicationSchema={applicationSchema}
        schemeCode={schemeCode}
        schemes={[
          {
            name: schemeName,
            refId,
            nextSteps:
              "The main applicant will be notified that you have given the consent."
          }
        ]}
        email={email}
        returnPage="homepage"
      />
    );
  }

  return (
    <Consent
      schemeCode={schemeCode}
      applicationSchema={applicationSchema}
      applicantName={data.applicantName}
      consentRefId={refId}
      form={form}
      consentSection={schema.section[0]}
      validationSchema={validationSchema}
      myInfoOptions={myInfoOptions}
      onSuccess={(email) => {
        setEmail(email);
        setIsSuccess(true);
      }}
    />
  );
};

interface ConsentProps {
  schemeCode: string;
  applicationSchema: ApplicationSchema;
  applicantName?: string;
  consentRefId: string;
  form: UseFormReturn;
  consentSection: SectionSchema;
  validationSchema: z.ZodObject<z.ZodRawShape>;
  myInfoOptions?: Record<string, any>;
  onSuccess: (email: string) => void;
}

const Consent = ({
  schemeCode,
  applicationSchema,
  applicantName,
  consentRefId,
  form,
  consentSection,
  validationSchema,
  myInfoOptions,
  onSuccess
}: ConsentProps) => {
  const {
    formState: { errors }
  } = form;

  const { isOverlay, setIsOverlay } = useOverlay();
  const [isSubmitError, setIsSubmitError] = useState(false);
  const isSubmitting = useRef(false);

  const consent = useMutation(
    () => submitSgwConsent(schemeCode, consentRefId, form.getValues()),
    {
      onMutate: () => {
        setIsOverlay(true);
        setIsSubmitError(false);
      },
      onSuccess: (data) => {
        onSuccess(data.email);
      },
      onError: () => {
        setIsSubmitError(true);
      },
      onSettled: () => {
        setIsOverlay(false);
      }
    }
  );

  const submitConsent = async () => {
    if (isSubmitting.current) return;
    isSubmitting.current = true;
    try {
      await consent.mutateAsync();
    } catch (err) {
      console.error("error submitting consent: ", err);
    } finally {
      isSubmitting.current = false;
    }
  };

  const isDisableButton =
    isOverlay || (errors && Object.keys(errors).length > 0);

  return (
    <form onSubmit={form.handleSubmit(submitConsent)}>
      <CenteredGridContainer backgroundColor={cl.white}>
        <SectionHeader
          title="Consent"
          subtitle={`Your consent is required for ${applicantName}’s application.`}
        />
        <MyInfo
          schemeCode={schemeCode}
          form={form}
          schemaId="consent"
          applicationSchema={applicationSchema}
        />
        <Section
          schemeCode={schemeCode}
          section={consentSection}
          form={form}
          sectionSchema={validationSchema.shape[consentSection.id]}
          myInfoOptions={myInfoOptions}
        />
      </CenteredGridContainer>

      <ButtonContainer>
        {isSubmitError && (
          <ErrorWrapper>
            <ErrorMessage>
              Our system is experiencing some issues. Please try again later.
            </ErrorMessage>
          </ErrorWrapper>
        )}
        <Button
          disabled={isDisableButton}
          type="submit"
          buttonScheme="first"
          buttonText="Submit"
        />
      </ButtonContainer>
    </form>
  );
};

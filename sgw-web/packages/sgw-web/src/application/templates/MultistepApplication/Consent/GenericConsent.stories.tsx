import { <PERSON>a, <PERSON>Obj } from "@storybook/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { StorybookApplicationPage } from "application/components/form/storybookUtils";

import { ApplicationSchema } from "../GenericApplication.helper";

import { GenericConsent as GenericConsentComponent } from "./GenericConsent";

const meta: Meta<typeof GenericConsentComponent> = {
  title: "Application Generator/Template/Generic Consent",
  component: GenericConsentComponent
};

export default meta;

type Story = StoryObj<typeof GenericConsentComponent>;

const consentSchema: ApplicationSchema = {
  schemeName: "Fake Schema",
  schemeCode: "schemeCode",
  schemeDetailsLink: "/",
  subtitle: "Fake Scheme subtitle",
  dashboard: {
    applicationGuidance: "Lorem ipsum"
  },
  contacts: [
    {
      faqLink: "https://supportgowhere.life.gov.sg"
    }
  ],
  agencyControlled: false,
  nextSteps: "Fake next steps",
  schema: [
    {
      id: "main",
      section: [
        {
          id: "profile",
          title: "consent (not used)",
          member: [
            {
              type: "CUSTOM_FIELD",
              subType: "FREE_TEXT",
              id: "freeText",
              title: "Free Text"
            }
          ]
        }
      ]
    }
  ],
  consentSchema: {
    section: [
      {
        id: "consent",
        title: "consent (not used)",
        member: [
          {
            type: "DECORATOR",
            subType: "INFO_BLOCK",
            title: [
              "If you are not aware of the above application, or not the intended family member, please email us for assistance."
            ]
          },
          {
            type: "PRESET_FIELD",
            subType: "name",
            id: "name",
            prefillSource: "myInfo.name",
            editable: false
          },
          {
            type: "PRESET_FIELD",
            subType: "nric",
            id: "nric",
            prefillSource: "myInfo.nric",
            editable: false
          },
          {
            type: "DECORATOR",
            subType: "HEADER",
            title: "Contact details"
          },
          {
            type: "PRESET_FIELD",
            subType: "mobileNumber",
            id: "mobileNumber",
            prefillSource: "myInfo.mobileNumber",
            editable: true
          },
          {
            type: "PRESET_FIELD",
            subType: "homeNumber",
            id: "homeNumber",
            optional: true,
            prefillSource: "myInfo.homeNumber",
            editable: true
          },
          {
            type: "PRESET_FIELD",
            subType: "email",
            id: "email",
            optional: true,
            prefillSource: "myInfo.email",
            editable: true
          },
          {
            type: "CUSTOM_GROUP",
            subType: "TNC",
            id: "dataSharing",
            title: "Consent to data sharing",
            acknowledge: "I acknowledge and consent to the terms above.",
            content: [
              {
                header:
                  "Consent for collection, use and disclosure of personal information",
                indent: [
                  {
                    paragraph:
                      "I understand that the Singapore Public Agencies and Participating Organisations require my Personal Information for the following operational and analytical purposes:",
                    indent: [
                      {
                        paragraph:
                          "to verify my and my Family’s identity and relationship for the Services or Scheme;"
                      },
                      {
                        paragraph:
                          "to determine my and my Family’s eligibility for the Services or Scheme;"
                      },
                      {
                        paragraph:
                          "to provide me and my Family with the Services or Scheme; and"
                      },
                      {
                        paragraph:
                          "for data analysis, evaluation and policy-making, for the Services or Scheme."
                      }
                    ]
                  },
                  {
                    paragraph:
                      "I consent and agree that the Singapore Public Agencies and Participating Organisations may collect, use and disclose my Personal Information for the purposes stated in Paragraph 1 and any other purpose permitted by law. I also consent and agree to the disclosure of my Personal Information to law enforcement officers. I understand that if there are any discrepancies in the Personal Information collected, such discrepancies may be reflected to the relevant Singapore Public Agencies, so that they may take the necessary steps to rectify any inaccurate records relating to me."
                  },
                  {
                    paragraph:
                      "My consent remains valid until I withdraw it in writing. I accept that it will take up to 10 working days from the date of receipt before the withdrawal of consent takes place."
                  },
                  {
                    paragraph:
                      "I have read and understood this consent form fully, including the attached Terms of Consent. I declare that the information that I have provided is accurate at the time I submit this form."
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
};

const consentAppStatusQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: Infinity // to stop component from making API calls and use mock data instead
    }
  }
});
consentAppStatusQueryClient.setQueryData(
  ["applicationConsent", consentSchema.schemeCode],
  {
    applicantName: "Tony Stark"
  }
);
const Template = (props) => {
  return (
    <QueryClientProvider client={consentAppStatusQueryClient}>
      <StorybookApplicationPage schema={consentSchema}>
        <GenericConsentComponent
          schemeCode={consentSchema.schemeCode}
          applicationSchema={props.schema}
          refId="FAKE-1234"
        />
      </StorybookApplicationPage>
    </QueryClientProvider>
  );
};

export const GenericConsent: Story = {
  render: (args) => <Template {...args} />,
  args: {
    applicationSchema: consentSchema
  }
};

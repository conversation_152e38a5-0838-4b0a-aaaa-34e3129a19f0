import { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import { ApplyDashboard as ApplyDashboardComponent } from "./ApplyDashboard";
import { ComponentProps } from "react";
import { ApplicationSchema } from "application/templates/MultistepApplication/GenericApplication.helper";
import { StorybookApplicationPage } from "application/components/form/storybookUtils";

const meta: Meta<typeof ApplyDashboardComponent> = {
  title: "Application Generator/Template/Apply Dashboard",
  component: ApplyDashboardComponent,
  argTypes: {
    onSelectedSchemaId: { table: { disable: true } }
  }
};
export default meta;

const noDraftQueryClient = new QueryClient();
noDraftQueryClient.setQueryData(["draft", "mda"], undefined);

const withDraftQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: Infinity // to stop component from making API calls and use mock data instead
    }
  }
});
withDraftQueryClient.setQueryData(["draft", "mda"], {
  content: { profile: { text: "abc" } },
  expiredDate: "10 Jul 2024, 11:59pm"
});

interface TemplateProps extends ComponentProps<typeof ApplyDashboardComponent> {
  withDraft?: boolean;
}

const Template = ({
  withDraft,
  schemeCode,
  application,
  applicationState,
  maintenanceMsg,
  ...otherProps
}: TemplateProps) => {
  return (
    <QueryClientProvider
      client={withDraft ? withDraftQueryClient : noDraftQueryClient}
    >
      <StorybookApplicationPage schema={MOCK_SCHEMA}>
        <ApplyDashboardComponent
          application={application}
          applicationState={applicationState}
          maintenanceMsg={maintenanceMsg}
          schemeCode={schemeCode}
          {...otherProps}
          // mock
          onSelectedSchemaId={() => undefined}
        />
      </StorybookApplicationPage>
    </QueryClientProvider>
  );
};

const MOCK_SCHEMA: ApplicationSchema = {
  schemeName: "Mother's day assistance",
  subtitle: "Supported by Ministry of Social and Family Development",
  schemeCode: "mda",
  schemeDetailsLink: "/schemes/CRG/covid-19-recovery-grant",
  dashboard: {
    applicationStatusNote: "",
    applicationGuidance:
      "If you had recently submitted an application, please do not press “Apply now” as it may delay the processing of your application."
  },
  contacts: [
    {
      faqLink: "https://www.google.com.sg/",
      email: "<EMAIL>",
      hotlineNumber: "1800 222 0000",
      locationLink: "https://goo.gl/maps/RDrj6Cu15j6Png7r7",
      helpExtra: [
        "You may also visit your nearest Social Service Office if you require urgent help"
      ]
    }
  ],
  nextSteps:
    "Your application has been submitted, please wait 2 - 3 working days for it to be processed.",
  schema: [
    {
      id: "main",
      section: [
        {
          title: "Profile",
          subtitle: "This is profile subtitle",
          id: "profile",
          member: [
            {
              id: "details",
              title: "Details",
              type: "CUSTOM_GROUP",
              subType: "BLANK",
              member: [
                {
                  id: "text",
                  type: "CUSTOM_FIELD",
                  subType: "DATE",
                  title: "text field",
                  optional: true
                },
                {
                  type: "DECORATOR",
                  subType: "HEADER",
                  title: "Group level header"
                },
                {
                  id: "text-number",
                  type: "CUSTOM_FIELD",
                  subType: "TEXT_NUMBER",
                  title: "number field"
                }
              ]
            },
            {
              id: "text",
              type: "CUSTOM_FIELD",
              subType: "TEXT",
              title: "section-level text field"
            },
            {
              id: "family",
              title: "Family",
              type: "CUSTOM_GROUP",
              subType: "BLANK",
              member: [
                {
                  id: "name",
                  type: "PRESET_FIELD",
                  subType: "nric"
                },
                {
                  id: "date",
                  type: "PRESET_FIELD",
                  subType: "dob",
                  optional: true
                },
                {
                  id: "myaddress",
                  type: "PRESET_FIELD",
                  subType: "address"
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  applicationJourney: {
    steps: [
      {
        title: "Apply",
        subtitle: "Applied on {{appliedDateTime}}",
        description:
          "Provide details of yourself and your household members.\n&nbsp;\nIf you need help with your application, please refer to this [step-by-step video guide](https://www.youtube.com/watch?v=n2jkwhEyTmU)."
      },
      {
        title: "Assigning an officer to you",
        description: "An officer is being assigned to your application."
      },
      {
        title: "Talk to your officer",
        description:
          "The SSO will call you within 3 working days. Your officer will then follow-up with you to open your case."
      },
      {
        title: "Processing",
        description:
          "Your application is being reviewed. Upon submission of all supporting documents, we will take 4-6 weeks to process your application."
      },
      {
        title: "Check outcome",
        description: "Check your application details."
      }
    ],
    ongoingStatusToStep: {
      "0": 1,
      "1": 2,
      "2": 3,
      "20": 3,
      "21": 3
    },
    action: {
      consentDocs: {
        title: "Consent",
        description:
          "Your application may require additional consent from others.",
        actionLabel: "Get consent",
        url: "/grants/smta/{{refId}}/documents"
      },
      outstandingItems: {
        description: "Upload documents requested by your officer.",
        deadline: "Upload by {{deadline}}",
        actionLabel: "Upload documents",
        url: "/grants/smta/{{refId}}/documents"
      }
    },
    statusToAction: {
      "0": ["consentDocs"],
      "1": ["consentDocs"],
      "20": ["outstandingItems"]
    }
  }
};

const DEFAULT_SCHEMA_PROPS = {
  schemeCode: MOCK_SCHEMA.schemeCode,
  dashboard: MOCK_SCHEMA.dashboard,
  contacts: MOCK_SCHEMA.contacts,
  applicationJourney: MOCK_SCHEMA.applicationJourney,
  applicationSchema: MOCK_SCHEMA.schema
} as const;

type Story = StoryObj<typeof Template>;

export const NewApplication: Story = {
  render: (args) => <Template {...args} />,
  args: {
    ...DEFAULT_SCHEMA_PROPS,
    application: {
      refId: "MDA-1234",
      statusCode: "0",
      status: "Submitted",
      appliedDateTime: "5 Feb 2025",
      updatedDateTime: "10 Feb 2025"
    },
    applicationState: "allow"
  }
};

export const NewApplicationWithDraft: Story = {
  render: (args) => <Template {...args} />,
  args: {
    ...DEFAULT_SCHEMA_PROPS,
    withDraft: true,
    application: {
      refId: "MDA-1234",
      statusCode: "0",
      status: "Submitted",
      appliedDateTime: "5 Feb 2025",
      updatedDateTime: "10 Feb 2025"
    },
    applicationState: "allow"
  }
};

export const UpcomingMaintenance: Story = {
  render: (args) => <Template {...args} />,
  args: {
    ...DEFAULT_SCHEMA_PROPS,
    application: {
      refId: "MDA-1234",
      statusCode: "0",
      status: "Submitted",
      appliedDateTime: "5 Feb 2025",
      updatedDateTime: "10 Feb 2025"
    },
    applicationState: "allow",
    maintenanceMsg:
      "This scheme provider will be undergoing a scheduled maintenance on **20 April 2024** from **6:00am** to **10:00am**."
  }
};

export const Disallow: Story = {
  render: (args) => <Template {...args} />,
  args: {
    ...DEFAULT_SCHEMA_PROPS,
    application: {
      refId: "MDA-1234",
      statusCode: "0",
      status: "Submitted",
      appliedDateTime: "5 Feb 2025",
      updatedDateTime: "10 Feb 2025"
    },
    applicationState: "disallow"
  }
};

export const Maintenance: Story = {
  render: (args) => <Template {...args} />,
  args: {
    ...DEFAULT_SCHEMA_PROPS,
    application: {
      refId: "MDA-1234",
      statusCode: "0",
      status: "Submitted",
      appliedDateTime: "5 Feb 2025",
      updatedDateTime: "10 Feb 2025"
    },
    applicationState: "maintenance"
  }
};

export const Ineligible: Story = {
  render: (args) => <Template {...args} />,
  args: {
    ...DEFAULT_SCHEMA_PROPS,
    ineligibleReason: {
      title: "Custom ineligible title",
      description:
        "The ineligible reason is based on the ineligible code returned by agency. The mapping for the code is defined on the scheme schema."
    },
    application: {
      refId: "MDA-1234",
      statusCode: "0",
      status: "Submitted",
      appliedDateTime: "5 Feb 2025",
      updatedDateTime: "10 Feb 2025"
    },
    applicationState: "ineligible"
  }
};

export const MultipleRole: Story = {
  render: (args) => <Template {...args} />,
  args: {
    ...DEFAULT_SCHEMA_PROPS,
    applicationState: "allow"
  }
};

export const ApplyInfoModal: Story = {
  render: (args) => <Template {...args} />,
  args: {
    ...DEFAULT_SCHEMA_PROPS,
    applicationState: "allow",
    applyInfo: {
      pwd: {
        template: "notEligiblePwd"
      },
      parent: {
        template: "childrenNotEligible",
        data: {
          children: [
            { name: "Choo Xi Ling", nric: "Sxxxx862C" },
            { name: "Yeung Seng Hee", nric: "Sxxxx103Z" }
          ]
        }
      }
    }
  }
};

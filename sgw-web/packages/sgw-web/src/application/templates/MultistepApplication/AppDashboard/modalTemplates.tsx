import styled from "styled-components";

import { TemplateData } from "application/api/sgw";
import { Text } from "commons/components/Text";
import { sp } from "commons/styles";
import { Link } from "commons/components/Link";

export const TEMPLATE_IDS = [
  "notEligiblePwd",
  "notEligiblePwdCitizen",
  "childrenNotEligible",
  "verifiedPwd",
  "notVerifiedPwd",
  "verifiedPwdChildren",
  "noVerifiedPwdChildren"
] as const;

// create union type from array values
export type TemplateId = typeof TEMPLATE_IDS[number];

interface ModalButton {
  text: string;
  variant: "primary" | "secondary";
  onClick: () => void;
}

export interface ModalTemplate {
  title: string;
  description: string;
  content: React.ReactNode;
  buttons: {
    primary?: ModalButton;
    secondary?: ModalButton;
  };
  onClose: () => void;
}

interface ModalHandlers {
  onClose: () => void;
  onProceed: () => void;
}

type ModalTemplateProps = Record<
  TemplateId,
  (handlers: ModalHandlers, data?: TemplateData) => ModalTemplate
>;

const modalTemplate: ModalTemplateProps = {
  notEligiblePwd: ({ onClose }) => ({
    title: `Disability status is verified\nor pending verification`,
    description: "Disability status is verified or pending verification",
    content: (
      <Container>
        <Text>
          No further action is needed. If your disability status is pending
          verification, you will receive an email notification on the outcome
          within 15 working days.
        </Text>
        <Text>
          For other disability support and services, please proceed with your
          application or visit{" "}
          <Link to="https://enablingguide.sg" linkType="external">
            enablingguide.sg
          </Link>{" "}
          for more details.
        </Text>
      </Container>
    ),
    buttons: {
      primary: {
        variant: "primary",
        text: "Okay",
        onClick: onClose
      }
    },
    onClose
  }),
  notEligiblePwdCitizen: ({ onClose }) => ({
    title: "Not Eligible",
    description: "Not Eligible",
    content: (
      <Container>
        <Text>
          Only Singapore Citizens or Permanent Residents are eligible.
        </Text>
      </Container>
    ),
    buttons: {
      primary: {
        variant: "primary",
        text: "Okay",
        onClick: onClose
      }
    },
    onClose
  }),
  childrenNotEligible: ({ onProceed, onClose }, data) => ({
    title: "The following child(ren) has been verified:",
    description: "The following child(ren) has been verified:",
    content: (
      <Container>
        <List>
          {data?.children?.map(({ name, nric }) => (
            <li key={nric}>
              <Text>{`${name}, ${nric}`}</Text>
            </li>
          ))}
        </List>
        <Text>
          You can only apply for a child who has not been verified yet.{" "}
          <strong>
            If you’re applying for a different child, you may proceed to apply.
          </strong>
        </Text>
      </Container>
    ),
    buttons: {
      primary: {
        variant: "primary",
        text: "Continue to form",
        onClick: onProceed
      },
      secondary: {
        variant: "secondary",
        text: "Cancel",
        onClick: onClose
      }
    },
    onClose
  }),
  verifiedPwd: ({ onProceed, onClose }) => ({
    title: "Verified as a Person with Disability",
    description: "Verified as a Person with Disability",
    content: (
      <Container>
        <Text>
          You do not have to upload the Disability Verification Form (DVF) for
          this application.
        </Text>
      </Container>
    ),
    buttons: {
      primary: {
        variant: "primary",
        text: "Continue to form",
        onClick: onProceed
      },
      secondary: {
        variant: "secondary",
        text: "Cancel",
        onClick: onClose
      }
    },
    onClose
  }),
  notVerifiedPwd: ({ onProceed, onClose }) => ({
    title: "Not verified as a Person with Disability",
    description: "Not verified as a Person with Disability",
    content: (
      <Container>
        <Text>
          You will need to upload the Disability Verification Form (DVF) to
          complete your application.
        </Text>
      </Container>
    ),
    buttons: {
      primary: {
        variant: "primary",
        text: "Continue to form",
        onClick: onProceed
      },
      secondary: {
        variant: "secondary",
        text: "Cancel",
        onClick: onClose
      }
    },
    onClose
  }),
  verifiedPwdChildren: ({ onProceed, onClose }, data) => ({
    title: "Verification for Person with Disability",
    description: "Verification for Person with Disability",
    content: (
      <Container>
        <Text>The following child(ren) has already been verified:</Text>
        <List>
          {data?.children?.map(({ name, nric }) => (
            <li key={nric}>
              <Text>{`${name}, ${nric}`}</Text>
            </li>
          ))}
        </List>
        <Text>
          <strong>
            You do not need to upload the Disability Verification Form (DVF) for
            those who are already verified.
          </strong>
        </Text>
        <Text>
          <strong>
            For those who are not verified yet, please upload the DVF in this
            application.
          </strong>
        </Text>
      </Container>
    ),
    buttons: {
      primary: {
        variant: "primary",
        text: "Continue to form",
        onClick: onProceed
      },
      secondary: {
        variant: "secondary",
        text: "Cancel",
        onClick: onClose
      }
    },
    onClose
  }),
  noVerifiedPwdChildren: ({ onProceed, onClose }) => ({
    title: "Verification for Person with Disability",
    description: "Verification for Person with Disability",
    content: (
      <Container>
        <Text>
          Your child(ren)&#39;s disability status have not been verified.
        </Text>
        <Text>
          <strong>Please upload the DVF in this application.</strong>
        </Text>
      </Container>
    ),
    buttons: {
      primary: {
        variant: "primary",
        text: "Continue to form",
        onClick: onProceed
      },
      secondary: {
        variant: "secondary",
        text: "Cancel",
        onClick: onClose
      }
    },
    onClose
  })
};

export const getModalConfig = (
  templateId: TemplateId,
  handlers: ModalHandlers,
  templateData?: TemplateData
): ModalTemplate => {
  const template = modalTemplate[templateId];

  return template(handlers, templateData);
};

const Container = styled.div`
  display: grid;
  gap: ${sp._24};
`;

const List = styled.ul`
  margin: 0;
  padding-left: ${sp._24};
`;

import { SgwAppStatus } from "application/api/sgw";
import { AssistanceInfoCard } from "application/components/AssistanceInfoCard";
import { CenteredGridContainer } from "application/components/CenteredGridContainer";
import { GenericTimeline } from "application/components/GenericTimeline";
import { StatusActivityCard } from "application/components/StatusActivityCard";
import { StatusCard } from "application/components/StatusCard";
import { ContactInfoList } from "application/composites/ContactInfoList";
import { ApplicationSchema } from "application/templates/MultistepApplication/GenericApplication.helper";
import { getAppPath } from "application/templates/MultistepApplication/helper";
import { Text } from "commons/components/Text";
import { cl } from "commons/styles";
import { GenericBannerButton } from "directory/components/GenericBannerButton/GenericBannerButton";
import { Detail, Group, TimelineHeader, Wrapper } from "./styles";
import MyApplications from "commons/assets/images/my-applications.png";
import { useStatusActivity } from "./Dashboard.helper";
import { StatusBlock } from "commons/components/StatusBlock";
import { Markdown } from "application/composites/Markdown";

const TERMINAL_STATUS_CODES = ["3", "4", "5", "6", "7", "8", "9"];

interface DashboardProps {
  schema: ApplicationSchema;
  status: SgwAppStatus;
  consentRequired: boolean;
  isServiceError: boolean;
  maintenanceMsg?: string;
}

interface MyApplicationsBannerProps {
  to: string;
}

export const Dashboard = ({
  schema,
  status,
  consentRequired,
  isServiceError,
  maintenanceMsg
}: DashboardProps) => {
  const { schemeCode, contacts, applicationJourney } = schema;
  const path = getAppPath(schema);
  const statusActivityCardProps = useStatusActivity(
    schema,
    status,
    consentRequired
  );
  const isTerminal = TERMINAL_STATUS_CODES.includes(status.statusCode);

  return (
    <CenteredGridContainer>
      <Wrapper>
        {maintenanceMsg && (
          <StatusBlock
            title="Upcoming maintenance"
            Description={
              <Text typeScale="b2">
                <Markdown>{maintenanceMsg}</Markdown>
              </Text>
            }
            status="notice"
          />
        )}
        {isServiceError && (
          <StatusBlock
            title="Under maintenance"
            Description="We are unable to retrieve application details from this scheme provider. Some details may not be complete or the most up-to-date. You can still proceed with your application and upload documents, if needed."
            status="warning"
          />
        )}

        <Group>
          <Text typeScale="h4">Your selected application</Text>
          {isTerminal ? (
            <StatusCard status={status} />
          ) : (
            <StatusActivityCard {...statusActivityCardProps} />
          )}
        </Group>
        {status.detail && status.detail.length > 0 && (
          <Detail>
            <Text typeScale="st1">Details</Text>
            {status.detail.map((detail, index) => {
              const { category, ...content } = detail;
              return (
                <AssistanceInfoCard
                  key={index}
                  icon={getAssistanceDetailIcon(category)}
                  {...content}
                />
              );
            })}
          </Detail>
        )}
        {applicationJourney && !isTerminal && (
          <div>
            <TimelineHeader>
              <Text typeScale="st1">Timeline</Text>
              {status.appliedDateTime && (
                <Text typeScale="cpItalic">
                  Last updated {status.appliedDateTime}
                </Text>
              )}
            </TimelineHeader>

            <GenericTimeline
              applicationJourney={applicationJourney}
              status={status}
            />
          </div>
        )}
        <MyApplicationsBanner
          to={`${path.myApplications}?tab=all&scheme=${schemeCode}`}
        />
        <ContactInfoList contacts={contacts} />
      </Wrapper>
    </CenteredGridContainer>
  );
};

const getAssistanceDetailIcon = (category: string) => {
  switch (category) {
    case "Financial":
      return "application-money-blue";
    case "Employment":
      return "application-suitcase-blue";
    case "Beneficiary":
      return "application-user-blue";
    case "Medical":
      return "application-medical-blue";
    case "Housing":
      return "application-house-blue";
    case "Education":
      return "application-closedBook-blue";
    default:
      return "application-user-blue";
  }
};

export const MyApplicationsBanner = ({ to }: MyApplicationsBannerProps) => {
  return (
    <GenericBannerButton
      LogoComponent={
        <img
          src={MyApplications}
          alt="My Applications Logo"
          height={60}
          width={60}
        />
      }
      TitleComponent={
        <Text style={{ color: cl.primary.blue.darker }} typeScale="ol1">
          My applications
        </Text>
      }
      DescComponent={
        <Text style={{ color: cl.grey.dark }} typeScale="st2">
          Track and follow up on your submitted applications.
        </Text>
      }
      buttonText="View applications"
      contentStyle={{
        backgroundColor: cl.primary.blue.lighter
      }}
      buttonStyle={{
        backgroundColor: cl.white,
        color: cl.grey.dark,
        borderColor: cl.grey.dark,
        width: 166
      }}
      to={to}
      arialLabel="View applications"
    />
  );
};

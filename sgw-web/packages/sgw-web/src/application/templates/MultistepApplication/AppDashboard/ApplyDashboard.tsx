import { useQueryClient } from "@tanstack/react-query";
import { useHistory } from "react-router";
import { useState } from "react";

import { GetDraftResponse } from "application/api/draft";
import { ApplicationState, SgwAppStatus, ApplyInfo } from "application/api/sgw";
import { IllustrationCard } from "application/components/IllustrationCard";
import { CenteredGridContainer } from "application/components/CenteredGridContainer";
import { GenericTimeline } from "application/components/GenericTimeline";
import { ButtonArrowRight } from "application/composites/ButtonArrowRight";
import { ContactInfoList } from "application/composites/ContactInfoList";
import { InfoBlock } from "application/composites/decorators";
import {
  ApplicationSchema,
  IneligibleReasonType
} from "application/templates/MultistepApplication/GenericApplication.helper";
import { getAppPath } from "application/templates/MultistepApplication/helper";
import { Spinner } from "commons/components/Spinner";
import { StatusBlock } from "commons/components/StatusBlock";
import { Text } from "commons/components/Text";
import { sgwWogaa } from "commons/services/wogaa";
import { cl } from "commons/styles";
import { MyApplicationsBanner } from "./Dashboard";
import {
  ButtonWrapper,
  TimelineHeader,
  Wrapper,
  Group,
  TimeoutWrapper
} from "./styles";
import { SectionButton } from "application/components/SectionButton";
import { Markdown } from "application/composites/Markdown";
import { Modal } from "commons/components/Modal";
import { Button } from "commons/components/buttons/Button";

import { TEMPLATE_IDS, ModalTemplate, getModalConfig } from "./modalTemplates";
import { ButtonsContainer } from "../sharedStyles";

interface ApplyDashboardProps {
  schemeCode: string;
  schema: ApplicationSchema;
  application?: SgwAppStatus;
  applicationState: ApplicationState;
  ineligibleReason?: IneligibleReasonType;
  onSelectedSchemaId: (id: string) => void;
  maintenanceMsg?: string;
  applyInfo?: ApplyInfo;
  agencyUnavailable?: boolean;
}

export const ApplyDashboard = ({
  schemeCode,
  schema,
  applicationState,
  application,
  ineligibleReason,
  onSelectedSchemaId,
  maintenanceMsg,
  applyInfo,
  agencyUnavailable
}: ApplyDashboardProps) => {
  const { contacts, applicationJourney } = schema;

  const path = getAppPath(schema);

  return (
    <CenteredGridContainer>
      <Wrapper>
        {maintenanceMsg && (
          <StatusBlock
            title="Upcoming maintenance"
            Description={
              <Text typeScale="b2">
                <Markdown>{maintenanceMsg}</Markdown>
              </Text>
            }
            status="notice"
          />
        )}
        {applicationState === "unavailable" && (
          <IllustrationCard
            image="underReview"
            title="Unable to apply"
            content="This may be due to ineligibility for one or more schemes or a temporary system issue with a scheme provider. You may try applying for the schemes individually."
          />
        )}
        {applicationState === "maintenance" && (
          <IllustrationCard
            image="maintenance"
            title="Under maintenance"
            content="We are currently unable to retrieve application details from this scheme provider. Please try again later."
          />
        )}
        {applicationState === "timeout" && (
          <TimeoutWrapper>
            <IllustrationCard
              image="maintenance"
              title="Page took too long to load"
              content="We are currently unable to retrieve application details from this scheme provider. You may want to check your internet connection and try again later."
            />
            <ButtonsContainer>
              <Button
                buttonText="Refresh page"
                onClick={() => window.location.reload()}
              />
            </ButtonsContainer>
          </TimeoutWrapper>
        )}
        {applicationState === "ineligible" && (
          <IllustrationCard
            image="notEligible"
            title={ineligibleReason?.title || "Not eligible"}
            content={
              ineligibleReason?.description ||
              "You are not eligible for this scheme."
            }
          />
        )}
        {applicationState === "disallow" && (
          <IllustrationCard
            image="underReview"
            title="Your application is being reviewed"
            content="You are unable to start a new application as you have an ongoing application."
          />
        )}

        {applicationState === "allow" && (
          <>
            {agencyUnavailable && (
              <StatusBlock
                title="Under maintenance"
                Description={
                  "We are unable to retrieve application details from this scheme provider. Some details may not be complete or the most up-to-date. You can still proceed with your application and upload documents, if needed."
                }
                status="warning"
              />
            )}

            <ApplySection
              schemeCode={schemeCode}
              schema={schema}
              onSelectedSchemaId={onSelectedSchemaId}
              applyInfo={applyInfo}
            />

            {applicationJourney && (
              <div>
                <TimelineHeader>
                  <Text typeScale="ol1">TIMELINE</Text>
                </TimelineHeader>
                <GenericTimeline applicationJourney={applicationJourney} />
              </div>
            )}
          </>
        )}

        <MyApplicationsBanner
          to={
            application
              ? `${path.myApplications}?tab=all&scheme=${schemeCode}`
              : path.myApplications
          }
        />

        <ContactInfoList contacts={contacts} />
      </Wrapper>
    </CenteredGridContainer>
  );
};

interface ApplySectionProps {
  schemeCode: string;
  schema: ApplicationSchema;
  onSelectedSchemaId: (id: string) => void;
  applyInfo?: ApplyInfo;
}

const ApplySection = ({
  schemeCode,
  schema,
  onSelectedSchemaId,
  applyInfo
}: ApplySectionProps) => {
  const { push } = useHistory();

  const { dashboard, schema: applicationSchema, bundleSchemeCode } = schema;

  const path = getAppPath(schema);

  const draftQuery = useQueryClient().getQueryState(["draft", schemeCode]);
  const draftData = useQueryClient().getQueryData<GetDraftResponse>([
    "draft",
    schemeCode
  ]);

  const [isApplyInfoModalOpen, setIsApplyInfoModalOpen] = useState(false);
  const [
    applyInfoModalConfig,
    setApplyInfoModalConfig
  ] = useState<ModalTemplate | null>(null);

  // applyInfo acts as a configuration for schemes that require showing of modal.
  // Either to inform the user they cannot proceed or to highlight important notes.
  // If the selected schemaId is not in applyInfo, the user can proceed directly to the form.
  const handleSchemaClick = (schemaId: string) => {
    const modalConfig = applyInfo?.[schemaId];

    if (!modalConfig || !TEMPLATE_IDS.includes(modalConfig.template)) {
      sgwWogaa.startAppTS();
      onSelectedSchemaId(schemaId);
      return;
    }

    const modalHandlers = {
      onClose: () => {
        setIsApplyInfoModalOpen(false);
        setApplyInfoModalConfig(null);
      },
      onProceed: () => {
        setIsApplyInfoModalOpen(false);
        sgwWogaa.startAppTS();
        onSelectedSchemaId(schemaId);
      }
    };
    const applyInfoModalConfig = getModalConfig(
      modalConfig.template,
      modalHandlers,
      modalConfig.data
    );

    setApplyInfoModalConfig(applyInfoModalConfig);
    setIsApplyInfoModalOpen(true);
  };

  if (draftQuery?.fetchStatus === "fetching") {
    return <Spinner />;
  }

  return (
    <>
      <Group>
        <Text typeScale="h4" color={cl.grey.dark}>
          New application
        </Text>

        {dashboard?.applicationGuidance && (
          <InfoBlock description={[dashboard.applicationGuidance]} />
        )}

        {draftData ? (
          <ContinueDraft
            expiredDate={draftData.expiredDate}
            onClick={() => {
              sgwWogaa.startAppTS();
              push(path.overview);
            }}
          />
        ) : (
          <NewApplication
            applicationSchema={applicationSchema}
            hasSafBundle={!!bundleSchemeCode}
            onClick={handleSchemaClick}
          />
        )}

        {bundleSchemeCode && (
          <SectionButton
            icon="application-fileAlt-blue"
            onClick={() => {
              push(getAppPath(schema).safSelection);
            }}
            title="Apply for multiple schemes: For lower-income families with young children"
            description="View scheme details and apply in just 1 form"
          />
        )}
      </Group>

      <Modal
        isOpen={isApplyInfoModalOpen}
        role="dialog"
        title={applyInfoModalConfig?.title}
        description={
          applyInfoModalConfig?.description ||
          "Application-related information and instructions"
        }
        onClose={applyInfoModalConfig?.onClose}
        primaryButton={applyInfoModalConfig?.buttons.primary}
        secondaryButton={applyInfoModalConfig?.buttons.secondary}
      >
        {applyInfoModalConfig?.content}
      </Modal>
    </>
  );
};

interface ContinueDraftProps {
  onClick: () => void;
  expiredDate: string;
}
const ContinueDraft = ({ onClick, expiredDate }: ContinueDraftProps) => (
  <>
    <ButtonWrapper>
      <ButtonArrowRight buttonText="Continue draft" onClick={onClick} />
    </ButtonWrapper>
    <StatusBlock
      Description={
        <Text typeScale="b2">Your draft expires on {expiredDate}.</Text>
      }
      status="warning"
    />
  </>
);

interface NewApplicationProps {
  applicationSchema: ApplicationSchema["schema"];
  /**
   * If this application is part of Single Application Form (SAF) bundle, render apply button in SectionButton component so it looks consistent with the SectionButton component used to navigate to SAF
   */
  hasSafBundle: boolean;
  onClick: (schemaId: string) => void;
}
const NewApplication = ({
  applicationSchema,
  hasSafBundle,
  onClick
}: NewApplicationProps) => {
  if (hasSafBundle || applicationSchema.length > 1) {
    return (
      <>
        {applicationSchema.map((schema) => (
          <SectionButton
            key={schema.id}
            icon="application-fileAlt-blue"
            onClick={() => onClick(schema.id)}
            title={schema.actionLabel || "Apply for 1 scheme"}
            description={schema.descriptionLabel}
          />
        ))}
      </>
    );
  }

  return (
    <ButtonWrapper>
      <ButtonArrowRight
        key={applicationSchema[0].id}
        buttonText={applicationSchema[0].actionLabel || "Apply Now"}
        onClick={() => onClick(applicationSchema[0].id)}
      />
    </ButtonWrapper>
  );
};

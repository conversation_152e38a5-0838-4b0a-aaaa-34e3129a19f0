import { breakpoint, sp } from "commons/styles";
import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sp._40};
  ${breakpoint.m} {
    gap: ${sp._48};
  }
`;

export const Group = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sp._16};
  ${breakpoint.m} {
    gap: ${sp._24};
  }
`;

export const Detail = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sp._16};
`;

export const ButtonWrapper = styled.div`
  display: flex;
  flex-direction: column;

  width: 100%;
  ${breakpoint.s} {
    width: 260px;
  }
`;

export const TimelineHeader = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: ${sp._16};
`;

export const TimeoutWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sp._16};
  ${breakpoint.m} {
    gap: ${sp._48};
  }
`;

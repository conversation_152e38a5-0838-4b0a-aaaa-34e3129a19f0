import { <PERSON>a, StoryObj } from "@storybook/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { MemoryRouter } from "react-router-dom";

import { BackButton } from "commons/components/BackButton";
import { <PERSON><PERSON><PERSON> } from "commons/components/Masthead";
import { Text } from "commons/components/Text";
import { Page } from "commons/composites/Page";

import { Dashboard as DashboardComponent } from "./Dashboard";
import { ComponentProps } from "react";

const meta: Meta<typeof DashboardComponent> = {
  title: "Application Generator/Template/Dashboard",
  component: DashboardComponent
};

export default meta;

const noAppStatusQueryClient = new QueryClient();
noAppStatusQueryClient.setQueryData(["applicationStatus", "mda"], []);

type Story = StoryObj<typeof DashboardComponent>;
type DashboardComponentProps = ComponentProps<typeof DashboardComponent>;

const Template = ({
  schema,
  status,
  ...otherProps
}: DashboardComponentProps) => {
  return (
    <QueryClientProvider client={noAppStatusQueryClient}>
      <Page printHeader={false}>
        <Masthead
          mastheadTitle={
            <Text typeScale="h3" style={{ marginBottom: 8 }}>
              {schema.schemeName}
            </Text>
          }
          mastheadSubtitle={<Text typeScale="t2">{schema.subtitle}</Text>}
          BackButtonComponent={
            <BackButton
              onClick={() =>
                (window.location.href = schema.schemeDetailsLink || "/")
              }
            >
              {schema.schemeDetailsLink ? "Scheme details" : "Home"}
            </BackButton>
          }
        />
        <MemoryRouter
          initialEntries={[`/grants/${schema.schemeCode}/${status.refId}`]}
        >
          <DashboardComponent schema={schema} status={status} {...otherProps} />
        </MemoryRouter>
      </Page>
    </QueryClientProvider>
  );
};

const MOCK_SCHEMA: DashboardComponentProps["schema"] = {
  schemeName: "Mother's day assistance",
  subtitle: "Supported by Ministry of Social and Family Development",
  schemeCode: "mda",
  schemeDetailsLink: "/schemes/CRG/covid-19-recovery-grant",
  dashboard: {
    applicationStatusNote: "",
    applicationGuidance:
      "If you had recently submitted an application, please do not press “Apply now” as it may delay the processing of your application."
  },
  contacts: [
    {
      faqLink: "https://www.google.com.sg/",
      email: "<EMAIL>",
      hotlineNumber: "1800 222 0000",
      locationLink: "https://goo.gl/maps/RDrj6Cu15j6Png7r7",
      helpExtra: [
        "You may also visit your nearest Social Service Office if you require urgent help"
      ]
    }
  ],
  nextSteps:
    "Your application has been submitted, please wait 2 - 3 working days for it to be processed.",
  schema: [
    {
      id: "main",
      section: [
        {
          title: "Profile",
          subtitle: "This is profile subtitle",
          id: "profile",
          member: [
            {
              id: "details",
              title: "Details",
              type: "CUSTOM_GROUP",
              subType: "BLANK",
              member: [
                {
                  id: "text",
                  type: "CUSTOM_FIELD",
                  subType: "DATE",
                  title: "text field",
                  optional: true
                },
                {
                  type: "DECORATOR",
                  subType: "HEADER",
                  title: "Group level header"
                },
                {
                  id: "text-number",
                  type: "CUSTOM_FIELD",
                  subType: "TEXT_NUMBER",
                  title: "number field"
                }
              ]
            },
            {
              id: "text",
              type: "CUSTOM_FIELD",
              subType: "TEXT",
              title: "section-level text field"
            },
            {
              id: "family",
              title: "Family",
              type: "CUSTOM_GROUP",
              subType: "BLANK",
              member: [
                {
                  id: "name",
                  type: "PRESET_FIELD",
                  subType: "nric"
                },
                {
                  id: "date",
                  type: "PRESET_FIELD",
                  subType: "dob",
                  optional: true
                },
                {
                  id: "myaddress",
                  type: "PRESET_FIELD",
                  subType: "address"
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  applicationJourney: {
    steps: [
      {
        title: "Apply",
        subtitle: "Applied on {{appliedDateTime}}",
        description:
          "Provide details of yourself and your household members.\n&nbsp;\nIf you need help with your application, please refer to this [step-by-step video guide](https://www.youtube.com/watch?v=n2jkwhEyTmU)."
      },
      {
        title: "Assigning an officer to you",
        description: "An officer is being assigned to your application."
      },
      {
        title: "Talk to your officer",
        description:
          "The SSO will call you within 3 working days. Your officer will then follow-up with you to open your case."
      },
      {
        title: "Processing",
        description:
          "Your application is being reviewed. Upon submission of all supporting documents, we will take 4-6 weeks to process your application."
      },
      {
        title: "Check outcome",
        description: "Check your application details."
      }
    ],
    ongoingStatusToStep: {
      "0": 1,
      "1": 2,
      "2": 3,
      "20": 3,
      "21": 3
    },
    action: {
      consentDocs: {
        title: "Consent",
        description:
          "Your application may require additional consent from others.",
        actionLabel: "Get consent",
        url: "/grants/smta/{{refId}}/documents"
      },
      outstandingItems: {
        description: "Upload documents requested by your officer.",
        deadline: "Upload by {{deadline}}",
        actionLabel: "Upload documents",
        url: "/grants/smta/{{refId}}/documents"
      }
    },
    statusToAction: {
      "0": ["consentDocs"],
      "1": ["consentDocs"],
      "20": ["outstandingItems"]
    }
  }
};

export const OngoingApplication: Story = {
  render: (args) => <Template {...args} />,
  args: {
    schema: MOCK_SCHEMA,
    status: {
      refId: "MDA-1234",
      statusCode: "0",
      status: "Submitted",
      appliedDateTime: "5 Feb 2025",
      updatedDateTime: "10 Feb 2025"
    }
  }
};

export const ConsentDocRequired: Story = {
  render: (args) => <Template {...args} />,
  args: {
    schema: MOCK_SCHEMA,
    status: {
      refId: "MDA-1234",
      statusCode: "0",
      status: "Submitted",
      appliedDateTime: "5 Feb 2025",
      updatedDateTime: "10 Feb 2025"
    },
    consentRequired: true
  }
};

export const PendingDoc: Story = {
  render: (args) => <Template {...args} />,
  args: {
    schema: MOCK_SCHEMA,
    status: {
      refId: "MDA-1234",
      statusCode: "20",
      status: "Pending Documents",
      appliedDateTime: "5 Feb 2025",
      updatedDateTime: "10 Feb 2025"
    }
  }
};

export const UpcomingMaintenance: Story = {
  render: (args) => <Template {...args} />,
  args: {
    schema: MOCK_SCHEMA,
    status: {
      refId: "MDA-1234",
      statusCode: "0",
      status: "Submitted",
      appliedDateTime: "5 Feb 2025",
      updatedDateTime: "10 Feb 2025"
    },
    maintenanceMsg:
      "This scheme provider will be undergoing a scheduled maintenance on **20 April 2024** from **6:00am** to **10:00am**."
  }
};

export const AgencyStatusApiError: Story = {
  render: (args) => <Template {...args} />,
  args: {
    schema: MOCK_SCHEMA,
    status: {
      refId: "MDA-1234",
      statusCode: "0",
      status: "Submitted",
      appliedDateTime: "5 Feb 2025",
      updatedDateTime: "10 Feb 2025"
    },
    isServiceError: true
  }
};

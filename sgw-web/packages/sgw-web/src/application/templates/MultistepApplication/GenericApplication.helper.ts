import z from "zod";
import DayJS from "dayjs";
import { get } from "lodash";

import {
  customDropdownValidator,
  CustomFieldType,
  customFreeTextValidator,
  hiddenValidator,
  customRadioValidator,
  customSingleCheckValidator,
  customTextDateValidator,
  customTextMonthValidator,
  customTextNumberValidator,
  customTextValidator,
  moneyValidator,
  multilineTextValidator,
  customFileUploadValidator,
  customSignatureValidator,
  customTermsAndConditionsValidator,
  customDateRangeValidator
} from "application/composites/customFields";
import { checkBoxValidator } from "application/composites/customFields/Checkbox";
import {
  fileUploadValidator,
  tncValidator
} from "application/composites/customGroups";
import {
  generateNumberOptions,
  SELECT_ID_SUFFIX
} from "application/composites/dynamicFields";
import {
  addressLine1Validator,
  addressLine2Validator,
  addressValidator,
  msfBankRefundValidator,
  msfLetterOfUndertakingValidator,
  msfOmnibusConsentValidator,
  blockValidator,
  buildingValidator,
  childValidator,
  countryOfBirthValidator,
  countryValidator,
  dateOfBirthValidator,
  emailValidator,
  employmentStatusValidator,
  homeNumberValidator,
  levelValidator,
  mailingAddressValidator,
  maritalStatusValidator,
  mobileNumberValidator,
  nameValidator,
  nationalityValidator,
  nricValidator,
  PresetFieldAddressType,
  PresetFieldBaseType,
  PresetFieldSelectType,
  PresetFieldType,
  raceValidator,
  relationshipTypeValidator,
  relationshipTypeV5Validator,
  residentialStatusValidator,
  schoolValidator,
  sexValidator,
  streetValidator,
  titleValidator,
  unitValidator,
  isPrefillSourceMyInfoNric,
  familyValidator,
  msfPaynowRefundValidator,
  educationValidator,
  occupationValidator,
  consenterValidator,
  householdMemberConsentValidator
} from "application/composites/presetFields";

const decorator = z.object({
  type: z.literal("DECORATOR"),
  subType: z.enum(["HEADER", "INFO_BLOCK"])
});

const globalAction = z.object({
  type: z.string(),
  dependsOn: z.string().array().optional(),
  relativeIds: z.string().array().optional(),
  defaultValue: z.string().optional()
});

const customGroup = z.object({
  id: z.string(),
  type: z.literal("CUSTOM_GROUP"),
  subType: z.enum(["BLANK", "FILE_UPLOAD", "TNC"]),
  title: z.string(),
  globalAction: globalAction.optional()
});

const customFieldBase = z.object({
  id: z.string(),
  type: z.literal("CUSTOM_FIELD"),
  title: z.string(),
  description: z.string().array().optional(),
  optional: z.boolean().optional(),
  prefillSource: z.string().optional(),
  editable: z.boolean().optional(),
  globalAction: globalAction.optional()
});

const customFieldText = customFieldBase.extend({
  subType: z.enum([
    CustomFieldType.enum.TEXT,
    CustomFieldType.enum.FREE_TEXT,
    CustomFieldType.enum.TEXT_NUMBER,
    CustomFieldType.enum.MULTILINE_TEXT
  ]),
  placeholder: z.string().optional(),
  maxLength: z.number().int().positive().optional()
});

const customFieldHidden = customFieldText.omit({ title: true }).extend({
  subType: z.enum([CustomFieldType.enum.HIDDEN]),
  prefillSource: z.string().optional()
});

const customFieldMoney = customFieldBase.extend({
  subType: z.enum([CustomFieldType.enum.MONEY]),
  placeholder: z.string().optional(),
  allowNegative: z.boolean().optional()
});

const customFieldSelect = customFieldBase.extend({
  subType: z.enum([CustomFieldType.enum.DROPDOWN, CustomFieldType.enum.RADIO]),
  options: z.union([
    z.string().array(),
    z.record(z.string(), z.string()),
    z.array(z.record(z.string(), z.string()))
  ])
});

const customFieldCheckbox = customFieldSelect.extend({
  subType: z.enum([CustomFieldType.enum.CHECKBOX]),
  exclusiveOptions: z.string().array().optional()
});

const customFieldDynamicDropdown = customFieldBase.extend({
  subType: z.enum([CustomFieldType.enum.DYNAMIC_DROPDOWN]),
  url: z.string(),
  options: z.record(z.string(), z.string()).optional()
});

const customFieldDateMonth = customFieldBase.extend({
  subType: z.enum([CustomFieldType.enum.DATE_MONTH]),
  minMonthRange: z.number().optional(),
  maxMonthRange: z.number().optional()
});

// prettier-ignore
const pointList = z.object({
  paragraph: z.string(),
  indent: z.object({
    paragraph: z.string(),
    indent: z.string().array().optional()
  })
    .array()
    .optional()
})
  .array()
  .nonempty()

const customFieldSignature = customFieldBase.extend({
  subType: z.enum([CustomFieldType.enum.SIGNATURE]),
  content: z
    .object({
      header: z.string(),
      indent: pointList
    })
    .array()
    .nonempty(),
  acknowledgement: z.string().array().optional(),
  additionalDetails: z.string().array().optional()
});

const customFieldOthers = customFieldBase.extend({
  subType: z.enum([
    CustomFieldType.enum.DATE,
    CustomFieldType.enum.DATE_RANGE,
    CustomFieldType.enum.SINGLE_CHECK
  ])
});

const customFieldFileUpload = customFieldBase.extend({
  subType: z.enum([CustomFieldType.enum.FILE_UPLOAD]),
  maxFile: z.number().optional(),
  maxFileSizeMb: z.number().optional(),
  instructions: z.string().array().optional(),
  documents: pointList,
  additionalDetails: z.string().array().optional()
});

const customFieldTermsAndConditions = customFieldBase.extend({
  subType: z.enum([CustomFieldType.enum.TNC]),
  acknowledge: z.string(),
  content: z
    .object({
      header: z.string(),
      indent: pointList
    })
    .array()
    .nonempty()
});

const customField = z.union([
  customFieldText,
  customFieldHidden,
  customFieldMoney,
  customFieldSelect,
  customFieldCheckbox,
  customFieldDynamicDropdown,
  customFieldDateMonth,
  customFieldSignature,
  customFieldOthers,
  customFieldFileUpload,
  customFieldTermsAndConditions
]);

const presetField = z.object({
  id: z.string(),
  type: z.literal("PRESET_FIELD"),
  subType: PresetFieldBaseType,
  optional: z.boolean().optional(),
  title: z.string().optional(),
  description: z.string().array().optional(),
  prefillSource: z.string().optional(),
  editable: z.boolean().optional(),
  globalAction: globalAction.optional()
});

const consent = z
  .union([
    z.boolean(),
    z.object({
      field: z.string(),
      value: z.string(),
      operator: z.string()
    })
  ])
  .optional();

const presetFieldNric = presetField.extend({
  subType: z.literal("nric"),
  consent,
  skipDuplicateCheck: z.boolean().optional(),
  consenterInfo: z
    .object({
      name: z.string()
    })
    .optional(),
  isUnmasked: z.boolean().optional()
});

const presetFieldEmail = presetField.extend({
  subType: z.literal("email"),
  template: z.enum(["application", "consent", "outstandingItems"]).optional()
});

const presetFieldFamily = presetField.extend({
  subType: z.literal("family"),
  consent
});

const presetFieldAddress = presetField.extend({
  subType: PresetFieldAddressType,
  allowOverseas: z.boolean().optional()
});

const presetFieldMsfBankRefund = presetField.extend({
  subType: z.literal("msfBankRefund"),
  // specify where to get recipient details i.e. name and nric. Specify relative IDs (relative to the bank details preset field) of name and nric in the array [<nameId>, <nricId>].
  priorityRelativeId: z.string().array().length(2).optional(),
  // will fall back to this default IDs to get recipient details if priority IDs specified above has no value. This accepts full IDs.
  defaultFullId: z.string().array().length(1)
});

const presetFieldMsfPaynowRefund = presetField.extend({
  subType: z.literal("msfPaynowRefund"),
  defaultFullId: z.string().array().length(1)
});

const signatureContentSchema = customFieldSignature.pick({
  title: true,
  content: true,
  acknowledgement: true,
  additionalDetails: true
});

const presetFieldMsfLetterOfUndertaking = presetField.extend({
  subType: z.literal("msfLetterOfUndertaking"),
  defaultFullId: z.string().array().length(1),
  signatureContent: signatureContentSchema
});

const presetFieldMsfOmnibusConsent = presetField.extend({
  subType: z.literal("msfOmnibusConsent"),
  priorityRelativeId: z.string().array().length(2)
});

const presetFieldSelect = presetField.extend({
  subType: PresetFieldSelectType,
  allowedOptions: z.string().array().nonempty().optional(),
  excludedOptions: z.string().array().nonempty().optional()
});

const header = decorator.extend({
  subType: z.literal("HEADER"),
  title: z.string(),
  description: z.string().array().optional()
});

const infoBlock = decorator.extend({
  subType: z.literal("INFO_BLOCK"),
  title: z.string().array().nonempty()
});

const decoratorSubType = z.discriminatedUnion("subType", [header, infoBlock]);

const groupMemberSubType = z.union([
  header,
  infoBlock,
  customField,
  presetField,
  presetFieldNric,
  presetFieldEmail,
  presetFieldFamily,
  presetFieldAddress,
  presetFieldSelect,
  presetFieldMsfBankRefund,
  presetFieldMsfPaynowRefund,
  presetFieldMsfLetterOfUndertaking,
  presetFieldMsfOmnibusConsent,
  z.lazy(() => globalGroupConditional),
  z.lazy(() => groupConditional)
]);

const blankGroup = customGroup.extend({
  subType: z.literal("BLANK"),
  member: groupMemberSubType.array()
});
// prettier-ignore
const tncGroup = customGroup.extend({
  subType: z.literal("TNC"),
  acknowledge: z.string(),
  content: z.object({
      header: z.string(),
      indent: pointList
    })
      .array()
      .nonempty()
});

const fileUploadGroup = customGroup.extend({
  subType: z.literal("FILE_UPLOAD"),
  optional: z.boolean().optional(),
  maxFile: z.number().optional(), // TODO: make maxFile mandatory?
  instructions: z.string().array().optional(),
  documents: pointList,
  additionalDetails: z.string().array().optional()
});

const multiValue = z.object({
  type: z.literal("MULTI_VALUE"),
  id: z.string(),
  title: z.string(),
  description: z.string().array().optional(),
  maxGroup: z.number(),
  prefillSource: z.string().optional(),
  editable: z.boolean().optional(), // for dropdown
  header: z.string(),
  group: blankGroup
});

const customGroupSubType = z.discriminatedUnion("subType", [
  blankGroup,
  fileUploadGroup,
  tncGroup
]);

export const COMPARISON_OPERATIONS = {
  equal: (a, b) => a === b,
  notEqual: (a, b) => a !== b,
  greaterThan: (a, b) => a > b,
  greaterThanInclusive: (a, b) => a >= b,
  lessThan: (a, b) => a < b,
  lessThanInclusive: (a, b) => a <= b
} as const;

export type Operator = keyof typeof COMPARISON_OPERATIONS;
export const OPERATORS = Object.keys(COMPARISON_OPERATIONS) as [
  Operator,
  ...Operator[]
];

export const RANGE_OPERATIONS = {
  includes: (a, b: any[]) => b.includes(a),
  between: (a, b: any[]) => a > b[0] && a < b[1],
  betweenInclusive: (a, b: any[]) => a >= b[0] && a <= b[1]
} as const;

export type RangeOperator = keyof typeof RANGE_OPERATIONS;
export const RANGE_OPERATORS = Object.keys(RANGE_OPERATIONS) as [
  RangeOperator,
  ...RangeOperator[]
];

const criteriaSchema = z.union([
  z.object({
    type: z.enum(["age", "value"]),
    sourcePath: z.string(), // could indicate nested path within source object e.g. dob.value
    operator: z.enum(OPERATORS),
    value: z.union([z.number(), z.string()])
  }),
  z.object({
    type: z.enum(["range"]),
    sourcePath: z.string(), // could indicate nested path within source object e.g. dob.value
    operator: z.enum(RANGE_OPERATORS),
    value: z.union([z.number().array().min(2), z.string().array().min(2)])
  })
]);

export type CriteriaSchema = z.infer<typeof criteriaSchema>;

/**
 * Utility function to evaluate whether input data satisfy the criteria specified.
 * @param criteria
 * @param inputData
 * @param fallback function will return this value in scenario where result could not be determined reliably i.e. when input data is empty
 * @returns boolean value whether input data satisfy the criteria
 */
export const evaluateCriteria = (
  criteria: CriteriaSchema,
  inputData: object,
  fallback = false
): boolean => {
  const { type, sourcePath, operator, value: targetValue } = criteria;

  let result = false;

  const sourceValue = get(inputData, sourcePath);
  if (sourceValue === null || sourceValue === undefined) {
    // could not evaluate undefined/null data, default to fallback
    return fallback;
  }

  switch (type) {
    case "age": {
      const age = DayJS().diff(DayJS(sourceValue), "year");
      result = COMPARISON_OPERATIONS[operator](age, targetValue);
      break;
    }

    case "value": {
      const value =
        typeof targetValue === "number" ? parseInt(sourceValue) : sourceValue;
      result = COMPARISON_OPERATIONS[operator](value, targetValue);
      break;
    }

    case "range": {
      const value =
        typeof targetValue[0] === "number"
          ? parseInt(sourceValue)
          : sourceValue;
      result = RANGE_OPERATIONS[operator](value, targetValue);
      break;
    }
  }

  return result;
};

const sectionMemberSubType = z.union([
  header,
  infoBlock,
  blankGroup,
  fileUploadGroup,
  tncGroup,
  customField,
  presetField,
  presetFieldNric,
  presetFieldEmail,
  presetFieldFamily,
  presetFieldAddress,
  presetFieldSelect,
  presetFieldMsfBankRefund,
  presetFieldMsfPaynowRefund,
  presetFieldMsfLetterOfUndertaking,
  presetFieldMsfOmnibusConsent,
  multiValue,
  z.lazy(() => sectionConditional),
  z.lazy(() => globalSectionConditional)
]);

const sectionCriteriaResult = z
  .object({
    choice: criteriaSchema.array().nonempty(),
    member: sectionMemberSubType.array()
  })
  .array()
  .min(1);

const globalSectionConditional: z.ZodType<GlobalSectionConditionalSchema> = z.object(
  {
    id: z.string(),
    type: z.literal("GLOBAL_SECTION_CONDITIONAL"),
    result: sectionCriteriaResult,
    selector: z.union([customField, presetField])
  }
);

const groupCriteriaResult = z
  .object({
    choice: criteriaSchema.array().nonempty(),
    member: groupMemberSubType.array()
  })
  .array()
  .min(1);

const globalGroupConditional: z.ZodType<GlobalGroupConditionalSchema> = z.object(
  {
    id: z.string(),
    type: z.literal("GLOBAL_GROUP_CONDITIONAL"),
    result: groupCriteriaResult,
    selector: z.union([customField, presetField])
  }
);

const conditional = z.object({
  id: z.string(),
  selector: z.union([
    customFieldSelect,
    customFieldCheckbox,
    presetFieldSelect,
    customFieldDynamicDropdown
  ])
});

const sectionResult = z
  .object({
    choice: z.string().array().nonempty(),
    member: sectionMemberSubType.array()
  })
  .array()
  .min(1);

const groupResult = z
  .object({
    choice: z.string().array().nonempty(),
    member: groupMemberSubType.array()
  })
  .array()
  .min(1);

type Conditional = z.infer<typeof conditional>;

type SectionResult = z.infer<typeof sectionResult>;

type GroupResult = z.infer<typeof groupResult>;

type SectionCriteriaResult = z.infer<typeof sectionCriteriaResult>;
type GroupCriteriaResult = z.infer<typeof groupCriteriaResult>;

export type SectionConditionalSchema = Conditional & {
  type: "SECTION_CONDITIONAL";
  result: SectionResult;
};

export type GroupConditionalSchema = Conditional & {
  type: "GROUP_CONDITIONAL";
  result: GroupResult;
};

export interface GlobalSectionConditionalSchema {
  id: string;
  type: "GLOBAL_SECTION_CONDITIONAL";
  result: SectionCriteriaResult;
  selector: CustomFieldSchema | PresetFieldSchema;
}

export interface GlobalGroupConditionalSchema {
  id: string;
  type: "GLOBAL_GROUP_CONDITIONAL";
  result: GroupCriteriaResult;
  selector: CustomFieldSchema | PresetFieldSchema;
}

// prettier-ignore
const sectionConditional: z.ZodType<SectionConditionalSchema> = conditional.extend({
  type: z.literal("SECTION_CONDITIONAL"),
  result: sectionResult
});

const groupConditional: z.ZodType<GroupConditionalSchema> = conditional.extend({
  type: z.literal("GROUP_CONDITIONAL"),
  result: groupResult
});

const group = z.object({
  id: z.string(),
  title: z.string(),
  subtitle: z.string().optional(),
  member: groupMemberSubType.array()
});

export const section = z.object({
  id: z.string(),
  title: z.string(),
  subtitle: z.string().optional(),
  member: sectionMemberSubType.array()
});

// prettier-ignore
const dashboard = z.object({
  applicationStatusNote: z.string().optional(),
  applicationGuidance: z.string().optional()
}).optional();

// prettier-ignore
const contact = z.object({
  title: z.string().optional(),
  faqLink: z.string().optional(),
  email: z.string().optional(),
  hotlineNumber: z.string().optional(),
  locationLink: z.string().optional(),
  helpExtra: z.string().array().optional()
})

const applicationJourney = z.object({
  steps: z
    .object({
      title: z.string(),
      subtitle: z.string().optional(),
      description: z.string()
    })
    .array()
    .min(2),
  ongoingStatusToStep: z.record(z.string(), z.number()),
  action: z
    .record(
      z.string(),
      z.object({
        title: z.string().optional(),
        description: z.string(),
        deadline: z.string().optional(),
        actionLabel: z.string(),
        url: z.string(),
        external: z.boolean().optional()
      })
    )
    .optional(),
  statusToAction: z.record(z.string(), z.string().array().min(1)).optional()
});

const applySchema = z.object({
  actionLabel: z.string().optional(),
  descriptionLabel: z.string().optional(),
  id: z.string(),
  section: section.array()
});

/**
 * Explicitly defining the output and input types for the 'section' schema.
 * This prevents TypeScript from fully inferring complex types inline, which can cause the
 * "The inferred type of this node exceeds the maximum length the compiler will serialize" error (ts7056).
 * By using the runtime type (ISectionRT), we reduce the complexity that TypeScript needs to serialize.
 * For more information: https://lorefnon.me/2023/11/28/fixing-inferred-types-exceeding-serializable-length/
 */

type SectionType = z.ZodType<
  z.output<typeof section>,
  z.ZodTypeDef,
  z.input<typeof section>
>;

const ineligibleReason = z.object({
  title: z.string().optional(),
  description: z.string()
});

const fileUploadExternal = z.object({
  url: z.string()
});

export const application = z.object({
  schemeName: z.string(),
  schemeCode: z.string(),
  bundleSchemeCode: z.string().optional(),
  bundledSchemeCodes: z.string().array().optional(),
  subtitle: z.string(),
  schemeDetailsLink: z.string().optional(),
  dashboard,
  contacts: contact.array().optional(),
  nextSteps: z.string(),
  applicationJourney: applicationJourney.optional(),
  schema: applySchema.array().min(1),
  outstandingDocumentsSchema: z
    .object({ section: (section as SectionType).array() })
    .optional(),
  consentDocumentsSchema: z
    .object({ section: (section as SectionType).array() })
    .optional(),
  consentSchema: z
    .object({ section: (section as SectionType).array() })
    .optional(),
  agencyControlled: z.boolean().optional(),
  ineligibleCodeToReason: z.record(z.string(), ineligibleReason).optional(),
  clearableMyInfoIds: z.string().array().optional(),
  myInfoOptionsIds: z.string().array().optional(),
  fileUploadExternal: fileUploadExternal.optional()
});

function generateCustomFieldSchema(customField: CustomFieldSchema) {
  let validator: z.ZodTypeAny;

  switch (customField.subType) {
    case CustomFieldType.enum.FREE_TEXT:
      validator = customFreeTextValidator(
        customField.maxLength,
        customField.optional
      );
      break;
    case CustomFieldType.enum.TEXT:
      validator = customTextValidator(
        customField.maxLength,
        customField.optional
      );
      break;
    case CustomFieldType.enum.TEXT_NUMBER:
      validator = customTextNumberValidator(
        customField.maxLength,
        customField.optional
      );
      break;
    case CustomFieldType.enum.MULTILINE_TEXT:
      validator = multilineTextValidator(
        customField.maxLength,
        customField.optional
      );
      break;
    case CustomFieldType.enum.HIDDEN:
      validator = hiddenValidator(customField.maxLength, customField.optional);
      break;
    case CustomFieldType.enum.MONEY:
      validator = moneyValidator(
        customField.optional,
        customField.allowNegative
      );
      break;
    case CustomFieldType.enum.DATE:
      validator = customTextDateValidator(customField.optional);
      break;
    case CustomFieldType.enum.DATE_RANGE:
      validator = customDateRangeValidator(customField.optional);
      break;
    case CustomFieldType.enum.DROPDOWN:
      validator = customDropdownValidator(
        customField.options,
        customField.optional
      );
      break;
    case CustomFieldType.enum.DYNAMIC_DROPDOWN:
      if (!customField.options) {
        throw new Error("missing options for dynamic dropdown");
      }
      validator = customDropdownValidator(
        customField.options,
        customField.optional
      );
      break;
    case CustomFieldType.enum.RADIO:
      validator = customRadioValidator(
        customField.options,
        customField.optional
      );
      break;
    case CustomFieldType.enum.SINGLE_CHECK:
      validator = customSingleCheckValidator(customField.optional);
      break;
    case CustomFieldType.enum.CHECKBOX:
      validator = checkBoxValidator(customField.options, customField.optional);
      break;
    case CustomFieldType.enum.DATE_MONTH:
      validator = customTextMonthValidator(
        customField.optional,
        customField.minMonthRange,
        customField.maxMonthRange
      );
      break;
    case CustomFieldType.enum.FILE_UPLOAD:
      validator = customFileUploadValidator(
        customField.optional,
        customField.maxFile
      );
      break;
    case CustomFieldType.enum.SIGNATURE:
      validator = customSignatureValidator(customField.optional);
      break;
    case CustomFieldType.enum.TNC:
      validator = customTermsAndConditionsValidator();
      break;
  }
  return validator;
}

function generatePresetFieldSchema(
  presetField:
    | PresetFieldSchema
    | PresetFieldNricSchema
    | PresetFieldEmailSchema
    | PresetFieldFamilySchema
    | PresetFieldMsfBankRefundSchema
    | PresetFieldMsfPaynowRefundSchema
    | PresetFieldMsfLetterOfUndertakingSchema
    | PresetFieldMsfOmnibusConsentSchema
    | PresetFieldAddressSchema
    | PresetFieldSelectSchema
) {
  let validator: z.ZodTypeAny;

  switch (presetField.subType) {
    case PresetFieldType.enum.block:
      validator = blockValidator(presetField.optional);
      break;
    case PresetFieldType.enum.building:
      validator = buildingValidator(presetField.optional);
      break;
    case PresetFieldType.enum.homeNumber:
      validator = homeNumberValidator(presetField.optional);
      break;
    case PresetFieldType.enum.mobileNumber:
      validator = mobileNumberValidator(presetField.optional);
      break;
    case PresetFieldType.enum.country:
      validator = countryValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.dob:
      validator = dateOfBirthValidator(presetField.optional);
      break;
    case PresetFieldType.enum.email:
      validator = emailValidator(presetField.optional);
      break;
    case PresetFieldType.enum.level:
      validator = levelValidator(presetField.optional);
      break;
    case PresetFieldType.enum.name:
      validator = nameValidator(presetField.optional);
      break;
    case PresetFieldType.enum.nric:
      validator = nricValidator(presetField.optional);
      break;
    case PresetFieldType.enum.street:
      validator = streetValidator(presetField.optional);
      break;
    case PresetFieldType.enum.unit:
      validator = unitValidator(presetField.optional);
      break;
    case PresetFieldType.enum.sex:
      validator = sexValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.addressLine1:
      validator = addressLine1Validator(presetField.optional);
      break;
    case PresetFieldType.enum.addressLine2:
      validator = addressLine2Validator(presetField.optional);
      break;
    case PresetFieldType.enum.maritalStatus:
      validator = maritalStatusValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.nationality:
      validator = nationalityValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.race:
      validator = raceValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.countryOfBirth:
      validator = countryOfBirthValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.residentialStatus:
      validator = residentialStatusValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.address:
      validator = addressValidator(presetField.allowOverseas); //TODO cannot optional?
      break;
    case PresetFieldType.enum.mailingAddress:
      validator = mailingAddressValidator(presetField.allowOverseas);
      break;
    case PresetFieldType.enum.title:
      validator = titleValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.employmentStatus:
      validator = employmentStatusValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.relationshipType:
      validator = relationshipTypeValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.relationshipTypeV5:
      validator = relationshipTypeV5Validator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.school:
      validator = schoolValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.education:
      validator = educationValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.occupation:
      validator = occupationValidator(
        presetField.optional,
        presetField.allowedOptions,
        presetField.excludedOptions
      );
      break;
    case PresetFieldType.enum.child:
      validator = childValidator();
      break;
    case PresetFieldType.enum.family:
      validator = familyValidator();
      break;
    case PresetFieldType.enum.msfBankRefund:
      validator = msfBankRefundValidator();
      break;
    case PresetFieldType.enum.msfPaynowRefund:
      validator = msfPaynowRefundValidator();
      break;
    case PresetFieldType.enum.msfLetterOfUndertaking:
      validator = msfLetterOfUndertakingValidator();
      break;
    case PresetFieldType.enum.msfOmnibusConsent:
      validator = msfOmnibusConsentValidator();
      break;
    case PresetFieldType.enum.consenter:
      validator = consenterValidator();
      break;
    case PresetFieldType.enum.householdMemberConsent:
      validator = householdMemberConsentValidator();
      break;
  }
  return validator;
}

function generateFieldSchema(
  field:
    | CustomFieldSchema
    | PresetFieldSchema
    | PresetFieldNricSchema
    | PresetFieldEmailSchema
    | PresetFieldFamilySchema
    | PresetFieldMsfBankRefundSchema
    | PresetFieldMsfPaynowRefundSchema
    | PresetFieldMsfLetterOfUndertakingSchema
    | PresetFieldMsfOmnibusConsentSchema
    | PresetFieldAddressSchema
    | PresetFieldSelectSchema
) {
  let fieldSchema = z.object({});

  if (field.type === "CUSTOM_FIELD") {
    fieldSchema = fieldSchema.extend({
      [field.id]: generateCustomFieldSchema(field)
    });
  } else if (field.type === "PRESET_FIELD") {
    // Skip validation for MyInfo NRIC as its value is retrieved from user context
    if (!isPrefillSourceMyInfoNric(field.prefillSource)) {
      fieldSchema = fieldSchema.extend({
        [field.id]: generatePresetFieldSchema(field)
      });
    }
  }

  return fieldSchema;
}

export function generateConditionalSchema(
  field: SectionConditionalSchema | GroupConditionalSchema
) {
  const allChoices: string[] = [];
  const choiceValidator = field.result.flatMap((result) => {
    return result.choice.map((choice) => {
      allChoices.push(choice);
      let resultMember = z.object({});

      if (field.type === "SECTION_CONDITIONAL") {
        resultMember = generateSectionMemberSchema(result.member);
      } else if (field.type === "GROUP_CONDITIONAL") {
        resultMember = generateGroupMemberSchema(result.member);
      }

      return z
        .object({
          condition: z.literal(choice)
        })
        .merge(generateFieldSchema(field.selector))
        .merge(resultMember);
    });
  });

  const noAdditionalDataValidator = z
    .object({
      condition: z.literal("") // empty string to represents all conditions that do not need additional data
    })
    .merge(generateFieldSchema(field.selector));

  const conditionalSchema = z.object({
    [field.id]: z
      .object({})
      .merge(generateFieldSchema(field.selector))
      .passthrough()
      .default({}) // so when entire conditional object value is undefined, errors are derived from the selector. e.g. when selector is optional, undefined value shouldn't have error
      // cannot use preprocess here cause there's a bug where error message doesn't generate correctly https://github.com/react-hook-form/resolvers/issues/635
      .transform((val) => {
        const res = {
          ...val,
          // @ts-ignore
          condition: allChoices.includes(val[field.selector.id])
            ? val[field.selector.id]
            : ""
        };

        return res;
      })
      .pipe(
        z
          //@ts-ignore
          .discriminatedUnion("condition", [
            ...choiceValidator,
            noAdditionalDataValidator
          ])
          .transform((val) => {
            const { condition, ...rest } = val;
            return rest;
          })
      )
  });

  return conditionalSchema;
}

export function generateConditionalResultSchema(
  field: GlobalSectionConditionalSchema | GlobalGroupConditionalSchema
) {
  const conditionalSchema = z.object({
    [field.id]: z
      .object({})
      .merge(generateSectionMemberSchema([field.selector]))
      .passthrough()
      .default({}) // so when entire conditional object value is undefined, errors are derived from the selector. e.g. when selector is optional, undefined value shouldn't have error
      .superRefine((val, ctx) => {
        const conditionVal = val[field.selector.id];
        // TODO: Current version of typescript (v4.9.5) cannot smartly deduce the type here. Upgrade to typescript@5.4.5 to remove typecasting to "any".
        const resultMember = (field.result as any).find((result) => {
          return result.choice.some((choice) =>
            evaluateCriteria(choice, { [choice.sourcePath]: conditionVal })
          );
        })?.member;

        if (!resultMember) {
          // no additional validation is needed if selector value does not match any choices
          return;
        }

        const validationSchema =
          field.type === "GLOBAL_SECTION_CONDITIONAL"
            ? generateSectionMemberSchema(resultMember)
            : generateGroupMemberSchema(resultMember);
        const validationResult = validationSchema.safeParse(val);
        if (!validationResult.success) {
          validationResult.error.issues.forEach((issue) => ctx.addIssue(issue));
        }
      })
  });

  return conditionalSchema;
}

export function generateCheckboxConditionalSchema(
  field: SectionConditionalSchema | GroupConditionalSchema
) {
  // create validation schema based on user selected options
  const conditionalSchema = z
    .object({})
    .passthrough() // passthrough used to allow all values as validation schema is not yet determined
    .transform((val, ctx) => {
      let combinedSchema = z
        .object({})
        .merge(generateFieldSchema(field.selector));
      const selectedOptions = val[field.selector.id] as string[] | undefined;

      // merge relevant results to one schema
      if (selectedOptions && selectedOptions.length > 0) {
        field.result.forEach((result) => {
          if (
            result.choice.some((option) => selectedOptions.includes(option))
          ) {
            if (field.type === "SECTION_CONDITIONAL") {
              combinedSchema = combinedSchema.merge(
                generateSectionMemberSchema(result.member)
              );
            } else if (field.type === "GROUP_CONDITIONAL") {
              combinedSchema = combinedSchema.merge(
                generateGroupMemberSchema(result.member)
              );
            }
          }
        });
      }

      const result = combinedSchema.safeParse(val);
      if (!result.success) {
        result.error.issues.forEach((issue) => {
          ctx.addIssue(issue);
        });
        return val; // return original value if validation failed
      } else {
        return result.data; // return data without unused keys when validation passes
      }
    });

  return z.object({ [field.id]: conditionalSchema });
}

export function generateGroupMemberSchema(members: GroupMemberSubType[]) {
  let groupMember = z.object({});

  for (const member of members) {
    if (member.type === "CUSTOM_FIELD" || member.type === "PRESET_FIELD") {
      groupMember = groupMember.merge(generateFieldSchema(member));
    } else if (member.type === "GROUP_CONDITIONAL") {
      // only checkbox custom field supported as there are no checkbox preset fields
      if (member.selector.subType === "CHECKBOX") {
        groupMember = groupMember.merge(
          generateCheckboxConditionalSchema(member)
        );
      } else {
        groupMember = groupMember.merge(generateConditionalSchema(member));
      }
    } else if (member.type === "GLOBAL_GROUP_CONDITIONAL") {
      groupMember = groupMember.merge(generateConditionalResultSchema(member));
    }
  }
  return groupMember;
}

export function generateGroupSchema(group: CustomGroupSubType) {
  let groupSchema = z.object({});

  if (group.subType === "BLANK") {
    groupSchema = groupSchema.extend({
      [group.id]: generateGroupMemberSchema(group.member).default({})
    });
  } else if (group.subType === "FILE_UPLOAD") {
    groupSchema = groupSchema.extend({
      [group.id]: fileUploadValidator(group.optional, group.maxFile)
    });
  } else if (group.subType === "TNC") {
    groupSchema = groupSchema.extend({
      [group.id]: tncValidator()
    });
  }

  return groupSchema;
}

export function generateMultiValueSchema(schema: MultiValueSchema) {
  const multiValueSelectField: CustomFieldSelectSchema = {
    type: "CUSTOM_FIELD",
    subType: "DROPDOWN",
    id: `${schema.group.id}${SELECT_ID_SUFFIX}`,
    title: schema.title,
    options: generateNumberOptions(schema.maxGroup)
  };

  return z.object({
    [schema.id]: z
      .object({
        [schema.group.id]: generateGroupMemberSchema(
          schema.group.member
        ).array()
      })
      .merge(generateFieldSchema(multiValueSelectField))
  });
}

export const generateSectionMemberSchema = (
  members: SectionMemberSubType[]
) => {
  let sectionSchema = z.object({});

  for (const member of members) {
    if (member.type === "CUSTOM_GROUP") {
      sectionSchema = sectionSchema.merge(generateGroupSchema(member));
    } else if (
      member.type === "CUSTOM_FIELD" ||
      member.type === "PRESET_FIELD"
    ) {
      sectionSchema = sectionSchema.merge(generateFieldSchema(member));
    } else if (member.type === "MULTI_VALUE") {
      sectionSchema = sectionSchema.merge(generateMultiValueSchema(member));
    } else if (member.type === "SECTION_CONDITIONAL") {
      // only custom checkbox supported as there are no checkbox preset fields
      if (member.selector.subType === "CHECKBOX") {
        sectionSchema = sectionSchema.merge(
          generateCheckboxConditionalSchema(member)
        );
      } else {
        sectionSchema = sectionSchema.merge(generateConditionalSchema(member));
      }
    } else if (member.type === "GLOBAL_SECTION_CONDITIONAL") {
      sectionSchema = sectionSchema.merge(
        generateConditionalResultSchema(member)
      );
    }
  }

  return sectionSchema;
};

export const generateApplicationSchema = (
  schema: Omit<ApplySchema, "id"> // id is not needed to generate validation schema
) => {
  let sectionSchema = z.object({});

  let tempSchema = z.object({});
  schema.section.forEach((section) => {
    tempSchema = tempSchema.extend({
      [section.id]: generateSectionMemberSchema(section.member)
    });
  });

  sectionSchema = sectionSchema.merge(tempSchema).extend({
    myInfoUsed: z.boolean().optional(),
    prefillFieldIds: z.string().array().optional()
  });

  return sectionSchema;
};

export type CustomFieldSchema = z.infer<typeof customField>;
export type CustomFieldSelectSchema = z.infer<typeof customFieldSelect>;
export type CustomFieldCheckboxSchema = z.infer<typeof customFieldCheckbox>;
export type CustomFieldDynamicDropdownSchema = z.infer<
  typeof customFieldDynamicDropdown
>;
export type CustomFieldSignatureSchema = z.infer<typeof customFieldSignature>;
export type CustomFieldFileUploadSchema = z.infer<typeof customFieldFileUpload>;
export type CustomFieldTermsAndConditionsSchema = z.infer<
  typeof customFieldTermsAndConditions
>;
export type PresetFieldSchema = z.infer<typeof presetField>;
export type PresetFieldNricSchema = z.infer<typeof presetFieldNric>;
export type PresetFieldEmailSchema = z.infer<typeof presetFieldEmail>;
export type PresetFieldFamilySchema = z.infer<typeof presetFieldFamily>;
export type PresetFieldMsfBankRefundSchema = z.infer<
  typeof presetFieldMsfBankRefund
>;
export type PresetFieldMsfPaynowRefundSchema = z.infer<
  typeof presetFieldMsfPaynowRefund
>;
export type PresetFieldMsfLetterOfUndertakingSchema = z.infer<
  typeof presetFieldMsfLetterOfUndertaking
>;
export type SignatureContentSchema = z.infer<typeof signatureContentSchema>;
export type PresetFieldMsfOmnibusConsentSchema = z.infer<
  typeof presetFieldMsfOmnibusConsent
>;
export type PresetFieldAddressSchema = z.infer<typeof presetFieldAddress>;
export type PresetFieldSelectSchema = z.infer<typeof presetFieldSelect>;
export type CustomGroupSchema = z.infer<typeof customGroup>;
export type FileUploadGroupSchema = z.infer<typeof fileUploadGroup>;
export type TncGroupSchema = z.infer<typeof tncGroup>;
export type MultiValueSchema = z.infer<typeof multiValue>;
export type GroupSchema = z.infer<typeof group>;
export type SectionSchema = z.infer<typeof section>;
export type DashboardSchema = z.infer<typeof dashboard>;
export type ContactSchema = z.infer<typeof contact>;
export type ApplicationJourneySchema = z.infer<typeof applicationJourney>;
export type ApplySchema = z.infer<typeof applySchema>;
export type ApplicationSchema = z.infer<typeof application>;

export type DecoratorSubType = z.infer<typeof decoratorSubType>;
export type CustomGroupSubType = z.infer<typeof customGroupSubType>;
export type GroupMemberSubType = z.infer<typeof groupMemberSubType>;
export type SectionMemberSubType = z.infer<typeof sectionMemberSubType>;
export type PointListType = z.infer<typeof pointList>;
export type IneligibleReasonType = z.infer<typeof ineligibleReason>;
export type FileUploadExternal = z.infer<typeof fileUploadExternal>;
export type GlobalAction = z.infer<typeof globalAction>;

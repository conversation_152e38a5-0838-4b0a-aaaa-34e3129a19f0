import { useQueryClient } from "@tanstack/react-query";
import { ReactNode, useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { useHistory } from "react-router-dom";

import Acknowledgement, {
  Scheme
} from "application/components/Acknowledgement";
import { CenteredGridContainer } from "application/components/CenteredGridContainer";
import { Button } from "commons/components/buttons/Button";
import { Text } from "commons/components/Text";
import { sgwWogaa } from "commons/services/wogaa";

import { getAppPath } from "../helper";
import {
  ContentContainer,
  ReviewContainer,
  ReviewWrapper
} from "./GenericSuccess.styles";
import { ApplicationSchema } from "../GenericApplication.helper";
import { ButtonsContainer } from "../sharedStyles";

export interface GenericSuccessProps {
  form: UseFormReturn<any>;
  applicationSchema: ApplicationSchema;
  schemeCode: string;
  schemes: Scheme[];
  ReviewList?: ReactNode;
  email?: string;
  returnPage?: "myApplications" | "homepage";
}

export const GenericSuccess = ({
  form,
  applicationSchema,
  schemeCode,
  schemes,
  ReviewList,
  email,
  returnPage = "myApplications"
}: GenericSuccessProps): JSX.Element => {
  const path = getAppPath(applicationSchema);
  const { replace } = useHistory();
  const queryClient = useQueryClient();

  const backButtonText =
    returnPage === "myApplications" ? "View applications" : "Go to home";
  const backButtonFn =
    returnPage === "myApplications"
      ? () => {
          replace(path.myApplications);
        }
      : () => {
          window.location.replace("/");
        };

  const wogaaTransactionId = schemes.map((scheme) => scheme.refId).join(",");
  const isApplication = !!ReviewList;

  useEffect(() => {
    return () => {
      // second reset is required to clear form values after submission
      form.reset({}); // sets default value to {}
      form.reset();
      queryClient.invalidateQueries(["applicationStatus", schemeCode]);
    };
  }, [form, queryClient, schemeCode]);

  useEffect(() => {
    if (isApplication) {
      sgwWogaa.completeAppTS(wogaaTransactionId);
    }
  }, [wogaaTransactionId, isApplication]);

  return (
    <CenteredGridContainer>
      <ContentContainer>
        {isApplication ? (
          <Acknowledgement
            title="Submitted!"
            schemes={schemes}
            email={email}
            buttonText="Download or print a copy of your submitted application"
            buttonFn={window.print}
          />
        ) : (
          <Acknowledgement title="Submitted!" schemes={schemes} email={email} />
        )}
      </ContentContainer>
      {isApplication && (
        <ReviewContainer className="print-only">
          <Text typeScale="h4">Here’s what we received from you</Text>
          <ReviewWrapper>{ReviewList}</ReviewWrapper>
        </ReviewContainer>
      )}

      <div className="print-hide">
        <ButtonsContainer>
          <Button
            buttonText={backButtonText}
            onClick={backButtonFn}
            buttonScheme="first"
          />
        </ButtonsContainer>
      </div>
    </CenteredGridContainer>
  );
};

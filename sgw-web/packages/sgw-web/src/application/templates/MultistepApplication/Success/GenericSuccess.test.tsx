import { render } from "@testing-library/react";
import { renderHook } from "@testing-library/react-hooks";
import { useForm } from "react-hook-form";
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { sgwWogaa } from "commons/services/wogaa";

import { GenericSuccess, GenericSuccessProps } from "./GenericSuccess";
import { ApplicationSchema } from "application/templates/MultistepApplication/GenericApplication.helper";

const initialProps = {
  form: renderHook(() => useForm<any>()).result.current,
  email: "",
  schemes: [
    {
      name: "Sample Scheme",
      refId: "fake-ref-id"
    }
  ],
  ReviewList: "Review List",
  schemeCode: "mda",
  applicationSchema: {
    schemeName: "Mother's day assistance",
    subtitle: "Supported by Ministry of Social and Family Development",
    schemeCode: "mda",
    schemeDetailsLink: "/schemes/CRG/covid-19-recovery-grant",
    dashboard: {
      applicationStatusNote: "",
      applicationGuidance:
        "If you had recently submitted an application, please do not press “Apply now” as it may delay the processing of your application."
    },
    contacts: [
      {
        faqLink: "https://www.google.com.sg/",
        email: "<EMAIL>",
        hotlineNumber: "1800 222 0000",
        locationLink: "https://goo.gl/maps/RDrj6Cu15j6Png7r7",
        helpExtra: [
          "You may also visit your nearest Social Service Office if you require urgent help"
        ]
      }
    ],
    nextSteps:
      "Your application has been submitted, please wait 2 - 3 working days for it to be processed.",
    schema: [
      {
        id: "main",
        section: [
          {
            title: "Profile",
            subtitle: "This is profile subtitle",
            id: "profile",
            member: [
              {
                id: "details",
                title: "Details",
                type: "CUSTOM_GROUP",
                subType: "BLANK",
                member: [
                  {
                    id: "text",
                    type: "CUSTOM_FIELD",
                    subType: "DATE",
                    title: "text field",
                    optional: true
                  },
                  {
                    type: "DECORATOR",
                    subType: "HEADER",
                    title: "Group level header"
                  },
                  {
                    id: "text-number",
                    type: "CUSTOM_FIELD",
                    subType: "TEXT_NUMBER",
                    title: "number field"
                  }
                ]
              },
              {
                id: "text",
                type: "CUSTOM_FIELD",
                subType: "TEXT",
                title: "section-level text field"
              },
              {
                id: "family",
                title: "Family",
                type: "CUSTOM_GROUP",
                subType: "BLANK",
                member: [
                  {
                    id: "name",
                    type: "PRESET_FIELD",
                    subType: "nric"
                  },
                  {
                    id: "date",
                    type: "PRESET_FIELD",
                    subType: "dob",
                    optional: true
                  },
                  {
                    id: "myaddress",
                    type: "PRESET_FIELD",
                    subType: "address"
                  }
                ]
              }
            ]
          }
        ]
      }
    ],
    applicationJourney: {
      steps: [
        {
          title: "Apply",
          subtitle: "Applied on {{appliedDateTime}}",
          description:
            "Provide details of yourself and your household members.\n&nbsp;\nIf you need help with your application, please refer to this [step-by-step video guide](https://www.youtube.com/watch?v=n2jkwhEyTmU)."
        },
        {
          title: "Assigning an officer to you",
          description: "An officer is being assigned to your application."
        },
        {
          title: "Talk to your officer",
          description:
            "The SSO will call you within 3 working days. Your officer will then follow-up with you to open your case."
        },
        {
          title: "Processing",
          description:
            "Your application is being reviewed. Upon submission of all supporting documents, we will take 4-6 weeks to process your application."
        },
        {
          title: "Check outcome",
          description: "Check your application details."
        }
      ],
      ongoingStatusToStep: {
        "0": 1,
        "1": 2,
        "2": 3,
        "20": 3,
        "21": 3
      },
      action: {
        consentDocs: {
          title: "Consent",
          description:
            "Your application may require additional consent from others.",
          actionLabel: "Get consent",
          url: "/grants/smta/{{refId}}/documents"
        },
        outstandingItems: {
          description: "Upload documents requested by your officer.",
          deadline: "Upload by {{deadline}}",
          actionLabel: "Upload documents",
          url: "/grants/smta/{{refId}}/documents"
        }
      },
      statusToAction: {
        "0": ["consentDocs"],
        "1": ["consentDocs"],
        "20": ["outstandingItems"]
      }
    }
  } as ApplicationSchema
};

describe("Generic Success", () => {
  jest.spyOn(sgwWogaa, "completeAppTS").mockImplementation();

  const renderComponent = (props: GenericSuccessProps) => {
    return render(
      <BrowserRouter>
        <QueryClientProvider client={new QueryClient()}>
          <GenericSuccess {...props} />
        </QueryClientProvider>
      </BrowserRouter>
    );
  };

  it("should render the intial props", () => {
    const { getByText, queryByText } = renderComponent(initialProps);

    expect(getByText(/fake-ref-id/i)).toBeInTheDocument();
    expect(getByText(/Review List/i)).toBeInTheDocument();

    expect(
      queryByText(/An acknowledgement email will be sent shortly to/i)
    ).not.toBeInTheDocument();
  });

  it("should render email acknowledgement when provided with email", () => {
    const { getByText } = renderComponent({
      ...initialProps,
      email: "t****<EMAIL>"
    });

    expect(
      getByText(
        "An acknowledgement email will be sent shortly to t****<EMAIL>."
      )
    ).toBeInTheDocument();
  });

  it("should render next steps instructions when provided with nextSteps", () => {
    const { getByText } = renderComponent({
      ...initialProps,
      schemes: [
        {
          name: "Sample Scheme",
          refId: "fake-ref-id",
          nextSteps: "Next steps for application."
        }
      ]
    });

    expect(getByText(/Next steps for application./i)).toBeInTheDocument();
  });
});

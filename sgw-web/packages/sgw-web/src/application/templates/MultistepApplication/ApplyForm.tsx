import { zodResolver } from "@hookform/resolvers/zod";
import { cloneDeep } from "lodash";
import { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Route, Switch, useLocation } from "react-router-dom";

import { DraftValues } from "application/api/draft";
import { FieldChangeHandler } from "application/hooks/useFieldChangeHandler";
import { getObjectFromPath } from "application/utils";
import { TriggerMap } from "application/utils/formTriggers";

import { GenericSection } from "./Form/GenericForm";
import {
  ApplicationSchema,
  ApplySchema,
  generateApplicationSchema
} from "./GenericApplication.helper";
import { getBaseAppPath, useLoseChangesGuard, usePrefillData } from "./helper";
import { GenericOverview } from "./Overview";
import { GenericReview } from "./Review";

interface ApplicationFormProps {
  applicationSchema: ApplicationSchema;
  schema: ApplySchema;
  triggerMap: TriggerMap;
  draft?: DraftValues;
}

export const ApplyForm = ({
  applicationSchema,
  schema,
  triggerMap,
  draft
}: ApplicationFormProps) => {
  const schemeCode = applicationSchema.schemeCode;
  const basePath = getBaseAppPath(schemeCode);

  const validationSchema = useMemo(() => generateApplicationSchema(schema), [
    schema
  ]);
  const resolver = zodResolver(validationSchema);
  const form = useForm({
    mode: "onTouched",
    resolver,
    defaultValues: draft
  });

  const {
    formState: { isDirty, isSubmitSuccessful },
    reset,
    trigger
  } = form;

  const { hasPrefilled, prefillData, myInfoQuery } = usePrefillData(
    schemeCode,
    schema.id,
    applicationSchema.bundledSchemeCodes,
    !draft
  );

  const [hasInitializedForm, setHasInitializedForm] = useState(false);

  useEffect(() => {
    const initializeForm = async () => {
      if (hasInitializedForm) return;

      let formSections: string[] = [];

      if (draft) {
        formSections = Object.keys(draft);
      } else if (hasPrefilled && prefillData) {
        const prefillValues = getObjectFromPath(prefillData);
        reset(cloneDeep(prefillValues));
        formSections = Object.keys(prefillValues);
      }

      if (formSections.length > 0) {
        // await validation to ensure form state is updated before running dependent actions
        await trigger(formSections);
        triggerMap.formInit.forEach(({ action }) => action(form));
        setHasInitializedForm(true);
      }
    };

    initializeForm();
  }, [draft, prefillData, hasPrefilled, reset, trigger, triggerMap, form]);

  useLoseChangesGuard(!isSubmitSuccessful && isDirty);

  const { pathname } = useLocation();
  const appMainView = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (pathname !== basePath.applyDashboard) {
      appMainView.current?.scrollIntoView();
    }
  }, [pathname, basePath.applyDashboard]);

  return (
    <FieldChangeHandler form={form} triggerMap={triggerMap}>
      <div ref={appMainView}>
        <Switch>
          <Route exact path={basePath.overview}>
            <GenericOverview
              schemaId={schema.id}
              schemeCode={schemeCode}
              applicationSchema={applicationSchema}
              form={form}
              sections={schema.section}
              validationSchema={validationSchema}
            />
          </Route>
          <Route exact path={basePath.review}>
            <GenericReview
              schemaId={schema.id}
              schemeCode={schemeCode}
              applicationSchema={applicationSchema}
              form={form}
              sections={schema.section}
              validationSchema={validationSchema}
              triggerMap={triggerMap}
            />
          </Route>
          <Route path={basePath.section}>
            <GenericSection
              schemeCode={schemeCode}
              applicationSchema={applicationSchema}
              form={form}
              sections={schema.section}
              validationSchema={validationSchema}
              myInfoOptions={myInfoQuery.data?.options}
            />
          </Route>
        </Switch>
      </div>
    </FieldChangeHandler>
  );
};

import { zodResolver } from "@hookform/resolvers/zod";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { set } from "lodash";
import { useEffect, useMemo, useRef } from "react";
import { useForm } from "react-hook-form";
import {
  Redirect,
  Route,
  Switch,
  useLocation,
  useParams
} from "react-router-dom";

import { SgwAppStatus } from "application/api/sgw";
import { ApplicationPage } from "application/composites/ApplicationPage";
import { Spinner } from "commons/components/Spinner";

import { GenericConsent } from "./Consent/GenericConsent";
import {
  ApplicationSchema,
  generateApplicationSchema
} from "./GenericApplication.helper";
import {
  useAppStatusQuery,
  useLoseChangesGuard,
  useAppSchemaQuery,
  usePrefillData,
  useOptionsQuery,
  getAppPath
} from "./helper";
import { GenericOutstandingDocuments } from "./OutstandingDocument";
import { Dashboard } from "./AppDashboard";

interface ApplicationProps {
  refId: string;
  applicationSchema: ApplicationSchema;
  application: SgwAppStatus;
  consentRequired: boolean;
  isServiceError: boolean;
  maintenanceMsg?: string;
  sgwOptions?: Record<string, any>;
}

export const Application = ({
  refId,
  applicationSchema,
  application,
  consentRequired,
  isServiceError,
  maintenanceMsg,
  sgwOptions
}: ApplicationProps) => {
  const schemeCode = applicationSchema.schemeCode;
  const path = getAppPath(applicationSchema, refId);

  const schema = getDocumentsSchema(
    applicationSchema,
    consentRequired,
    application
  );
  const validationSchema = useMemo(() => {
    if (schema) {
      return generateApplicationSchema(schema);
    }
  }, [schema]);
  const form = useForm({
    mode: "onTouched",
    resolver: validationSchema ? zodResolver(validationSchema) : undefined
  });
  const {
    formState: { isDirty }
  } = form;

  useLoseChangesGuard(isDirty);

  const { pathname } = useLocation();
  const appMainView = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (pathname !== path.dashboard) {
      appMainView.current?.scrollIntoView();
    }
  }, [pathname, path.dashboard]);

  return (
    <div ref={appMainView}>
      <Switch>
        <Route exact path={path.dashboard}>
          <Dashboard
            schema={applicationSchema}
            status={application}
            consentRequired={consentRequired}
            isServiceError={isServiceError}
            maintenanceMsg={maintenanceMsg}
          />
        </Route>

        {schema && (
          <Route exact path={path.documents}>
            <GenericOutstandingDocuments
              refId={refId}
              schemeCode={schemeCode}
              applicationSchema={applicationSchema}
              form={form}
              sections={schema.section}
              application={application}
              consentRequired={consentRequired}
              sgwOptions={sgwOptions}
            />
          </Route>
        )}

        <Route>
          <Redirect to={path.notFound} />
        </Route>
      </Switch>
    </div>
  );
};

export const getDocumentsSchema = (
  applicationSchema: ApplicationSchema,
  consentRequired: boolean,
  application: SgwAppStatus
) => {
  if (
    application.status === "Pending Documents" &&
    application.documentsType &&
    applicationSchema.outstandingDocumentsSchema
  ) {
    return processOutstandingDocumentsSchema(
      applicationSchema.outstandingDocumentsSchema,
      application.documentsType
    );
  } else if (consentRequired && applicationSchema.consentDocumentsSchema) {
    return applicationSchema.consentDocumentsSchema;
  }

  return undefined;
};

const processOutstandingDocumentsSchema = (
  outstandingDocumentsSchema: ApplicationSchema["outstandingDocumentsSchema"],
  documentsType: string
) => {
  const documentsTypeList = documentsType.split(",");

  const filteredMember = outstandingDocumentsSchema!.section[0].member.filter(
    (member) => {
      if (
        !(
          member.type === "DECORATOR" ||
          // always display multiline custom field so applicant can submit comments at outstanding doc
          (member.type === "CUSTOM_FIELD" &&
            member.subType === "MULTILINE_TEXT")
        )
      ) {
        return documentsTypeList.includes(member.id);
      }

      return true;
    }
  );

  return set(outstandingDocumentsSchema!, "section[0].member", filteredMember);
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
      refetchOnReconnect: false,
      networkMode: "always"
    },
    mutations: {
      retry: false,
      networkMode: "always"
    }
  }
});

const GenericApplication = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ApplicationGenerator />
    </QueryClientProvider>
  );
};

const ApplicationGenerator = () => {
  const { schemeCode, refId } = useParams<{
    schemeCode: string;
    refId: string;
  }>();

  const { appSchema, isFetchingAppSchema } = useAppSchemaQuery(schemeCode);
  const path = getAppPath(appSchema, refId);

  const { pathname } = useLocation();
  const isConsentPage = pathname.includes("/consent");

  // `bundledSchemeCodes` is for SAF applications with multiple schemes.
  // Not used for post-submission operations, so set to an empty array.
  const bundledSchemeCodes = [];
  const {
    isLoading: isLoadingAppStatus,
    isError: isAppStatusError,
    data: appStatus
  } = useAppStatusQuery(schemeCode, bundledSchemeCodes, !isConsentPage);

  const {
    status: applications = [],
    consentsRequired = [],
    agencyUnavailable = false,
    maintenanceMsg
  } = appStatus || {};

  const applicationByRefId = useMemo(() => {
    if (applications.length > 0) {
      return applications.find((application) => application.refId === refId);
    }

    return undefined;
  }, [applications, refId]);

  // only consent form for ongoing application needs prefill data
  const { hasPrefilled, prefillData, myInfoQuery } = usePrefillData(
    schemeCode,
    "consent",
    bundledSchemeCodes,
    isConsentPage,
    isConsentPage
  );

  const consentRequired =
    consentsRequired.find((item) => item.refId === refId)?.isRequired || false;

  const enableOptionsQuery =
    consentRequired || applicationByRefId?.statusCode === "20";
  const optionsQuery = useOptionsQuery(schemeCode, refId, enableOptionsQuery);

  if (isFetchingAppSchema) {
    return <Spinner />;
  }

  if (!appSchema) {
    return <Redirect to={path.notFound} />;
  }

  return (
    <ApplicationPage
      title={appSchema.schemeName}
      subtitle={appSchema.subtitle}
      buttonText={appSchema.schemeDetailsLink ? "Scheme details" : "Home"}
      onBackButtonClick={() =>
        (window.location.href = appSchema.schemeDetailsLink || "/")
      }
    >
      <Switch>
        <Route exact path={path.consent}>
          {appSchema.consentSchema ? (
            !hasPrefilled ? (
              <Spinner />
            ) : (
              <GenericConsent
                refId={refId}
                schemeCode={schemeCode}
                applicationSchema={appSchema}
                myInfoOptions={myInfoQuery.data?.options}
                prefillData={prefillData}
              />
            )
          ) : (
            <Redirect to={path.notFound} />
          )}
        </Route>
        <Route>
          {isLoadingAppStatus ? (
            <Spinner />
          ) : applicationByRefId ? (
            <Application
              refId={refId}
              application={applicationByRefId}
              applicationSchema={appSchema}
              consentRequired={consentRequired}
              isServiceError={isAppStatusError || agencyUnavailable}
              maintenanceMsg={maintenanceMsg}
              sgwOptions={optionsQuery.data}
            />
          ) : (
            <Redirect to={path.notFound} />
          )}
        </Route>
      </Switch>
    </ApplicationPage>
  );
};

export default GenericApplication;

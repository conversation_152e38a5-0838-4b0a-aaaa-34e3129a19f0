import styled from "styled-components";
import { Layout, MediaQuery, Typography } from "@lifesg/react-design-system";

import { Container } from "commons/components/Container";
import { sp } from "commons/styles";

export const StyledContainer = styled(Container)`
  padding-top: 40px;
  ${MediaQuery.MinWidth.sm} {
    padding-top: 32px;
  }
`;

export const StyledLayout = styled(Layout.Content)`
  margin-top: 24px;
  > div {
    padding: 0;
  }
`;

export const NoSchemesWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${sp._24};
  ${MediaQuery.MinWidth.lg} {
    gap: ${sp._32};
  }
`;

export const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

export const PillContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
`;

export const ButtonWrapper = styled.div`
  display: flex;
  flex-direction: column;
  margin: 40px 0;

  button {
    width: 100%;
  }

  ${MediaQuery.MinWidth.lg} {
    justify-content: flex-start;
    align-items: flex-start;
    margin: 48px 0;

    button {
      width: 260px;
    }
  }
`;

export const NoAvailableSchemesText = styled(Typography.BodyBL)`
  margin-top: 24px;
  text-align: center;

  ${MediaQuery.MinWidth.lg} {
    text-align: left;
  }
`;

export const SelectionCardWrapper = styled(Layout.ColDiv)`
  &:not(:first-child) {
    margin-top: 24px;
  }
`;

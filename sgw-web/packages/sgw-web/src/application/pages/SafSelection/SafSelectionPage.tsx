import {
  Button<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  TextList,
  Toggle,
  Typography
} from "@lifesg/react-design-system";
import { ExternalIcon } from "@lifesg/react-icons";
import {
  QueryClient,
  QueryClientProvider,
  useQuery
} from "@tanstack/react-query";
import { useMemo, useState } from "react";
import { useHistory } from "react-router-dom";

import { getSafAppState } from "application/api/sgw";
import { CenteredGridContainer } from "application/components/CenteredGridContainer";
import { ApplicationPage } from "application/composites/ApplicationPage";
import noActionRequired from "application/images/no-action-required.png";
import {
  buildUrl,
  getBaseAppPath
} from "application/templates/MultistepApplication/helper";
import { ButtonsContainer } from "application/templates/MultistepApplication/sharedStyles";
import { Button } from "commons/components/buttons/Button";
import { Modal } from "commons/components/Modal";
import { Spinner } from "commons/components/Spinner";
import { toastAPIError } from "commons/toast/toast";
import { slug } from "commons/util/slug/slug";
import { path } from "directory/app/router/paths";
import { schemeService } from "directory/app/services/Services";
import { NoResultsListing } from "directory/composites/NoResultsListing";
import { SchemeContent } from "directory/types/Schemes.type";

import { friendlyIdToSchemeCodeMapper } from "./helper";
import {
  ButtonWrapper,
  ContentContainer,
  NoAvailableSchemesText,
  NoSchemesWrapper,
  PillContainer,
  SelectionCardWrapper,
  StyledContainer,
  StyledLayout
} from "./SafSelectionPage.styles";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
      refetchOnReconnect: false,
      networkMode: "always"
    }
  }
});

const SafSelectionPage = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <SafSelectionView />
    </QueryClientProvider>
  );
};

interface SafSchemeContent extends SchemeContent {
  appgenSchemeCode: string;
}

const SafSelectionView = () => {
  const { push } = useHistory();
  const safBasePath = getBaseAppPath("saf");
  const [selectedSchemes, setSelectedSchemes] = useState<string[]>([]);

  // modal state
  const [show, setShow] = useState(false);
  const openModal = () => setShow(true);
  const closeModal = () => setShow(false);

  // Fetch cms scheme listing data
  const { data: schemes, isLoading: isSchemesLoading } = useQuery(
    ["schemes", friendlyIdToSchemeCodeMapper],
    () =>
      schemeService.getSchemes({
        serviceBundle: "SGW",
        schemeCode: Object.keys(friendlyIdToSchemeCodeMapper),
        lang: "en"
      }),
    {
      onError: (error) => toastAPIError(error as any)
    }
  );

  // Fetch SAF app state
  const { data: safSchemes, isLoading: isSafSchemesLoading } = useQuery(
    ["safSchemes"],
    getSafAppState,
    {
      onError: (error) => toastAPIError(error as any)
    }
  );

  // Process and filter schemes when both queries are complete
  const eligibleSchemes = useMemo<SafSchemeContent[]>(() => {
    if (!schemes || !safSchemes) {
      return [];
    }
    return schemes.reduce((acc: SafSchemeContent[], scheme) => {
      const appgenSchemeCode = friendlyIdToSchemeCodeMapper[scheme.schemeCode];
      if (
        appgenSchemeCode &&
        safSchemes.some(
          (safScheme) =>
            safScheme.schemeCode === appgenSchemeCode &&
            safScheme.applicationState === "allow"
        )
      ) {
        acc.push({ ...scheme, appgenSchemeCode });
      }
      return acc;
    }, []);
  }, [schemes, safSchemes]);

  // Function to toggle scheme selection
  const toggleSchemeSelection = (schemeCode: string) => {
    setSelectedSchemes((prevSelected) =>
      prevSelected.includes(schemeCode)
        ? prevSelected.filter((code) => code !== schemeCode)
        : [...prevSelected, schemeCode]
    );
  };

  const isSchemeSelected = (schemeCode: string) => {
    return selectedSchemes.includes(schemeCode);
  };

  const handleApplyNowClick = () => {
    if (selectedSchemes.length === 1) {
      const path = getBaseAppPath(selectedSchemes[0]);
      push(path.applyDashboard);
    } else {
      push(
        buildUrl(safBasePath.applyDashboard, {
          schemes: selectedSchemes
        })
      );
    }
  };

  return (
    <ApplicationPage
      title="Add-on schemes"
      subtitle="Select schemes to apply together"
    >
      {isSchemesLoading || isSafSchemesLoading ? (
        <Spinner />
      ) : eligibleSchemes.length > 0 ? (
        <>
          <StyledContainer>
            <Typography.BodyBL>
              {eligibleSchemes.length} scheme(s) available
            </Typography.BodyBL>
            <StyledLayout type="grid">
              {eligibleSchemes.map((scheme) => (
                <SelectionCardWrapper
                  xxsCols={8}
                  lgCols={8}
                  key={scheme.schemeCode}
                >
                  <SelectionCard
                    scheme={scheme}
                    isSelected={isSchemeSelected(scheme.schemeCode)}
                    onToggle={() =>
                      toggleSchemeSelection(scheme.appgenSchemeCode)
                    }
                  />
                </SelectionCardWrapper>
              ))}
            </StyledLayout>
            {eligibleSchemes.length === 1 && (
              <NoAvailableSchemesText>
                There are no other add-on schemes available for you to apply.
              </NoAvailableSchemesText>
            )}
            <ButtonWrapper>
              <Button
                disabled={selectedSchemes.length < 1}
                onClick={openModal}
                buttonText="Apply now"
              />
            </ButtonWrapper>
          </StyledContainer>
          <Modal
            isOpen={show}
            role="dialog"
            description="Apply for schemes"
            onClose={closeModal}
            title={`Apply for ${selectedSchemes.length} scheme(s)?`}
            primaryButton={{
              variant: "primary",
              text: "Apply",
              onClick: handleApplyNowClick
            }}
            secondaryButton={{
              variant: "secondary",
              text: "Cancel",
              onClick: closeModal
            }}
          >
            <Typography.BodyBL>
              {`You have selected ${selectedSchemes.length} scheme(s) to apply together:`}
            </Typography.BodyBL>
            <TextList.Ul>
              {eligibleSchemes
                .filter((scheme) => isSchemeSelected(scheme.appgenSchemeCode))
                .map((scheme) => (
                  <li key={scheme.schemeCode}>
                    <Typography.BodyBL>{scheme.title_en}</Typography.BodyBL>
                  </li>
                ))}
            </TextList.Ul>
            <br />
            <Typography.BodyBL>
              By proceeding with your application, you are acknowledging that if
              you do not meet the criteria or are unable to provide the required
              details for your application, you may not be able to qualify for
              the selected scheme(s).
            </Typography.BodyBL>
          </Modal>
        </>
      ) : (
        <CenteredGridContainer>
          <NoSchemesWrapper>
            <NoResultsListing
              imageSrc={noActionRequired}
              textColumnMd={12}
              title="No other add-on schemes available for you to apply"
              alt="No other add-on schemes available for you to apply"
            />
            <ButtonsContainer>
              <Button buttonText="Back to Home" onClick={() => push("/")} />
            </ButtonsContainer>
          </NoSchemesWrapper>
        </CenteredGridContainer>
      )}
    </ApplicationPage>
  );
};

export default SafSelectionPage;

interface SelectionCardProps {
  scheme: SafSchemeContent;
  isSelected: boolean;
  onToggle: () => void;
}

const SelectionCard = ({
  scheme,
  isSelected,
  onToggle
}: SelectionCardProps) => {
  return (
    <Toggle
      checked={isSelected}
      onClick={onToggle}
      compositeSection={{
        children: (
          <ContentContainer>
            <Typography.BodyBL>{scheme.content.description}</Typography.BodyBL>
            <PillContainer>
              {scheme.content.keywordsV2.map((keyword) => (
                <Pill key={keyword} colorType="grey" type="outline">
                  {keyword}
                </Pill>
              ))}
            </PillContainer>
            <Typography.LinkBL
              href={path.schemes.schemeCode_slug(
                scheme.schemeCode,
                slug(scheme.title_en)
              )}
              target="_blank"
              rel="noopener noreferrer"
            >
              <ButtonWithIcon.Default
                icon={<ExternalIcon />}
                iconPosition="right"
                styleType="light"
                style={{ width: "100%" }}
              >
                View details
              </ButtonWithIcon.Default>
            </Typography.LinkBL>
          </ContentContainer>
        ),
        collapsible: false
      }}
      indicator
    >
      <Typography.HeadingXS weight="semibold">
        {scheme.title_en}
      </Typography.HeadingXS>
    </Toggle>
  );
};

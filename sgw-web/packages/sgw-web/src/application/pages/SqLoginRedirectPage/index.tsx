import { Spinner } from "commons/components/Spinner";
import { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { env } from "commons/app/config";
import { getSqAuthDetails } from "application/api/sgw";
import { Page } from "commons/composites/Page";
import { WebsiteErrorPage } from "commons/components/WebsiteError";
import { AxiosError } from "axios";

const SqLoginRedirectPage = () => {
  const { schemeCode } = useParams<{ schemeCode: string }>();
  const [errorStatus, setErrorStatus] = useState<number | undefined>();

  const formRef = useRef<HTMLFormElement>(null);
  const tokenRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const startFlow = async () => {
      // Get required auth details
      try {
        const { token, sqServiceId } = await getSqAuthDetails(schemeCode);

        // Set form
        formRef.current!.action = `${env.SQ_DELEGATE_SSO_URL}/${sqServiceId}`;
        tokenRef.current!.value = token;

        // Submit form
        formRef.current!.submit();
      } catch (e) {
        setErrorStatus((e as AxiosError).status);
      }
    };

    startFlow();
  }, [schemeCode]);

  if (errorStatus) {
    return <WebsiteErrorPage error={errorStatus === 404 ? "404" : "500"} />;
  }

  return (
    <Page>
      <Spinner />
      <form ref={formRef} method="post">
        <input ref={tokenRef} type="hidden" name="token" />
      </form>
    </Page>
  );
};

export default SqLoginRedirectPage;

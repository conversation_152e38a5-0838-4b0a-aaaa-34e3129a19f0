import { SgwAppStatus } from "application/api/sgw";
import { StatusCard } from "application/components/StatusCard";
import NoPastApplication from "application/images/no-past-application.png";
import { getBaseAppPath } from "application/templates/MultistepApplication/helper";
import {
  Dropdown,
  DropdownProps,
  OptionType
} from "commons/components/inputFields/Dropdown";
import { Spinner } from "commons/components/Spinner";
import { Text } from "commons/components/Text";
import { cl } from "commons/styles";
import { NoResultsListing } from "directory/composites/NoResultsListing";
import { isEmpty } from "lodash";
import { useHistory } from "react-router-dom";
import { Wrapper } from "./styles";
import { StatusBlock } from "commons/components/StatusBlock";

interface AllApplicationListingsProps {
  isLoading: boolean;
  onFilterChange: (option: OptionType) => void;
  dropdownOptions: DropdownProps["options"];
  allApplications?: SgwAppStatus[];
  selectedFilter: OptionType;
  agencyUnavailable?: boolean;
}

export const AllApplicationListings = ({
  isLoading,
  allApplications,
  onFilterChange,
  dropdownOptions,
  selectedFilter,
  agencyUnavailable
}: AllApplicationListingsProps) => {
  const { push } = useHistory();

  if (isEmpty(allApplications) && !isLoading) {
    return (
      <Wrapper>
        <NoResultsListing
          imageSrc={NoPastApplication}
          title="No applications found"
          alt="No applications found"
        />
      </Wrapper>
    );
  }

  return (
    <Wrapper>
      <Dropdown
        id="filterBy"
        labelText="Filter by"
        options={dropdownOptions}
        value={selectedFilter}
        isDisabled={isLoading}
        isSearchable={false}
        onChange={(option) => onFilterChange(option as OptionType)}
        formatGroupLabel={({ label }) => (
          <Text style={{ color: cl.grey.regular }} typeScale="ol1">
            {label}
          </Text>
        )}
      />
      {isLoading ? (
        <Spinner />
      ) : (
        <>
          {agencyUnavailable && (
            <StatusBlock
              title="Under maintenance"
              Description="We are currently unable to retrieve application details from this scheme provider. The information displayed here may not be complete or the most up-to-date."
              status="warning"
            />
          )}
          <div>
            {allApplications?.map((status, index) => (
              <StatusCard
                key={index}
                status={status}
                schemeName={status.schemeName}
                buttonText="View details"
                onClick={() => {
                  const path = getBaseAppPath(status.schemeCode, status.refId);
                  push(path.dashboard);
                }}
              />
            ))}
          </div>
        </>
      )}
    </Wrapper>
  );
};

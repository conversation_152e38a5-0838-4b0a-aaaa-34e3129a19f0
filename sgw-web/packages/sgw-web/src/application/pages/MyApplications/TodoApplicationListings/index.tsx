import { SgwAppStatus } from "application/api/sgw";
import { SchemeSectionButton } from "application/components/SectionButton";
import NoActionRequired from "application/images/no-action-required.png";
import { getBaseAppPath } from "application/templates/MultistepApplication/helper";
import { Spinner } from "commons/components/Spinner";
import { Text } from "commons/components/Text";
import { NoResultsListing } from "directory/composites/NoResultsListing";
import { isEmpty, partition } from "lodash";
import { useMemo } from "react";
import { useHistory } from "react-router-dom";
import { Container, HeaderWrapper, Wrapper } from "./styles";

interface TodoApplicationListingsProps {
  todoApplications?: SgwAppStatus[];
  isLoading: boolean;
}

export const TodoApplicationListings = ({
  isLoading,
  todoApplications
}: TodoApplicationListingsProps) => {
  const [toApplySchemes, toFollowUpSchemes] = useMemo(
    () => partition(todoApplications, ({ status }) => isEmpty(status)),
    [todoApplications]
  );

  if (isLoading) {
    return <Spinner />;
  }

  return (
    <Wrapper>
      {isEmpty(todoApplications) ? (
        <NoResultsListing
          imageSrc={NoActionRequired}
          title="No applications to follow up"
          alt="No applications to follow up"
        />
      ) : (
        <>
          <TodoSection
            sectionTitle="To apply"
            sectionSubtitle="You have been identified to be eligible for the following scheme(s)."
            applications={toApplySchemes}
          />
          <TodoSection
            sectionTitle="To follow-up"
            applications={toFollowUpSchemes}
          />
        </>
      )}
    </Wrapper>
  );
};

const getApplicationPath = (application: SgwAppStatus) => {
  const path = getBaseAppPath(application.schemeCode, application.refId);

  // navigate to scheme details page for schemes where agency specifically indicates that user is elligible to apply
  if (isEmpty(application.status)) {
    switch (application.schemeCode) {
      case "clpp1":
        return "/schemes/C2OCv9Vu";
      case "clpp2":
        return "/schemes/CLPP2";
      case "clpp3":
        return "/schemes/CLPP3";
      case "clpp4":
        return "/schemes/CLPP4";
    }
  }

  // Handle paths for AppGen schemes based on application status
  if (application.status === "Draft") {
    return path.applyDashboard;
  } else if (application.status === "Consent Required") {
    return path.consent;
  } else {
    return path.dashboard;
  }
};

interface TodoSectionProps {
  sectionTitle: string;
  sectionSubtitle?: string;
  applications: SgwAppStatus[];
}

const TodoSection = ({
  applications,
  sectionTitle,
  sectionSubtitle
}: TodoSectionProps) => {
  const { push } = useHistory();

  if (isEmpty(applications)) return null;

  return (
    <Container>
      <HeaderWrapper>
        <Text typeScale="h4">{sectionTitle}</Text>
        {sectionSubtitle && <Text typeScale="b1">{sectionSubtitle}</Text>}
      </HeaderWrapper>

      {applications.map((application, index) => (
        <SchemeSectionButton
          key={index}
          application={application}
          onClick={() => {
            const path = getApplicationPath(application);
            push(path);
          }}
        />
      ))}
    </Container>
  );
};

import { lazy, Suspense } from "react";
import { Route, Switch, useLocation, Redirect } from "react-router-dom";

import { useSession } from "commons/app/context/session";
import { Spinner } from "commons/components/Spinner";
import { WebsiteErrorPage } from "commons/components/WebsiteError";

// Grant Forms
const GenericApplyApplication = lazy(
  () =>
    import("application/templates/MultistepApplication/GenericApplyApplication")
);
const GenericApplication = lazy(
  () => import("application/templates/MultistepApplication/GenericApplication")
);

const MyApplications = lazy(
  () => import("application/pages/MyApplications/MyApplicationsPage")
);

const SqLoginRedirectPage = lazy(
  () => import("application/pages/SqLoginRedirectPage")
);

const SafSelection = lazy(
  () => import("application/pages/SafSelection/SafSelectionPage")
);

const ApplicationRoutes = (): JSX.Element => {
  const { pathname: currPath } = useLocation();
  const { isLoading, isAuthenticated, login } = useSession();

  if (isLoading) {
    return <Spinner />;
  }

  // this will happen on singpass authentication failures and will be handled by main routes errorboundary
  // TODO need to relook at overall app structure
  if (currPath === "/grants/error") {
    throw new Error("unable to proceed");
  }

  if (!isAuthenticated) {
    login();
    return <></>;
  }

  return (
    <Suspense fallback={<Spinner />}>
      <Switch>
        <Route exact path="/grants/saf/selection" component={SafSelection} />
        <Route
          exact
          path="/grants/:schemeCode/redirect"
          component={SqLoginRedirectPage}
        />
        <Route
          path="/grants/:schemeCode/apply"
          component={GenericApplyApplication}
        />
        <Route
          path="/grants/:schemeCode/:refId"
          component={GenericApplication}
        />
        {/* Redirects from the initial scheme code path to the new application path */}
        <Route
          exact
          path="/grants/:schemeCode"
          render={({ match }) => (
            <Redirect to={`/grants/${match.params.schemeCode}/apply`} />
          )}
        />

        <Route exact path="/my-applications" component={MyApplications} />
        <Route>
          <WebsiteErrorPage error="404" />
        </Route>
      </Switch>
    </Suspense>
  );
};

export default ApplicationRoutes;

import { BackButton } from "commons/components/BackButton";
import { Mast<PERSON> } from "commons/components/Masthead";
import { Text } from "commons/components/Text";
import { Page } from "commons/composites/Page";
import { ReactNode } from "react";

// Keep the shape close to original setting first until we are ready for refactoring
type ApplicationPageProps = {
  title: any;
  subtitle: any;
  buttonText?: string;
  onBackButtonClick?: () => void;
  children: ReactNode;
};

export const ApplicationPage = ({
  title,
  subtitle,
  buttonText,
  onBackButtonClick,
  children
}: ApplicationPageProps) => {
  return (
    <>
      <Page printHeader={false}>
        <Masthead
          mastheadTitle={<Text typeScale="h3">{title}</Text>}
          mastheadSubtitle={<Text typeScale="t2">{subtitle}</Text>}
          BackButtonComponent={
            buttonText && onBackButtonClick ? (
              <BackButton onClick={onBackButtonClick}>{buttonText}</BackButton>
            ) : undefined
          }
        />
        {children}
      </Page>
    </>
  );
};

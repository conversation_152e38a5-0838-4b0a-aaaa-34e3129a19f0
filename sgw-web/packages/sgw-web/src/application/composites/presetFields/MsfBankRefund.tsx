import { useEffect } from "react";
import { UseFormReturn, useWatch } from "react-hook-form";
import { z } from "zod";

import { FileScanStep } from "application/api/fileV2";
import {
  BankBranch as BankBranchValues,
  BankName as BankNameValues
} from "application/enum/bankDetails";
import { isFieldDisabled } from "application/templates/MultistepApplication/helper";
import { usePrevious } from "commons/hooks/usePrevious";

import {
  customSignatureValidator,
  Dropdown,
  dropdownValidator,
  ERROR_CODE,
  InputTextNumber,
  Radio,
  radioValidator,
  recordArrToObj,
  Signature,
  textNumberValidator
} from "../customFields";
import { Header } from "../decorators";
import { ReviewHeader } from "../ReviewHeader";
import { getDisplayValue, ViewOnlyField } from "../ViewOnlyField";
import { Name, nameValidator, Nric, nricValidator } from ".";
import { FieldTitle } from "./constants";
import { presetFieldDefaultValues } from "./helper";
import { SignatureContentSchema } from "application/templates/MultistepApplication/GenericApplication.helper";

const BANK_NAME_TITLE = "Bank name";
const BANK_BRANCH_TITLE = "Bank branch";
const BANK_ACCOUNT_NUMBER_TITLE = "Bank account number";
const BANK_ACCOUNT_NUMBER_DESCRIPTION = "Enter account number without hyphens.";
const JOINT_ALL_ACCOUNT_TITLE = "Is this a joint-all account?";

const DBS_SIGNATURE_TEXTS: SignatureContentSchema = {
  title: "For bank account holder: Bank refund claims",
  content: [
    {
      header: "Acknowledgement of Giro Payment",
      indent: [
        {
          paragraph:
            "I understand that my financial grant, if approved, will be credited into the bank account stated above."
        }
      ]
    },
    {
      header: "Refund of Overpayment",
      indent: [
        {
          paragraph:
            "I authorise my bank to refund any overpayment of the cash grant to the Ministry of Social and Family Development from my bank account as stated above."
        },
        {
          paragraph:
            "This authorisation cannot be withdrawn. The bank is not required to verify the amount or the right of the Social Service Office (SSO)/Family Service Centre (FSC) to any claims submitted."
        },
        {
          paragraph:
            "The bank shall be entitled to treat any electronic signature/thumbprint provided by me as my original signature/thumbprint as though it had been given in hard copy. The bank does not need to further verify my electronic signature/thumbprint and it shall be deemed as valid, accurate and authentic."
        }
      ]
    }
  ],
  acknowledgement: [
    "I acknowledge that I have read the above terms and conditions."
  ],
  additionalDetails: ["Ensure that the signature matches the bank’s records."]
};

const JOINT_DBS_SIGNATURE_TEXTS: SignatureContentSchema = {
  ...DBS_SIGNATURE_TEXTS,
  title: "For the other joint-all account holder: Bank refund claims"
};

// Enhanced other bank copy. Used for paynow nric as well.
export const OTHER_BANK_SIGNATURE_TEXTS: SignatureContentSchema = {
  title: "For bank account holder: Bank refund claims",
  content: [
    {
      header: "Acknowledgement of Giro Payment",
      indent: [
        {
          paragraph:
            "I understand that my financial grant, if approved, will be credited into the bank account stated above."
        }
      ]
    },
    {
      header: "Refund of Overpayment",
      indent: [
        {
          paragraph:
            "I authorise my bank to refund any overpayment of the cash grant to the Ministry of Social and Family Development from my bank account as stated above; or bank account linked to my PayNow (NRIC). This authorisation is in accordance with any communication or instructions which may from time to time be or appear to originate from the Ministry of Social and Family Development. The bank shall not be liable for any losses, damages, expenses, claims or liabilities suffered by me/use as a result of the bank acting upon any such refund claim."
        },
        {
          paragraph:
            "This authorisation cannot be withdrawn. The bank is not required to verify the amount or the right of the Ministry of Social and Family Development to any refund claims submitted or the authority or identity of the person making or purporting to make such a refund claim and regardless of the circumstances prevailing at the time of such refund claim being received by the bank."
        },
        {
          paragraph:
            "The bank shall be entitled to treat any electronic signature/thumbprint provided by me as my original signature/thumbprint as though it had been given in hard copy. The bank does not need to further verify my electronic signature/ thumbprint and it shall be deemed as valid, accurate and authentic. The bank shall be entitled to treat any document it receives, including any refund claim, which contains or purports to contain my electronic signature/thumbprint, as fully authorised and binding upon me. I accept full responsibility for all documents, including any refund claim, received by the bank whether such documents were given by me or purported to be given by me with/without our knowledge or consent."
        },
        {
          paragraph:
            "I agree to indemnify the bank against all losses, damages, expenses, claims or liabilities incurred or sustained by the bank arising out of or in connection with any refund claims made by the Ministry of Social and Family Development or the acting upon or carry out of any steps in connection with such refund claims or my purported revocation of any authorisation in this letter."
        }
      ]
    }
  ],
  acknowledgement: [
    "I acknowledge that the Social Service Office family coach or Family Service Centre case worker had explained the refund claims to me."
  ],
  additionalDetails: ["Ensure that the signature matches the bank’s records."]
};

const JOINT_OTHER_BANK_SIGNATURE_TEXTS: SignatureContentSchema = {
  ...OTHER_BANK_SIGNATURE_TEXTS,
  title: "For the other joint-all account holder: Bank refund claims"
};

const isDbsPosb = (bankCode: string) => {
  return bankCode === "DBS7171" || bankCode === "POSB7171";
};

const isSkipBankBranch = (bankCode: string) => {
  const bankCodesWithoutBranch = [
    "DBS7171", // DBS
    "POSB7171", // POSB
    "7375", // UOB
    "7339", // OCBC
    "9496", // SCB
    "7302" // Malayan Banking Berhad
  ];
  return bankCodesWithoutBranch.includes(bankCode);
};

interface MsfBankRefundProps {
  namespace: string;
  form: UseFormReturn;
  schemeCode: string;
  fullWidth?: boolean;
  prefillSource?: string;
  editable?: boolean;
  step?: FileScanStep;
}

export const MsfBankRefund = ({
  namespace,
  form,
  schemeCode,
  fullWidth,
  prefillSource,
  editable,
  step
}: MsfBankRefundProps) => {
  const jointAllNamespace = `${namespace}.jointAllAccount`;
  const { control, setValue, resetField } = form;
  const prefillFieldIds = form.getValues("prefillFieldIds");

  const [bankCode, isJointAllAccount] = useWatch({
    control,
    name: [`${namespace}.bankCode`, `${jointAllNamespace}.isJointAllAccount`]
  });

  const previousBankCode = usePrevious(bankCode);
  const previousIsJointAllAccount = usePrevious(isJointAllAccount);

  useEffect(() => {
    if (previousBankCode && previousBankCode !== bankCode) {
      resetField(`${namespace}.bankBranch`, {
        keepDirty: true,
        defaultValue: presetFieldDefaultValues.msfBankRefund.bankBranch
      });
      resetField(`${namespace}.bankAccountNumber`, {
        keepDirty: true,
        defaultValue: presetFieldDefaultValues.msfBankRefund.bankAccountNumber
      });

      resetField(`${namespace}.recipientSignature`, {
        keepDirty: true,
        defaultValue: presetFieldDefaultValues.msfBankRefund.recipientSignature
      });
      resetField(jointAllNamespace, {
        keepDirty: true,
        defaultValue: presetFieldDefaultValues.msfBankRefund.jointAllAccount
      });
    }
  }, [namespace, jointAllNamespace, bankCode, previousBankCode, resetField]);

  useEffect(() => {
    if (
      previousIsJointAllAccount &&
      previousIsJointAllAccount !== isJointAllAccount
    ) {
      resetField(`${jointAllNamespace}.name`, {
        keepDirty: true,
        defaultValue:
          presetFieldDefaultValues.msfBankRefund.jointAllAccount.name
      });
      resetField(`${jointAllNamespace}.nric`, {
        keepDirty: true,
        defaultValue:
          presetFieldDefaultValues.msfBankRefund.jointAllAccount.nric
      });
      resetField(`${jointAllNamespace}.jointAllSignature`, {
        keepDirty: true,
        defaultValue:
          presetFieldDefaultValues.msfBankRefund.jointAllAccount
            .jointAllSignature
      });
    }
  }, [
    jointAllNamespace,
    isJointAllAccount,
    previousIsJointAllAccount,
    resetField,
    setValue
  ]);

  return (
    <>
      <Dropdown
        form={form}
        id={`${namespace}.bankCode`}
        title={BANK_NAME_TITLE}
        options={BankNameValues}
        fullWidth={fullWidth}
        disabled={isFieldDisabled(
          `${namespace}.bankCode`,
          prefillSource,
          editable,
          prefillFieldIds
        )}
      />
      {bankCode && (
        <>
          {!isSkipBankBranch(bankCode) && (
            <Dropdown
              form={form}
              id={`${namespace}.bankBranch`}
              title={BANK_BRANCH_TITLE}
              description={[
                "View [branch code](https://www.uob.com.sg/assets/pdfs/global/achcode.pdf)."
              ]}
              options={BankBranchValues[bankCode] || []}
              fullWidth={fullWidth}
              disabled={isFieldDisabled(
                `${namespace}.bankBranch`,
                prefillSource,
                editable,
                prefillFieldIds
              )}
            />
          )}

          <InputTextNumber
            id={`${namespace}.bankAccountNumber`}
            form={form}
            title={BANK_ACCOUNT_NUMBER_TITLE}
            description={[BANK_ACCOUNT_NUMBER_DESCRIPTION]}
            fullWidth={fullWidth}
            disabled={isFieldDisabled(
              `${namespace}.bankAccountNumber`,
              prefillSource,
              editable,
              prefillFieldIds
            )}
          />

          {isDbsPosb(bankCode) ? (
            <Signature
              form={form}
              schemeCode={schemeCode}
              id={`${namespace}.recipientSignature`}
              step={step}
              title={DBS_SIGNATURE_TEXTS.title}
              content={DBS_SIGNATURE_TEXTS.content}
              acknowledgement={DBS_SIGNATURE_TEXTS.acknowledgement}
              additionalDetails={DBS_SIGNATURE_TEXTS.additionalDetails}
            />
          ) : (
            <Signature
              form={form}
              schemeCode={schemeCode}
              id={`${namespace}.recipientSignature`}
              step={step}
              title={OTHER_BANK_SIGNATURE_TEXTS.title}
              content={OTHER_BANK_SIGNATURE_TEXTS.content}
              acknowledgement={OTHER_BANK_SIGNATURE_TEXTS.acknowledgement}
              additionalDetails={OTHER_BANK_SIGNATURE_TEXTS.additionalDetails}
            />
          )}

          <Radio
            form={form}
            id={`${jointAllNamespace}.isJointAllAccount`}
            title={JOINT_ALL_ACCOUNT_TITLE}
            description={[
              "Joint-all accounts require both parties to give their consent before any transaction can be made."
            ]}
            options={["Yes", "No"]}
            fullWidth={fullWidth}
            disabled={isFieldDisabled(
              `${jointAllNamespace}.isJointAllAccount`,
              prefillSource,
              editable,
              prefillFieldIds
            )}
          />

          {isJointAllAccount && isJointAllAccount === "0" && (
            <>
              <Header title="Particulars of the other joint-all account holder" />

              <Name
                form={form}
                id={`${jointAllNamespace}.name`}
                fullWidth={fullWidth}
              />
              <Nric
                form={form}
                id={`${jointAllNamespace}.nric`}
                fullWidth={fullWidth}
              />
              {isDbsPosb(bankCode) ? (
                <Signature
                  form={form}
                  schemeCode={schemeCode}
                  id={`${jointAllNamespace}.jointAllSignature`}
                  step={step}
                  title={JOINT_DBS_SIGNATURE_TEXTS.title}
                  content={JOINT_DBS_SIGNATURE_TEXTS.content}
                  acknowledgement={JOINT_DBS_SIGNATURE_TEXTS.acknowledgement}
                  additionalDetails={
                    JOINT_DBS_SIGNATURE_TEXTS.additionalDetails
                  }
                />
              ) : (
                <Signature
                  form={form}
                  schemeCode={schemeCode}
                  id={`${jointAllNamespace}.jointAllSignature`}
                  step={step}
                  title={JOINT_OTHER_BANK_SIGNATURE_TEXTS.title}
                  content={JOINT_OTHER_BANK_SIGNATURE_TEXTS.content}
                  acknowledgement={
                    JOINT_OTHER_BANK_SIGNATURE_TEXTS.acknowledgement
                  }
                  additionalDetails={
                    JOINT_OTHER_BANK_SIGNATURE_TEXTS.additionalDetails
                  }
                />
              )}
            </>
          )}
        </>
      )}
    </>
  );
};

interface MsfBankRefundViewOnlyProps {
  value: MsfBankRefundSchema;
}

export const MsfBankRefundViewOnly = ({
  value
}: MsfBankRefundViewOnlyProps) => {
  return (
    <>
      <ViewOnlyField
        title={BANK_NAME_TITLE}
        value={getDisplayValue(BankNameValues, value.bankCode)}
      />

      {value.bankBranch && (
        <ViewOnlyField
          title={BANK_BRANCH_TITLE}
          value={getDisplayValue(
            BankBranchValues[value.bankCode],
            value.bankBranch
          )}
        />
      )}

      <ViewOnlyField
        title={BANK_ACCOUNT_NUMBER_TITLE}
        value={value.bankAccountNumber}
      />

      <ViewOnlyField
        type="signature"
        title={
          isDbsPosb(value.bankCode)
            ? DBS_SIGNATURE_TEXTS.title
            : OTHER_BANK_SIGNATURE_TEXTS.title
        }
        value={value.recipientSignature ? [value.recipientSignature] : []}
      />

      <ViewOnlyField
        title={JOINT_ALL_ACCOUNT_TITLE}
        value={value.jointAllAccount.isJointAllAccount === "0" ? "Yes" : "No"}
      />

      {value.jointAllAccount.isJointAllAccount === "0" && (
        <>
          <ReviewHeader type="group">
            Particulars of the other joint-all account holder
          </ReviewHeader>

          <ViewOnlyField
            title={FieldTitle.name}
            value={value.jointAllAccount.name}
          />
          <ViewOnlyField
            title={FieldTitle.nric}
            value={value.jointAllAccount.nric}
          />
          <ViewOnlyField
            type="signature"
            title={
              isDbsPosb(value.bankCode)
                ? JOINT_DBS_SIGNATURE_TEXTS.title
                : JOINT_OTHER_BANK_SIGNATURE_TEXTS.title
            }
            value={
              value.jointAllAccount.jointAllSignature
                ? [value.jointAllAccount.jointAllSignature]
                : []
            }
          />
        </>
      )}
    </>
  );
};

const getBankAccountValidation = (
  bankCode: string
): [(value: string) => boolean, string] => {
  let bankAccountValidator: (value: string) => boolean;
  let errorMessage: string;
  switch (bankCode) {
    case "POSB7171":
    case "7232": // HSBC
      bankAccountValidator = (value) => /^\d{9}$/.test(value);
      errorMessage = `${ERROR_CODE.INVALID_BANK_ACCOUNT_LENGTH}9`;
      break;
    case "DBS7171":
    case "9496": // Standard Chartered
      bankAccountValidator = (value) => /^\d{10}$/.test(value);
      errorMessage = `${ERROR_CODE.INVALID_BANK_ACCOUNT_LENGTH}10`;
      break;
    case "7339": // OCBC
      bankAccountValidator = (value) => /^(\d{10}|\d{12})$/.test(value);
      errorMessage = `${ERROR_CODE.INVALID_BANK_ACCOUNT_LENGTH}10/12`;
      break;
    case "7302": // Malayan banking berhad
      bankAccountValidator = (value) => /^\d{11}$/.test(value);
      errorMessage = `${ERROR_CODE.INVALID_BANK_ACCOUNT_LENGTH}11`;
      break;
    case "7375": // UOB
      bankAccountValidator = (value) =>
        /^(\d{7}|\d{9,14}|\d{17,18})$/.test(value);
      errorMessage = `${ERROR_CODE.INVALID_BANK_ACCOUNT_LENGTH}`;
      break;
    case "8712": // ICBC
      bankAccountValidator = (value) => /^\d{19}$/.test(value);
      errorMessage = `${ERROR_CODE.INVALID_BANK_ACCOUNT_LENGTH}`;
      break;
    default:
      bankAccountValidator = (value) => /^\d{1,19}$/.test(value);
      errorMessage = `${ERROR_CODE.INVALID_BANK_ACCOUNT_LENGTH}`;
  }

  return [bankAccountValidator, errorMessage];
};

export const msfBankRefundValidator = () => {
  const jointAllAccountValidation = z.object({
    jointAllAccount: z
      .object({ isJointAllAccount: radioValidator(["0", "1"]) })
      .passthrough()
      .pipe(
        z.discriminatedUnion("isJointAllAccount", [
          z.object({
            isJointAllAccount: z.literal("0"),
            name: nameValidator(),
            nric: nricValidator(),
            jointAllSignature: customSignatureValidator()
          }),
          z.object({ isJointAllAccount: z.literal("1") })
        ])
      )
  });

  const validations = BankNameValues.map((bankNameOption) => {
    const bankCode = Object.keys(bankNameOption)[0];
    const [validateFn, errorMessage] = getBankAccountValidation(bankCode);
    return z
      .object({
        bankCode: z.literal(bankCode),
        bankAccountNumber: textNumberValidator(256).refine(validateFn, {
          message: errorMessage
        }),
        bankBranch: isSkipBankBranch(bankCode)
          ? z.string().optional()
          : dropdownValidator(recordArrToObj(BankBranchValues[bankCode])),
        recipientSignature: customSignatureValidator()
      })
      .merge(jointAllAccountValidation);
  });

  return z
    .object({ bankCode: dropdownValidator(recordArrToObj(BankNameValues)) })
    .passthrough()
    .pipe(
      z.discriminatedUnion(
        "bankCode",
        // slice is done to conform to discriminated union type requiring >0 items in arr
        [validations[0], ...validations.slice(1)]
      )
    );
};

type MsfBankRefundSchema = z.infer<ReturnType<typeof msfBankRefundValidator>>;

import {
  Dropdown as AppDropdown,
  generateOptions
} from "application/components/Dropdown";
import { appendOptional } from "application/utils";

import {
  CustomFieldProps,
  ERROR_CODE,
  generateCustomOptions,
  Options,
  stringValidator,
  getValidValues
} from "./helper";

interface DropdownProps extends CustomFieldProps {
  options: Options;
}

export const Dropdown = ({
  form,
  id,
  title,
  description,
  disabled,
  options,
  isOptional = false,
  defaultValue,
  fullWidth
}: DropdownProps) => {
  return (
    <AppDropdown
      className="field"
      form={form}
      title={appendOptional(title, isOptional)}
      subtitle={description}
      id={id}
      readOnly={disabled}
      options={
        Array.isArray(options)
          ? generateCustomOptions(options)
          : generateOptions(options)
      }
      defaultValue={defaultValue}
      fullWidth={fullWidth}
    />
  );
};

export const dropdownValidator = (options: Options, isOptional = false) => {
  const validValues = getValidValues(options);

  return stringValidator(256, isOptional).refine(
    (val) => (val ? validValues.includes(val) : true),
    {
      message: ERROR_CODE.INVALID_OPTION
    }
  );
};

export const customDropdownValidator = (
  options: Options,
  isOptional = false
) => {
  const validator = dropdownValidator(options, isOptional);

  return isOptional ? validator.optional() : validator;
};

import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

import { FileScanStep } from "application/api/fileV2";
import { FileUploadCard } from "application/components/FileUploadCard";
import { generatePoints } from "application/composites/customGroups/helper";
import { Markdown } from "application/composites/Markdown";
import { CustomFieldFileUploadSchema } from "application/templates/MultistepApplication/GenericApplication.helper";
import { appendOptional } from "application/utils";

interface FileUploadProps {
  id: string;
  form: UseFormReturn;
  field: CustomFieldFileUploadSchema;
  schemeCode: string;
  step?: FileScanStep;
}

export const FileUpload = ({
  id,
  form,
  field,
  schemeCode,
  step = FileScanStep.APPLICATION
}: FileUploadProps) => {
  return (
    <FileUploadCard
      className="field"
      id={id}
      form={form}
      schemeCode={schemeCode}
      step={step}
      maxFile={field.maxFile || 10}
      maxFileSizeMb={field.maxFileSizeMb || 4}
      attachmentType={id}
      title={appendOptional(field.title, field.optional || false)}
      instructions={
        field.instructions &&
        field.instructions.map((item, index) => (
          <p key={index}>
            <Markdown>{item}</Markdown>
          </p>
        ))
      }
      documents={generatePoints(field.documents)}
      additionalDetails={
        field.additionalDetails &&
        field.additionalDetails.map((detail, index) => (
          <p key={index}>
            <Markdown>{detail}</Markdown>
          </p>
        ))
      }
    />
  );
};

export const customFileUploadValidator = (isOptional = false, maxFile = 10) => {
  const fileValidator = z.object({
    fileName: z
      .string()
      .min(1, {
        message: "File name cannot be empty."
      })
      .max(250, {
        message:
          "File name exceeded 250 characters. Reduce the file name length and try again."
      })
      .regex(/^[a-zA-Z0-9 _.=+-@]*$/, {
        message:
          "File name should only contain letters, numbers, spaces and the following characters: + - = . _ @."
      }),
    attachmentType: z.string(), // not used by appgen but it's here to reflect the value inserted into form
    fileSize: z.number(), // used by draft
    fileId: z.string().optional() // used by external file upload system where file is uploaded directly to
  });

  const fileArrayValidator = z
    .array(fileValidator, {
      required_error: "At least 1 document is required."
    })
    .max(maxFile, {
      message: `Reached limit of ${maxFile} files in this section. Try uploading the file in another section or reducing the number of files.`
    });

  const validator = isOptional
    ? fileArrayValidator.optional()
    : fileArrayValidator.nonempty({
        message: "At least 1 document is required."
      });

  return validator;
};

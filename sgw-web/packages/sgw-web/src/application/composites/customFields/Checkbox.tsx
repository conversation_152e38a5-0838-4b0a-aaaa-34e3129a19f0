import { z } from "zod";

import {
  Checkbox as AppCheckbox,
  generateOptions
} from "application/components/Checkbox";
import { appendOptional } from "application/utils";

import {
  CustomFieldProps,
  ERROR_CODE,
  generateCustomOptions,
  Options,
  getValidValues
} from "./helper";

interface CheckboxGroupProps extends CustomFieldProps {
  options: Options;
  exclusiveOptions?: string[];
}

export const Checkbox = ({
  form,
  id,
  title,
  description,
  disabled,
  options,
  exclusiveOptions,
  isOptional = false,
  fullWidth
}: CheckboxGroupProps) => {
  return (
    <AppCheckbox
      className="field"
      form={form}
      id={id}
      title={appendOptional(title, isOptional)}
      subtitle={description}
      readOnly={disabled}
      options={
        Array.isArray(options)
          ? generateCustomOptions(options)
          : generateOptions(options)
      }
      exclusiveOptions={exclusiveOptions}
      fullWidth={fullWidth}
    />
  );
};

export const checkBoxValidator = (options: Options, isOptional = false) => {
  const stringArrayValidator = z.array(z.string(), {
    required_error: ERROR_CODE.REQUIRED
  });

  const validator = isOptional
    ? stringArrayValidator.optional()
    : stringArrayValidator.nonempty({
        message: ERROR_CODE.REQUIRED
      });

  const validValues = getValidValues(options);

  return validator.refine(
    (vals) => {
      return vals !== undefined
        ? vals.every((val) => validValues.includes(val))
        : true;
    },
    {
      message: ERROR_CODE.INVALID_OPTION
    }
  );
};

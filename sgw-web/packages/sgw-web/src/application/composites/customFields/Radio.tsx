import {
  generateOptions,
  Radio as AppRadio
} from "application/components/Radio";
import { appendOptional } from "application/utils";

import {
  CustomFieldProps,
  ERROR_CODE,
  generateCustomOptions,
  Options,
  stringValidator,
  getValidValues
} from "./helper";

interface RadioProps extends CustomFieldProps {
  options: Options;
  inline?: boolean;
}

export const Radio = ({
  form,
  id,
  title,
  description,
  options,
  disabled,
  inline,
  isOptional = false,
  fullWidth
}: RadioProps) => {
  return (
    <AppRadio
      className="field"
      form={form}
      id={id}
      title={appendOptional(title, isOptional)}
      subtitle={description}
      options={
        Array.isArray(options)
          ? generateCustomOptions(options)
          : generateOptions(options)
      }
      readOnly={disabled}
      inline={inline}
      fullWidth={fullWidth}
    />
  );
};

export const radioValidator = (options: Options, isOptional = false) => {
  const validValues = getValidValues(options);

  return stringValidator(256, isOptional).refine(
    (val) => (val ? validValues.includes(val) : true),
    {
      message: ERROR_CODE.INVALID_OPTION
    }
  );
};

export const customRadioValidator = (options: Options, isOptional = false) => {
  const validator = radioValidator(options, isOptional);

  return isOptional ? validator.optional() : validator;
};

import { UseFormReturn } from "react-hook-form";
import z from "zod";

import { FileScanStep } from "application/api/fileV2";
import { Checkbox } from "application/composites/customFields/Checkbox";
import { Money } from "application/composites/customFields/Money";
import { ViewOnlyField } from "application/composites/ViewOnlyField";
import { CustomFieldSchema } from "application/templates/MultistepApplication/GenericApplication.helper";
import ViewOnlyLabel from "commons/components/ViewOnlyLabel";

import {
  Datepicker,
  DateRangePicker,
  Dropdown,
  Hidden,
  InputFreeText,
  InputText,
  InputTextNumber,
  Monthpicker,
  MultilineText,
  Radio,
  Signature,
  SingleCheck,
  FileUpload,
  TermsAndConditions
} from "./";

export const DEFAULT_MIN_DATE = new Date("1900-01-01");
export const DEFAULT_MAX_DATE = new Date("2099-12-31");

export const CustomFieldType = z.enum([
  "TEXT",
  "FREE_TEXT",
  "TEXT_NUMBER",
  "MULT<PERSON>INE_TEXT",
  "DROPDOWN",
  "RADIO",
  "CHECKBOX",
  "DATE",
  "DATE_MONTH",
  "DATE_RANGE",
  "SINGLE_CHECK",
  "MONEY",
  "DYNAMIC_DROPDOWN",
  "FILE_UPLOAD",
  "HIDDEN",
  "SIGNATURE",
  "TNC"
]);

export type CustomField = z.infer<typeof CustomFieldType>;

export const ERROR_CODE = {
  REQUIRED: "REQUIRED",
  ONLY_NUMERIC: "ONLY_NUMERIC",
  ONLY_ALPHANUMERIC: "ONLY_ALPHANUMERIC",
  INVALID_FREE_TEXT: "INVALID_FREE_TEXT",
  EXCEED_MAX_CHARACTER: "EXCEED_MAX_CHARACTER-", // to be appended with max length e.g. EXCEED_MAX_CHARACTER-256
  EXCEED_MAX_DIGIT: "EXCEED_MAX_DIGIT-", // to be appended with max length e.g. EXCEED_MAX_DIGIT-9
  ENTER_EXACT_DIGIT: "ENTER_EXACT_DIGIT-", // to be appended with exact length e.g. ENTER_EXACT_DIGIT-6
  REQUIRED_ACKNOWLEDGEMENT: "REQUIRED_ACKNOWLEDGEMENT",
  REQUIRED_END_DATE: "REQUIRED_END_DATE",
  INVALID_OPTION: "INVALID_OPTION",
  INVALID_NRIC: "INVALID_NRIC",
  INVALID_MOBILE_NUMBER: "INVALID_MOBILE_NUMBER",
  INVALID_HOME_NUMBER: "INVALID_HOME_NUMBER",
  INVALID_EMAIL: "INVALID_EMAIL",
  INVALID_NAME: "INVALID_NAME",
  INVALID_BANK_ACCOUNT_LENGTH: "INVALID_BANK_ACCOUNT_LENGTH-" // to be appended with exact length e.g. INVALID_BANK_ACCOUNT_LENGTH-9
};

export const stringValidator = (maxLength: number, isOptional: boolean) => {
  const validator = z
    .string({
      required_error: ERROR_CODE.REQUIRED
    })
    .trim()
    .max(maxLength, {
      message: `${ERROR_CODE.EXCEED_MAX_CHARACTER}${maxLength}`
    });

  return isOptional ? validator : noEmptyString(validator);
};

const noEmptyString = (validator: z.ZodString) => {
  return validator.min(1, {
    message: ERROR_CODE.REQUIRED
  });
};

export const customFieldSelector = (
  field: CustomFieldSchema,
  id: string,
  form: UseFormReturn,
  schemeCode: string,
  step?: FileScanStep,
  disabled?: boolean
) => {
  switch (field.subType) {
    case CustomFieldType.enum.TEXT:
      return (
        <InputText
          id={id}
          key={id}
          title={field.title}
          description={field.description}
          placeholder={field.placeholder}
          form={form}
          isOptional={field.optional}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.FREE_TEXT:
      return (
        <InputFreeText
          id={id}
          key={id}
          title={field.title}
          description={field.description}
          placeholder={field.placeholder}
          form={form}
          isOptional={field.optional}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.TEXT_NUMBER:
      return (
        <InputTextNumber
          id={id}
          key={id}
          title={field.title}
          description={field.description}
          placeholder={field.placeholder}
          form={form}
          isOptional={field.optional}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.MULTILINE_TEXT:
      return (
        <MultilineText
          id={id}
          key={id}
          title={field.title}
          description={field.description}
          placeholder={field.placeholder}
          form={form}
          isOptional={field.optional}
          maxLength={field.maxLength}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.MONEY:
      return (
        <Money
          id={id}
          key={id}
          title={field.title}
          description={field.description}
          placeholder={field.placeholder}
          form={form}
          isOptional={field.optional}
          allowNegative={field.allowNegative}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.DATE:
      return (
        <Datepicker
          id={id}
          key={id}
          title={field.title}
          description={field.description}
          form={form}
          isOptional={field.optional}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.DROPDOWN:
    case CustomFieldType.enum.DYNAMIC_DROPDOWN:
      return (
        <Dropdown
          id={id}
          key={id}
          title={field.title}
          description={field.description}
          form={form}
          options={field.options!}
          isOptional={field.optional}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.RADIO:
      return (
        <Radio
          id={id}
          key={id}
          title={field.title}
          description={field.description}
          form={form}
          options={field.options}
          isOptional={field.optional}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.SINGLE_CHECK:
      return (
        <SingleCheck
          id={id}
          key={id}
          title={field.title}
          form={form}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.CHECKBOX:
      return (
        <Checkbox
          id={id}
          key={id}
          title={field.title}
          description={field.description}
          form={form}
          options={field.options}
          exclusiveOptions={field.exclusiveOptions}
          isOptional={field.optional}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.DATE_MONTH:
      return (
        <Monthpicker
          id={id}
          key={id}
          title={field.title}
          description={field.description}
          form={form}
          minMonthRange={field.minMonthRange}
          maxMonthRange={field.maxMonthRange}
          isOptional={field.optional}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.DATE_RANGE:
      return (
        <DateRangePicker
          id={id}
          key={id}
          title={field.title}
          description={field.description}
          form={form}
          isOptional={field.optional}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.FILE_UPLOAD:
      return (
        <FileUpload
          id={id}
          key={id}
          field={field}
          form={form}
          schemeCode={schemeCode}
          step={step}
        />
      );
    case CustomFieldType.enum.HIDDEN:
      return (
        <Hidden
          id={id}
          title="" // title not req but added for type
          key={id}
          form={form}
          disabled={disabled}
        />
      );
    case CustomFieldType.enum.SIGNATURE:
      return (
        <Signature
          id={id}
          key={id}
          title={field.title}
          form={form}
          schemeCode={schemeCode}
          step={step}
          content={field.content}
          acknowledgement={field.acknowledgement}
          additionalDetails={field.additionalDetails}
          isOptional={field.optional}
        />
      );
    case CustomFieldType.enum.TNC:
      return <TermsAndConditions id={id} key={id} form={form} field={field} />;
  }
};

export const customFieldViewOnlySelector = (
  field: CustomFieldSchema,
  id: string,
  form: UseFormReturn
) => {
  switch (field.subType) {
    case CustomFieldType.enum.TEXT:
    case CustomFieldType.enum.FREE_TEXT:
    case CustomFieldType.enum.MULTILINE_TEXT:
    case CustomFieldType.enum.TEXT_NUMBER:
      return (
        <ViewOnlyField
          key={id}
          title={field.title}
          value={form.getValues(id)}
          isOptional={field.optional}
        />
      );

    case CustomFieldType.enum.MONEY:
      return (
        <ViewOnlyField
          key={id}
          type="money"
          title={field.title}
          value={form.getValues(id)}
          isOptional={field.optional}
        />
      );

    case CustomFieldType.enum.DATE:
      return (
        <ViewOnlyField
          key={id}
          type="date"
          title={field.title}
          value={form.getValues(id)}
          isOptional={field.optional}
        />
      );

    case CustomFieldType.enum.DROPDOWN:
    case CustomFieldType.enum.DYNAMIC_DROPDOWN:
    case CustomFieldType.enum.RADIO:
      return (
        <ViewOnlyField
          key={id}
          title={field.title}
          value={getOptionLabel(form.getValues(id), field.options!)}
          isOptional={field.optional}
        />
      );

    case CustomFieldType.enum.SINGLE_CHECK:
      return (
        <ViewOnlyLabel key={id}>
          <SingleCheck id={id} form={form} title={field.title} disabled />
        </ViewOnlyLabel>
      );

    case CustomFieldType.enum.CHECKBOX: {
      const checkboxValues = form.getValues(id) || [];
      return (
        <ViewOnlyField
          key={id}
          type="checkboxGroup"
          title={field.title}
          value={checkboxValues.map((val) =>
            getOptionLabel(val, field.options)
          )}
          isOptional={field.optional}
        />
      );
    }

    case CustomFieldType.enum.DATE_MONTH:
      return (
        <ViewOnlyField
          key={id}
          type="dateMonth"
          title={field.title}
          value={form.getValues(id)}
          isOptional={field.optional}
        />
      );

    case CustomFieldType.enum.DATE_RANGE:
      return (
        <ViewOnlyField
          key={id}
          type="dateRange"
          title={field.title}
          value={form.getValues(id)}
          isOptional={field.optional}
        />
      );

    case CustomFieldType.enum.FILE_UPLOAD:
      return (
        <ViewOnlyField
          key={id}
          type="fileUpload"
          title={field.title}
          value={form.getValues(id)}
          isOptional={field.optional}
        />
      );
    case CustomFieldType.enum.SIGNATURE: {
      const signatureFile = form.getValues(id);
      return (
        <ViewOnlyField
          key={id}
          type="signature"
          title={field.title}
          value={signatureFile ? [signatureFile] : []}
          isOptional={field.optional}
        />
      );
    }
    case CustomFieldType.enum.TNC:
      return (
        <ViewOnlyLabel label={field.title} className="view-only-field">
          <SingleCheck id={id} form={form} title={field.acknowledge} disabled />
        </ViewOnlyLabel>
      );
  }
};

export const customFieldDefaultValues: Record<CustomField, any> = {
  CHECKBOX: [],
  SINGLE_CHECK: false,
  DATE: "",
  DATE_RANGE: ["", ""],
  DROPDOWN: "",
  FREE_TEXT: "",
  MULTILINE_TEXT: "",
  MONEY: "",
  RADIO: "",
  TEXT: "",
  TEXT_NUMBER: "",
  DYNAMIC_DROPDOWN: "",
  DATE_MONTH: "",
  FILE_UPLOAD: [],
  HIDDEN: "",
  SIGNATURE: null,
  TNC: false
};

export interface CustomFieldProps {
  form: UseFormReturn;
  id: string;
  title: string;
  description?: string[];
  disabled?: boolean;
  isOptional?: boolean;
  defaultValue?: any;
  isHide?: boolean;
  fullWidth?: boolean;
}

type CustomOptions = (string | Record<string, string>)[];
export type PresetOptions = {
  [key: string]: string; // format is value: label in ddl
};
export type Options = CustomOptions | PresetOptions;

// used to convert array of records to object for field validation
//TODO: to move into dropdown/radio/checkbox validator once all enums are switched to Record<string, string>[]
export const recordArrToObj = (
  options: Record<string, string>[]
): PresetOptions => {
  return options.reduce((acc, curr) => ({ ...acc, ...curr }), {});
};

// handles array based options
//TODO: to refactor when all enums are switched to Record<string, string>[]
export const generateCustomOptions = (options: CustomOptions) => {
  return options.map((option, index) => {
    if (typeof option === "string") {
      return { value: "" + index, label: option };
    } else {
      const [value, label] = Object.entries(option)[0];
      return { value, label };
    }
  });
};

/**
 * Returns an array of valid option values for selection fields (dropdown, radio, checkbox).
 * Supports array of strings, array of single-key objects, and object formats.
 * Used by validators to determine which values are allowed for a given field.
 */
export const getValidValues = (options: Options): string[] => {
  if (Array.isArray(options)) {
    return options.map((option, index) => {
      if (typeof option === "string") {
        // For array of strings, use index as value
        return String(index);
      }
      // For array of single-key objects, use the key as value
      return Object.keys(option)[0];
    });
  } else if (typeof options === "object" && options !== null) {
    // For object format, use keys as valid values
    return Object.keys(options);
  }
  return [];
};

/**
 * Returns the display label for a given value from a selection field's options.
 *
 * Supports all option formats:
 * - Array of strings: value is the index, label is the string at that index.
 * - Array of single-key objects: value is the key, label is the value of that key.
 * - Object: value is the key, label is the value of that key.
 *
 * @param value - The value to look up (index or key).
 * @param options - The options for the field (array or object).
 * @returns The label string for the given value, or an empty string if not found.
 */
export const getOptionLabel = (value: string, options: Options): string => {
  if (Array.isArray(options)) {
    // Array of strings: value is index
    if (typeof options[0] === "string") {
      const opt = options[value];
      return typeof opt === "string" ? opt : "";
    }

    // Array of single-key objects: value is key
    if (typeof options[0] === "object" && options[0] !== null) {
      const found = options.find(
        (opt) => typeof opt === "object" && Object.keys(opt)[0] === value
      );
      return found ? Object.values(found)[0] || "" : "";
    }
    return "";
  }

  if (typeof options === "object" && options !== null) {
    // Object: value is key
    return options[value] || "";
  }

  return "";
};

import _ from "lodash";
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useMemo,
  useState
} from "react";
import { UseFormReturn } from "react-hook-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { DraftValues, postDraft } from "application/api/draft";
import { FileUploadExternal } from "application/templates/MultistepApplication/GenericApplication.helper";
import { voidFn } from "application/utils";
import { useOverlay } from "commons/app/context/overlay";
import { useToast } from "commons/app/context/toast";

interface Draft {
  content: DraftValues;
  expiredDate: string;
}

interface IFormMetadataContext {
  isLoading: boolean;
  setIsLoading: (isLoading: boolean) => void;
  draft?: Draft;
  setDraft: (draft: Draft | undefined) => void;
  fileUploadExternal?: FileUploadExternal;
}

const FormMetadataContext = createContext<IFormMetadataContext>({
  isLoading: false,
  setIsLoading: voidFn,
  draft: undefined,
  setDraft: voidFn,
  fileUploadExternal: undefined
});

export const useFormMetadata = () => useContext(FormMetadataContext);

interface FormMetadataProviderProps {
  draft?: Draft;
  fileUploadExternal?: FileUploadExternal;
  children: ReactNode;
}

export const FormMetadataProvider = ({
  draft: draftProp,
  fileUploadExternal,
  children
}: FormMetadataProviderProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [draft, setDraft] = useState<Draft | undefined>(draftProp);

  useEffect(() => {
    setDraft(draftProp);
  }, [draftProp]);

  const providerValue = useMemo(
    () => ({
      isLoading,
      setIsLoading,
      draft,
      setDraft,
      fileUploadExternal
    }),
    [draft, isLoading, fileUploadExternal]
  );

  return (
    <FormMetadataContext.Provider value={providerValue}>
      {children}
    </FormMetadataContext.Provider>
  );
};

export const useDraft = (form: UseFormReturn<any>, schemeCode: string) => {
  const formValues = form.getValues();
  const { setIsOverlay } = useOverlay();
  const { setIsLoading, setDraft } = useFormMetadata();
  const { setToast } = useToast();
  const queryClient = useQueryClient();

  const saveDraft = useMutation(() => postDraft(schemeCode, formValues), {
    onMutate: () => {
      setIsOverlay(true);
      setIsLoading(true);
    },
    onSuccess: (data) => {
      const draftData = {
        content: formValues,
        ...data
      };
      setDraft(draftData); // this is used by smta, consider removing this when smta is migrated to appgen
      queryClient.setQueryData(["draft", schemeCode], draftData); // this is used by appgen

      form.reset(_.cloneDeep(formValues), {
        keepErrors: true
      });

      setToast("Draft saved");
    },
    onError: () => {
      setToast("Unable to save draft");
    },
    onSettled: () => {
      setIsOverlay(false);
      setIsLoading(false);
    }
  });

  return saveDraft;
};

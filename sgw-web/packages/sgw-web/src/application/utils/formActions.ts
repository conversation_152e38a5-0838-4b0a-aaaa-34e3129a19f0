import { get } from "lodash";

import { GlobalAction } from "application/templates/MultistepApplication/GenericApplication.helper";
import {
  replaceIndexPlaceholder,
  calculateAgeByDate,
  calculateAgeByYear,
  getDependentValue
} from "./";

/**
 * Generates action functions for global actions based on type.
 *
 * NEW GLOBAL ACTION GUIDE:
 * - To add a new global action type, add a new `case` in the switch statement.
 * - The returned function should accept the React Hook Form context (e.g., { getValues, setValue, unregister }) and perform the desired logic.
 * - If your action logic is complex or reusable, consider extracting it into a helper function (like computeAge).
 *
 * @param type - The global action type (e.g., "copy", "calculateAge")
 * @param fieldId - The target field ID for the action
 * @param options - Additional options for the action
 */
export const actionGenerators = (
  type: string,
  fieldId: string,
  options: Omit<GlobalAction, "type">
) => {
  switch (type) {
    // sample of how to copy
    case "copy":
      return ({ getValues, setValue }) => {
        const value = getDependentValue(getValues, options.dependsOn);
        setValue(fieldId, value);
      };
    case "populateDefaultValue":
      return ({ setValue }) => {
        if (options.defaultValue !== undefined) {
          setValue(fieldId, options.defaultValue);
        }
      };

    case "calculateAge":
      return computeAge(fieldId, options, calculateAgeByDate);

    case "calculateMultiValueAge":
      return computeAgeMulti(fieldId, options, calculateAgeByDate);

    case "calculateAgeByYear":
      return computeAge(fieldId, options, calculateAgeByYear);

    case "calculateMultiValueAgeByYear":
      return computeAgeMulti(fieldId, options, calculateAgeByYear);

    case "checkLegalAge":
      return ({ getValues, setValue, unregister }) => {
        // TODO: update to the use case of the hidden field selector
        const age = getDependentValue(getValues, options.dependsOn);
        const isLegalAge = age >= 21 ? "YES" : "NO";

        const namespace = fieldId.substring(0, fieldId.lastIndexOf("."));
        unregister(namespace);

        setValue(fieldId, isLegalAge);
      };

    default:
      throw new Error(`Unsupported action type: ${type}`);
  }
};

const computeAge = (
  fieldId: string,
  options: Omit<GlobalAction, "type">,
  calculateFn: (dateString: string) => number | null
) => {
  return ({ getValues, setValue }) => {
    const dateValue = getDependentValue(getValues, options.dependsOn);

    if (!dateValue) {
      setValue(fieldId, "");
      return;
    }

    const calculatedAge = calculateFn(dateValue);

    if (calculatedAge !== null) {
      setValue(fieldId, calculatedAge.toString());
    }
  };
};

const computeAgeMulti = (
  fieldId: string,
  options: Omit<GlobalAction, "type">,
  calculateFn: (dateString: string) => number | null
) => {
  return ({ getValues, setValue }) => {
    const dateFieldPath = options.relativeIds?.[0];
    if (!dateFieldPath) return;

    const sourceDetails = getDependentValue(getValues, options.dependsOn);
    if (!sourceDetails || !Array.isArray(sourceDetails)) return;

    sourceDetails.forEach((item, index) => {
      const targetFieldPath = replaceIndexPlaceholder(fieldId, index);
      const dateValue = get(item, dateFieldPath);

      if (!dateValue) {
        setValue(targetFieldPath, "");
        return;
      }
      const calculatedAge = calculateFn(dateValue);

      if (calculatedAge !== null) {
        setValue(targetFieldPath, calculatedAge.toString());
      }
    });
  };
};

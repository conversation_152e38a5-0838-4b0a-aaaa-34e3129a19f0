import { UseFormReturn } from "react-hook-form";

import {
  ApplySchema,
  SectionMemberSubType,
  GroupMemberSubType
} from "application/templates/MultistepApplication/GenericApplication.helper";

import { actionGenerators } from "./formActions";

interface MemberWithGlobalAction {
  fullId: string;
  member: SectionMemberSubType | GroupMemberSubType;
}

/**
 * Finds all schema members that have a globalAction defined.
 *
 * NEW GLOBAL ACTION GUIDE:
 * - If you introduce a new schema item type that can support globalAction,
 *   add a corresponding case to the switch statement in traverse().
 * - Ensure you push the member with the correct fullId and member reference.
 */
export const findMembersWithGlobalAction = (schema: ApplySchema) => {
  const members: MemberWithGlobalAction[] = [];

  // Recursively traverse schema items to collect members with globalAction
  function traverse(namespace: string, items: any[]) {
    for (const item of items) {
      switch (item.type) {
        case "SECTION_CONDITIONAL":
        case "GROUP_CONDITIONAL":
          if (item.globalAction) {
            members.push({ fullId: `${namespace}.${item.id}`, member: item });
          }
          if (item.selector?.globalAction) {
            members.push({
              fullId: `${namespace}.${item.id}.${item.selector.id}`,
              member: item.selector
            });
          }
          item.result.forEach((result: any) =>
            traverse(`${namespace}.${item.id}`, result.member)
          );
          break;

        case "GLOBAL_SECTION_CONDITIONAL":
        case "GLOBAL_GROUP_CONDITIONAL":
          if (item.selector?.globalAction) {
            members.push({
              fullId: `${namespace}.${item.id}.${item.selector.id}`,
              member: item.selector
            });
          }
          break;

        case "CUSTOM_GROUP":
          if (item.globalAction) {
            members.push({ fullId: `${namespace}.${item.id}`, member: item });
          }
          if (item.subType === "BLANK" && item.member) {
            traverse(`${namespace}.${item.id}`, item.member);
          }
          break;

        case "PRESET_FIELD":
        case "CUSTOM_FIELD":
          if (item.globalAction) {
            members.push({ fullId: `${namespace}.${item.id}`, member: item });
          }
          break;

        case "MULTI_VALUE":
          if (item.globalAction) {
            members.push({ fullId: `${namespace}.${item.id}`, member: item });
          }
          if (item.group) {
            if (item.group.globalAction) {
              members.push({
                fullId: `${namespace}.${item.id}.${item.group.id}`,
                member: item.group
              });
            }
            traverse(
              `${namespace}.${item.id}.${item.group.id}[index]`,
              item.group.member
            );
          }
          break;

        case "DECORATOR":
          if (item.globalAction) {
            members.push({ fullId: "", member: item });
          }
          break;
      }
    }
  }

  // Start traversal from the top level
  schema.section.forEach((section) => traverse(section.id, section.member));

  return members;
};

interface TriggerAction {
  fieldId: string;
  action: (form: UseFormReturn) => void;
}

/**
 * TriggerMap holds all global actions that can be triggered at different form events.
 * - formInit: Actions to run when the form is initialized.
 * - formSubmit: Actions to run on form submission.
 * - valueChange: Actions to run when a dependent field value changes.
 */
export interface TriggerMap {
  formInit: TriggerAction[];
  formSubmit: TriggerAction[];
  valueChange: Map<string, TriggerAction[]>;
}

/**
 * Parses the schema to build a TriggerMap of all global actions.
 */
export const parseSchema = (schema) => {
  const triggerMap: TriggerMap = {
    formInit: [],
    formSubmit: [],
    valueChange: new Map() // To store multiple actions that might be triggered for each dependent field
  };

  if (!schema) return triggerMap;

  // get all the member with globalAction, then loop through each member
  const membersWithGlobalAction = findMembersWithGlobalAction(schema);

  for (const { fullId, member } of membersWithGlobalAction) {
    // Skip if the member doesn't have a globalAction
    if (!("globalAction" in member && member.globalAction)) continue;

    const { type, ...options } = member.globalAction;
    const action = actionGenerators(type, fullId, options);

    // Add to form initialization and submission triggers
    triggerMap.formInit.push({ fieldId: fullId, action });
    triggerMap.formSubmit.push({ fieldId: fullId, action });

    // Add to value change map using dependency fields
    options.dependsOn?.forEach((dependField) => {
      const existing = triggerMap.valueChange.get(dependField) || [];
      triggerMap.valueChange.set(dependField, [
        ...existing,
        { fieldId: fullId, action }
      ]);
    });
  }

  return triggerMap;
};

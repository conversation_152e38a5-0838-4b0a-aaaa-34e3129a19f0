import { plainToInstance } from "class-transformer";
import { validateSync } from "class-validator";
import DayJS from "dayjs";
import { set } from "lodash";
import {
  FieldPath,
  FieldValues,
  SetValueConfig,
  UseFormSetValue
} from "react-hook-form";

import { TClass } from "application/validators";
import { Country } from "application/enum";

interface NewValue<T extends FieldValues> {
  id: FieldPath<T>;
  value: any;
}

export const voidFn = (): void => {
  return;
};

export const formatBytes = (bytes: number): string => {
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  const sizes = ["B", "KB", "MB", "GB"];

  return Math.round(bytes / Math.pow(1024, i)) * 1 + " " + sizes[i];
};

// transform items (enum) to DropdownOption for ReactDropdown to read properly
export const getOptionsFromEnum = (
  enumObj: object
): { label: string; value: string }[] =>
  Object.keys(enumObj).map((label: string) => ({
    label,
    value: enumObj[label]
  }));

export const getFilteredOptionsFromEnum = (
  enumObj: object,
  filter: string[]
) => {
  const options = getOptionsFromEnum(enumObj);
  return options.filter(({ value }) => !filter.includes(value));
};

export const getAccordionType = (
  schema: TClass,
  valueObj?: object | null,
  errorObj?: object,
  isAllOptional?: boolean // if input fields in accordion are all optional, no need success type
) => {
  const isCompleted =
    valueObj && validateSync(plainToInstance(schema, valueObj)).length === 0;

  if (isCompleted && !isAllOptional) {
    return "Success";
  }

  if (errorObj && Object.keys(errorObj).length > 0) {
    return "Error";
  }

  return "Default";
};

export const getFileUploadAccordionType = (
  files: Array<any>,
  isError: boolean,
  isOptional?: boolean
) => {
  if (isError) return "Error";
  if (isOptional) return "Default";
  if (files && files.length > 0) return "Success";

  return "Default";
};

export const setValues = <T extends FieldValues>(
  setValue: UseFormSetValue<T>,
  newValues: NewValue<T>[],
  config?: SetValueConfig
) => {
  newValues.forEach(({ id, value }) => {
    setValue(id, value, config);
  });
};

export const setValuesToEmptyString = <T extends FieldValues>(
  setValue: UseFormSetValue<T>,
  ids: FieldPath<T>[],
  config?: SetValueConfig
) => {
  const newValues: NewValue<T>[] = ids.map((id) => ({
    id,
    value: ""
  }));

  setValues(setValue, newValues, config);
};

export const replaceUndefined = (_: any, value: any) =>
  typeof value === "undefined" ? null : value;

export const replaceToNull = (_: any, value: any): any => {
  return typeof value === "undefined" || value === "" ? null : value;
};

export const getKey = (obj: object, value: string | undefined): string => {
  return Object.keys(obj).find((label) => obj[label] === value) || "";
};

export const getValue = (obj: object, key: string): string => {
  return Object.values(obj).find((label) => obj[key] === label) || "";
};

export const formatLocalAddress = (
  country: string,
  postalCode: string,
  street: string,
  block: string,
  level: string | undefined,
  unit: string | undefined,
  building: string | undefined
): string => {
  const unitNumber = level || unit ? `#${level || ""}-${unit || ""}` : "";
  const getCountry = getValue(Country, country);
  const formatted = `${block} ${street} ${unitNumber} ${building || ""}`
    .replace(/\s+/g, " ") // remove any continuous white spaces
    .trim();

  return `${formatted}\n${getCountry} ${postalCode}`;
};

export const formatForeignAddress = (
  country: string,
  line1: string,
  line2: string | undefined
): string => {
  const getCountry = getValue(Country, country);
  return `${getCountry}\n${line1}${line2 ? `\n${line2}` : ""}`;
};

export const formatDate = (date: Date | string, format?: string): string =>
  DayJS(date).format(format ? format : `DD/MM/YYYY`);

export const formatPhone = (code: string, phone: string): string => {
  const concat = `${code} ${phone}`;
  return concat.charAt(0) === "+" ? concat : `+${concat}`;
};

export const formatCurrency = (value: number): string =>
  `S$ ${value
    .toFixed(2)
    .toString()
    .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}`;

export const appendOptional = (title: string, isOptional: boolean) =>
  isOptional ? title + " (optional)" : title;

/**
 * iterate through an object with path as key to return an actual object derived from its path
 * e.g. {"a.b": 1} to {a: {b: 1}}
 */
export const getObjectFromPath = (pathValuePair: { [path: string]: any }) => {
  const res: Record<string, any> = {};
  Object.entries(pathValuePair).forEach(([path, value]) => {
    set(res, path, value);
  });
  return res;
};

export const isLeapYear = (year: number): boolean =>
  year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);

export const calculateAgeByDate = (dateString: string): number | null => {
  const dob = new Date(dateString);
  const today = new Date();

  if (isNaN(dob.getTime())) return null;

  let age = today.getFullYear() - dob.getFullYear();

  // Determine birthday for the current year.
  // If DOB is Feb 29 and this year is not a leap year,
  // consider the birthday to be February 28.
  let birthdayThisYear: Date;
  if (
    dob.getMonth() === 1 &&
    dob.getDate() === 29 &&
    !isLeapYear(today.getFullYear())
  ) {
    birthdayThisYear = new Date(today.getFullYear(), 1, 28);
  } else {
    birthdayThisYear = new Date(
      today.getFullYear(),
      dob.getMonth(),
      dob.getDate()
    );
  }

  // If today is before this year's birthday, subtract 1 from age.
  if (today < birthdayThisYear) {
    age--;
  }

  return age;
};

export const calculateAgeByYear = (dateString: string): number | null => {
  const dob = new Date(dateString);
  const today = new Date();

  if (isNaN(dob.getTime())) return null;

  const age = today.getFullYear() - dob.getFullYear();

  return age;
};

export const replaceIndexPlaceholder = (
  path: string,
  index: number
): string => {
  return path.replace("[index]", `.${index}`);
};

export const getDependentValue = (
  getValues: (fieldPath?: string) => any,
  dependsOn?: string[],
  index = 0
) => {
  if (!Array.isArray(dependsOn) || !dependsOn[index]) return undefined;

  return getValues(dependsOn[index]);
};

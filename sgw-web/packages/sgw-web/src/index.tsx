import "bootstrap-icons/font/bootstrap-icons.css";
import "leaflet/dist/leaflet.css";
import "react-leaflet-markercluster/dist/styles.min.css";
import "reflect-metadata";

import { GlobalStyles } from "GlobalStyles";
import { StrictMode } from "react";
import ReactDOM from "react-dom";
import { BrowserRouter } from "react-router-dom";

import { AlertProvider } from "commons/app/context/alert";
import { AnnouncementProvider } from "commons/app/context/announcement";
import { OverlayProvider } from "commons/app/context/overlay";
import { SessionProvider } from "commons/app/context/session";
import { ToastProvider } from "commons/app/context/toast";
import { TranslationProvider } from "commons/app/context/translation";
import AppRoutes from "commons/app/routes";
import { ViewportProvider } from "commons/hooks/viewport";
import "commons/styles/custom-sgds.scss";
import { ToastifyContainer } from "./commons/toast/toast";
import { env } from "./commons/app/config";
import ReactGA from "react-ga4";
import { ThemeProvider } from "styled-components";
import { SupportGoWhereTheme } from "@lifesg/react-design-system";
env.GOOGLE_ANALYTICS_ID && ReactGA.initialize(env.GOOGLE_ANALYTICS_ID);

declare global {
  interface Window {
    wogaaCustom: any;
    wogaaLayer: any;
  }
}

ReactDOM.render(
  <StrictMode>
    <>
      <GlobalStyles />
      <BrowserRouter>
        <ThemeProvider theme={SupportGoWhereTheme}>
          <ViewportProvider>
            <OverlayProvider>
              <TranslationProvider>
                <AlertProvider>
                  <AnnouncementProvider>
                    <ToastProvider>
                      <SessionProvider>
                        <ToastifyContainer hideProgressBar />
                        <AppRoutes />
                      </SessionProvider>
                    </ToastProvider>
                  </AnnouncementProvider>
                </AlertProvider>
              </TranslationProvider>
            </OverlayProvider>
          </ViewportProvider>
        </ThemeProvider>
      </BrowserRouter>
    </>
  </StrictMode>,
  document.getElementById("root")
);

export const env = {
  API_CMS: import.meta.env.VITE_API_CMS || "",
  LOGIN_URL: import.meta.env.VITE_LOGIN_URL || "/ndi/login",
  LOGOUT_URL: import.meta.env.VITE_LOGOUT_URL || "/ndi/login/logout",
  REFRESH_URL: import.meta.env.VITE_REFRESH_URL || "/ndi/refresh-token",
  MYINFO_USER_URL: import.meta.env.VITE_MYINFO_USER_URL || "/ndi/mi/user",
  MYINFO_PERSON_URL: import.meta.env.VITE_MYINFO_PERSON_URL || "/ndi/mi/person",
  ENABLE_FILE_RESIZE: import.meta.env.VITE_ENABLE_FILE_RESIZE === "true",
  SQ_DELEGATE_SSO_URL: import.meta.env.VITE_SQ_DELEGATE_SSO_URL || "",

  ENABLE_FEATURE_SVC_LISTING: import.meta.env.VITE_ENABLE_FEATURE_SVC_LISTING,
  ENABLE_FEATURE_BUDGET_2022: import.meta.env.VITE_ENABLE_FEATURE_BUDGET_2022,
  ENABLE_FEATURE_SVC_FILTER: import.meta.env.VITE_ENABLE_FEATURE_SVC_FILTER,
  REACT_APP_BC_SCHEMES_CHECK_API_URL:
    import.meta.env.VITE_BC_SCHEMES_CHECK_API_URL ||
    "https://dev.api.supportgowhere.life.gov.sg/v1/sr/bc/benefits/check",
  ENABLE_FEATURE_R2_FEATURE: import.meta.env.VITE_ENABLE_FEATURE_R2_FEATURE,
  ENABLE_FEATURE_MAP_POSTALCODE_FIELD: import.meta.env
    .VITE_ENABLE_FEATURE_MAP_POSTALCODE_FIELD,
  ENABLE_FEATURE_R3: import.meta.env.VITE_ENABLE_FEATURE_R3,
  GOOGLE_ANALYTICS_ID: import.meta.env.VITE_GOOGLE_ANALYTICS_ID,
  ENABLE_GOOGLE_ANALYTICS:
    import.meta.env.VITE_ENABLE_GOOGLE_ANALYTICS === "true",
  ENABLE_POST_BC_FEATURES: import.meta.env.VITE_ENABLE_POST_BC_FEATURES,

  LAST_UPDATED: __LAST_UPDATED__ || "",
  MODE: import.meta.env.MODE || "",
  ENABLE_API_MOCKS: import.meta.env.VITE_ENABLE_API_MOCKS === "true"
};

export const toggle = {
  UNDER_MAINTENANCE: import.meta.env.VITE_UNDER_MAINTENANCE === "true",
  SHOW_SITEWIDE_BANNER: import.meta.env.VITE_SHOW_SITEWIDE_BANNER === "true",
  SHOW_ANNOUNCEMENT_BANNER:
    import.meta.env.VITE_SHOW_ANNOUNCEMENT_BANNER === "true"
};

import styled from "styled-components";

import { Container } from "commons/components/Container";
import { breakpoint, gradient, sp } from "commons/styles";
import { Button } from "../buttons/Button";

interface StyledDivProps {
  background: string;
  extraBottomPadding?:
    | boolean
    | {
        mobile?: string;
        desktop?: string;
      };
}

export const StyledContainer = styled(Container)<StyledDivProps>`
  background: ${(props) => gradient(props.background)};

  padding-bottom: ${({ extraBottomPadding }) =>
    extraBottomPadding
      ? extraBottomPadding === true
        ? sp._48
        : extraBottomPadding.mobile
      : 0};
  ${breakpoint.m} {
    padding-bottom: ${({ extraBottomPadding }) =>
      extraBottomPadding
        ? extraBottomPadding === true
          ? sp._48
          : extraBottomPadding.desktop
        : 0};
  }
`;

export const ContentWrapper = styled.div`
  width: 100%;
  max-width: ${breakpoint.maxContent};
  margin: auto;
  display: flex;
  justify-content: space-between;
`;

export const ButtonWrapper = styled.div`
  padding-top: ${sp._16};
  display: flex;

  ${breakpoint.m} {
    padding-top: ${sp._32};
  }
`;

export const HeaderWrapper = styled.div`
  padding-top: ${sp._24};
  padding-bottom: ${sp._32};

  flex: 1 1 0; /* This helps to fix translations wrapping too early */

  ${breakpoint.m} {
    padding-top: ${sp._48};
    padding-bottom: ${sp._64};
  }

  > * {
    margin-bottom: ${sp._8};

    ${breakpoint.m} {
      margin-bottom: ${sp._16};
    }
  }

  .clamp {
    // If -webkit-line-clamp is not supported, use this single-line fallback
    white-space: nowrap;
    overflow-wrap: break-word;
    text-overflow: ellipsis;
    overflow: hidden;
    // If -webkit-line-clamp is supported, use multi-line ellipsis
    @supports (-webkit-line-clamp: 2) {
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      overflow-wrap: break-word;
    }
  }
`;

export const TitleContainer = styled.div`
  display: flex;
  align-items: start;
  justify-content: space-between;
  gap: ${sp._16};
`;

export const StyledSubtitle = styled.div`
  color: inherit;
  margin-top: ${sp._8};
`;

export const ImageWrapper = styled.div`
  padding-left: ${sp._16};
  display: none;
  ${breakpoint.m} {
    display: flex;
    align-items: flex-end;
  }
`;

/**
 * Container for the share button
 */
export const ShareButtonContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  margin-top: ${sp._24};

  // mobile view button to align left.
  align-items: flex-start;
  ${breakpoint.m} {
    margin-top: 0;
    // tablet/desktop view button to align right.
    align-items: flex-end;
  }
`;

/**
 * Styled Button with modified border radius and padding.
 */
export const StyledButton = styled(Button)`
  width: fit-content;
  border-radius: ${sp._32};

  padding: ${sp._10} ${sp._12};
  ${breakpoint.m} {
    padding: ${sp._12};
  }
`;

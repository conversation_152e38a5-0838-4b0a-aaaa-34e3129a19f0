import { CSSProperties, ReactNode } from "react";
import { Col } from "react-grid-system";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ContentWrapper,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>W<PERSON>per,
  ShareButtonContainer,
  StyledButton,
  StyledContainer,
  StyledSubtitle,
  TitleContainer
} from "commons/components/Masthead/Masthead.styles";
import { useViewport } from "commons/hooks/viewport";
import { gradientCl } from "commons/styles";
import { ReadMore } from "directory/components/ReadMore";
import { Icon } from "../Icon";
import { useI18n } from "commons/hooks/i18n";

export interface MastheadProps {
  mastheadTitle: ReactNode;
  mastheadSubtitle: ReactNode;
  children?: ReactNode;
  background?: string;
  icon?: any;
  style?: CSSProperties;
  className?: string;
  imageStyle?: CSSProperties;
  extraBottomPadding?:
    | boolean
    | {
        mobile?: string;
        desktop?: string;
      };
  showMore?: boolean;
  shareUrl?: string;

  mastheadTopComponent?: React.ReactElement | null;
  CategoriesComponent?: React.ReactElement | null;
  BackButtonComponent?: React.ReactElement | null;
}

const Masthead = ({
  mastheadTitle,
  mastheadSubtitle,
  background = gradientCl.primary.blue.lighter50,
  icon,
  children,
  style,
  className,
  imageStyle,
  extraBottomPadding,
  showMore = false,
  shareUrl,

  mastheadTopComponent,
  CategoriesComponent,
  BackButtonComponent,

  ...rest
}: MastheadProps): JSX.Element => {
  const { mode } = useViewport();
  const { LL } = useI18n();

  const renderShareButton = (): JSX.Element => {
    if (!shareUrl) return <></>;
    return (
      <ShareButtonContainer>
        <StyledButton
          buttonScheme="second"
          itemLeft={<Icon name={"copy-link-blue"} size="m" />}
          buttonText={LL.masthead.share_link()}
          typeScale="bt2"
          onClick={() => navigator.clipboard.writeText(shareUrl)}
          data-tooltip-content={LL.masthead.link_copied()}
          data-tooltip-id="tooltip-feedback"
          data-tooltip-place="bottom"
          aria-describedby="tooltip-feedback"
        />
      </ShareButtonContainer>
    );
  };

  return (
    <StyledContainer
      style={style}
      className={className}
      background={background}
      extraBottomPadding={extraBottomPadding}
      {...rest}
    >
      {mastheadTopComponent}
      {BackButtonComponent && (
        <ButtonWrapper>
          <Col style={{ marginRight: "auto", flex: "0 1 auto", width: "auto" }}>
            {BackButtonComponent}
          </Col>
        </ButtonWrapper>
      )}
      <ContentWrapper>
        <HeaderWrapper>
          {CategoriesComponent}

          <TitleContainer>
            {mastheadTitle}
            {mode === "desktop" && renderShareButton()}
          </TitleContainer>

          {showMore ? (
            <ReadMore data-testid={"mastheadSubtitle"}>
              {mastheadSubtitle}
            </ReadMore>
          ) : (
            <StyledSubtitle data-testid={"mastheadSubtitle"}>
              {mastheadSubtitle}
            </StyledSubtitle>
          )}

          {children}
          {mode === "mobile" && renderShareButton()}
        </HeaderWrapper>
        {icon && (
          <ImageWrapper>
            <Col md={5}>
              <img
                src={icon}
                aria-hidden={true}
                alt="Masthead"
                style={imageStyle}
                data-testid="img-icon"
              />
            </Col>
          </ImageWrapper>
        )}
      </ContentWrapper>
    </StyledContainer>
  );
};

export { Masthead };

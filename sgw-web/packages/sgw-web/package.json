{"name": "@sgw/web", "private": true, "scripts": {"start": "vite", "build": "vite build", "storybook": "storybook dev -p 9001 -c .storybook", "build:storybook": "storybook build -c .storybook -o ./dist/storybook/ ", "typesafe-i18n": "typesafe-i18n", "test": "jest", "lint": "eslint . --ext .ts,.tsx --max-warnings=50 --ignore-pattern **stories.tsx", "compile": "tsc --noEmit && npm run lint", "sitemap": "NODE_PATH=./src npx ts-node --project tsconfig.node.json ./src/directory/scripts/sitemap-builder.ts", "xray-sync": "<PERSON><PERSON>"}, "dependencies": {"@ai-sdk/ui-utils": "^0.0.50", "@chakra-ui/react": "^1.8.9", "@floating-ui/react": "^0.26.16", "@govtechsg/sgds": "2.0.0", "@govtechsg/sgds-react": "2.0.0", "@hookform/resolvers": "^2.8.5", "@lifesg/react-design-system": "^3.0.0-alpha.18", "@lifesg/react-icons": "^1.14.0", "@reach/dialog": "^0.15.0", "@reach/tabs": "^0.16.4", "@react-spring/web": "^9.2.4", "@tanstack/react-query": "^4.36.1", "@tiptap/extension-link": "^2.26.1", "@tiptap/extension-table-cell": "^2.5.5", "@tiptap/extension-table-header": "^2.5.5", "@tiptap/extension-table-row": "^2.5.5", "@tiptap/extension-underline": "^2.5.5", "@tiptap/starter-kit": "^2.5.5", "antd": "^4.24.12", "axios": "^1.11.0", "bootstrap-icons": "^1.9.1", "class-transformer": "^0.5.1", "class-validator": "0.13.2-NexusIQ-patch", "compressorjs": "^1.2.1", "dayjs": "^1.10.4", "leaflet": "^1.7.1", "lodash": "^4.17.21", "mui-tiptap": "^1.9.3", "pluralize": "^8.0.0", "query-string": "^7.0.0", "react": "^17.0.2", "react-accessible-accordion": "^3.3.4", "react-datepicker": "^6.9.0", "react-dom": "^17.0.2", "react-error-boundary": "^3.1.1", "react-ga4": "^1.4.1", "react-grid-system": "^7.3.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.29.0", "react-leaflet": "^3.2.5", "react-leaflet-markercluster": "^3.0.0-rc1", "react-markdown": "^8.0.6", "react-modal": "^3.12.1", "react-number-format": "^4.5.3", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^5.2.0", "react-scroll": "^1.8.2", "react-select": "^5.8.3", "react-share": "^4.4.0", "react-textarea-autosize": "^8.3.2", "react-toastify": "^9.1.1", "react-tooltip": "^5.21.5", "react-use": "^17.5.0", "reflect-metadata": "^0.1.13", "rehype-raw": "^7.0.0", "remark-gfm": "^3.0.1", "sass": "^1.54.5", "signature_pad": "^5.0.1", "slugify": "^1.6.0", "smoothscroll-polyfill": "^0.4.4", "styled-components": "^5.2.2", "typesafe-i18n": "2.35.1", "uuid": "^9.0.0", "zod": "^3.22.3"}, "devDependencies": {"@types/faker": "^5.5.3", "@types/jest": "^26.0.22", "@types/leaflet": "^1.7.9", "@types/lodash": "^4.14.178", "@types/react": "^17.0.3", "@types/react-datepicker": "^7.0.0", "@types/react-dom": "^17.0.3", "@types/react-modal": "^3.12.0", "@types/react-router-dom": "^5.1.7", "@types/react-scroll": "^1.8.2", "@types/styled-components": "^5.1.9", "@vitejs/plugin-react": "^4.0.0", "babel-plugin-styled-components": "^2.1.4", "dotenv": "^8.2.0", "faker": "^5.5.3", "gw-jiray-util": "^4.0.0", "husky": "^4.3.8", "jest-junit": "^14.0.0", "ts-node": "^10.4.0", "type-fest": "^2.17.0", "vite-plugin-svgr": "^3.2.0", "vite-tsconfig-paths": "^4.2.0", "xlsx": "^0.18.5"}, "husky": {"hooks": {"pre-push": "npm run compile && npm run test"}}}